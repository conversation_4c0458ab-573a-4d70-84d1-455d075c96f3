<?php

/**
 * CMS Pro - Simple Entry Point
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple routing
$request_uri = $_SERVER['REQUEST_URI'] ?? '/';
$script_name = $_SERVER['SCRIPT_NAME'] ?? '';
$base_path = dirname($script_name);

// Remove base path from request URI
if ($base_path !== '/') {
    $request_uri = substr($request_uri, strlen($base_path));
}

// Remove query string
$request_uri = strtok($request_uri, '?');

// Basic routing
switch ($request_uri) {
    case '/':
    case '/simple.php':
        echo "<h1>🎉 CMS Pro - Hoş Geldiniz!</h1>";
        echo "<p>Sistem başarıyla kuruldu ve çalışıyor!</p>";
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>📋 Giriş Bilgileri:</h3>";
        echo "<strong>Admin Panel:</strong> <a href='simple.php/admin'>Admin Panel</a><br>";
        echo "<strong>Email:</strong> <EMAIL><br>";
        echo "<strong>Password:</strong> admin123";
        echo "</div>";
        echo "<p><a href='debug.php'>🔧 Debug Bilgileri</a> | <a href='test.php'>🧪 Test Sayfası</a></p>";
        break;
        
    case '/admin':
    case '/admin/':
        // Simple admin login form
        if ($_POST['email'] ?? false) {
            // Handle login
            try {
                $pdo = new PDO('mysql:host=localhost;dbname=cms_pro', 'root', '');
                $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
                $stmt->execute([$_POST['email']]);
                $user = $stmt->fetch();
                
                if ($user && password_verify($_POST['password'], $user['password'])) {
                    session_start();
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
                    
                    echo "<h1>✅ Giriş Başarılı!</h1>";
                    echo "<p>Hoş geldiniz, " . htmlspecialchars($_SESSION['user_name']) . "!</p>";
                    echo "<p><a href='simple.php/dashboard'>Dashboard'a Git</a></p>";
                } else {
                    echo "<h1>❌ Giriş Başarısız!</h1>";
                    echo "<p>Email veya şifre hatalı.</p>";
                    echo "<p><a href='simple.php/admin'>Tekrar Dene</a></p>";
                }
            } catch (Exception $e) {
                echo "<h1>❌ Hata!</h1>";
                echo "<p>" . $e->getMessage() . "</p>";
            }
        } else {
            // Show login form
            echo "<h1>🔐 CMS Pro - Admin Girişi</h1>";
            echo "<form method='POST' style='max-width: 400px; margin: 20px 0;'>";
            echo "<div style='margin-bottom: 15px;'>";
            echo "<label>Email:</label><br>";
            echo "<input type='email' name='email' value='<EMAIL>' required style='width: 100%; padding: 8px; margin-top: 5px;'>";
            echo "</div>";
            echo "<div style='margin-bottom: 15px;'>";
            echo "<label>Şifre:</label><br>";
            echo "<input type='password' name='password' value='admin123' required style='width: 100%; padding: 8px; margin-top: 5px;'>";
            echo "</div>";
            echo "<button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;'>Giriş Yap</button>";
            echo "</form>";
            echo "<p><small>Varsayılan: <EMAIL> / admin123</small></p>";
            echo "<p><a href='simple.php'>← Ana Sayfa</a></p>";
        }
        break;
        
    case '/dashboard':
        session_start();
        if (!isset($_SESSION['user_id'])) {
            header('Location: simple.php/admin');
            exit;
        }
        
        echo "<h1>📊 CMS Pro Dashboard</h1>";
        echo "<p>Hoş geldiniz, " . htmlspecialchars($_SESSION['user_name']) . "!</p>";
        
        try {
            $pdo = new PDO('mysql:host=localhost;dbname=cms_pro', 'root', '');
            
            // Get statistics
            $pages = $pdo->query("SELECT COUNT(*) FROM pages")->fetchColumn();
            $posts = $pdo->query("SELECT COUNT(*) FROM blog_posts")->fetchColumn();
            $users = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
            $categories = $pdo->query("SELECT COUNT(*) FROM categories")->fetchColumn();
            
            echo "<div style='display: flex; gap: 20px; margin: 20px 0; flex-wrap: wrap;'>";
            echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; min-width: 150px;'>";
            echo "<h3>📄 Sayfalar</h3><h2 style='color: #007bff;'>{$pages}</h2>";
            echo "</div>";
            echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; min-width: 150px;'>";
            echo "<h3>📝 Blog Yazıları</h3><h2 style='color: #28a745;'>{$posts}</h2>";
            echo "</div>";
            echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; min-width: 150px;'>";
            echo "<h3>👥 Kullanıcılar</h3><h2 style='color: #ffc107;'>{$users}</h2>";
            echo "</div>";
            echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; min-width: 150px;'>";
            echo "<h3>📂 Kategoriler</h3><h2 style='color: #dc3545;'>{$categories}</h2>";
            echo "</div>";
            echo "</div>";
            
            // Recent content
            echo "<h3>📋 Son İçerikler</h3>";
            
            // Recent pages
            $recentPages = $pdo->query("SELECT title, created_at FROM pages ORDER BY created_at DESC LIMIT 5")->fetchAll();
            if ($recentPages) {
                echo "<h4>Son Sayfalar:</h4><ul>";
                foreach ($recentPages as $page) {
                    echo "<li>" . htmlspecialchars($page['title']) . " <small>(" . $page['created_at'] . ")</small></li>";
                }
                echo "</ul>";
            }
            
            // Recent posts
            $recentPosts = $pdo->query("SELECT title, created_at FROM blog_posts ORDER BY created_at DESC LIMIT 5")->fetchAll();
            if ($recentPosts) {
                echo "<h4>Son Blog Yazıları:</h4><ul>";
                foreach ($recentPosts as $post) {
                    echo "<li>" . htmlspecialchars($post['title']) . " <small>(" . $post['created_at'] . ")</small></li>";
                }
                echo "</ul>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>Hata: " . $e->getMessage() . "</p>";
        }
        
        echo "<hr>";
        echo "<p><a href='simple.php/logout'>Çıkış Yap</a> | <a href='simple.php'>Ana Sayfa</a></p>";
        break;
        
    case '/logout':
        session_start();
        session_destroy();
        echo "<h1>👋 Çıkış Yapıldı</h1>";
        echo "<p>Başarıyla çıkış yaptınız.</p>";
        echo "<p><a href='simple.php/admin'>Tekrar Giriş Yap</a> | <a href='simple.php'>Ana Sayfa</a></p>";
        break;
        
    default:
        http_response_code(404);
        echo "<h1>404 - Sayfa Bulunamadı</h1>";
        echo "<p>Aradığınız sayfa bulunamadı: <code>" . htmlspecialchars($request_uri) . "</code></p>";
        echo "<p><a href='simple.php'>← Ana Sayfa</a></p>";
        break;
}

// Add some basic styling
echo "<style>
body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 40px; line-height: 1.6; }
h1 { color: #333; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
</style>";
?>
