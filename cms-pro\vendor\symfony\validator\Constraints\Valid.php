<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Validator\Constraints;

use Symfony\Component\Validator\Constraint;

/**
 * @Annotation
 * @Target({"PROPERTY", "METHOD", "ANNOTATION"})
 *
 * <AUTHOR> <bsch<PERSON><PERSON>@gmail.com>
 */
class Valid extends Constraint
{
    public $traverse = true;

    public function __get($option)
    {
        if ('groups' === $option) {
            // when this is reached, no groups have been configured
            return null;
        }

        return parent::__get($option);
    }

    /**
     * {@inheritdoc}
     */
    public function addImplicitGroupName($group)
    {
        if (null !== $this->groups) {
            parent::addImplicitGroupName($group);
        }
    }
}
