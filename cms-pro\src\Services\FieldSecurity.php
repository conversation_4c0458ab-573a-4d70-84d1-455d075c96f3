<?php

namespace CmsPro\Services;

use CmsPro\Core\Database;

/**
 * Field Security Service
 * 
 * @package CmsPro\Services
 */
class FieldSecurity
{
    private $db;
    private $sanitizer;
    private $securityRules = [];

    public function __construct(Database $db = null, FieldSanitizer $sanitizer = null)
    {
        $this->db = $db ?: app()->getDatabase();
        $this->sanitizer = $sanitizer ?: new FieldSanitizer();
        $this->initializeSecurityRules();
    }

    /**
     * Secure field value
     */
    public function secureValue($fieldType, $value, $context = [])
    {
        // First sanitize the value
        $sanitizedValue = $this->sanitizer->sanitize($fieldType, $value);

        // Then apply security rules
        $securedValue = $this->applySecurityRules($fieldType, $sanitizedValue, $context);

        // Log security events if needed
        $this->logSecurityEvent($fieldType, $value, $securedValue, $context);

        return $securedValue;
    }

    /**
     * Check for malicious content
     */
    public function detectMaliciousContent($value)
    {
        $threats = [];

        // Check for XSS attempts
        if ($this->detectXss($value)) {
            $threats[] = 'xss';
        }

        // Check for SQL injection attempts
        if ($this->detectSqlInjection($value)) {
            $threats[] = 'sql_injection';
        }

        // Check for script injection
        if ($this->detectScriptInjection($value)) {
            $threats[] = 'script_injection';
        }

        // Check for path traversal
        if ($this->detectPathTraversal($value)) {
            $threats[] = 'path_traversal';
        }

        // Check for command injection
        if ($this->detectCommandInjection($value)) {
            $threats[] = 'command_injection';
        }

        // Check for LDAP injection
        if ($this->detectLdapInjection($value)) {
            $threats[] = 'ldap_injection';
        }

        return $threats;
    }

    /**
     * Validate file upload security
     */
    public function validateFileUpload($filePath, $originalName, $mimeType)
    {
        $issues = [];

        // Check file extension
        $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
        if ($this->isDangerousExtension($extension)) {
            $issues[] = 'dangerous_extension';
        }

        // Check MIME type
        if ($this->isDangerousMimeType($mimeType)) {
            $issues[] = 'dangerous_mime_type';
        }

        // Check file content
        if (file_exists($filePath)) {
            $content = file_get_contents($filePath, false, null, 0, 1024); // Read first 1KB
            if ($this->containsMaliciousCode($content)) {
                $issues[] = 'malicious_content';
            }
        }

        // Check file size
        if (file_exists($filePath) && filesize($filePath) > $this->getMaxFileSize()) {
            $issues[] = 'file_too_large';
        }

        return $issues;
    }

    /**
     * Encrypt sensitive field value
     */
    public function encryptSensitiveValue($value, $fieldType)
    {
        if ($this->isSensitiveField($fieldType)) {
            return $this->encrypt($value);
        }

        return $value;
    }

    /**
     * Decrypt sensitive field value
     */
    public function decryptSensitiveValue($value, $fieldType)
    {
        if ($this->isSensitiveField($fieldType) && $this->isEncrypted($value)) {
            return $this->decrypt($value);
        }

        return $value;
    }

    /**
     * Apply rate limiting for field operations
     */
    public function checkRateLimit($operation, $identifier, $limit = 100, $window = 3600)
    {
        $key = "field_rate_limit:{$operation}:{$identifier}";
        $current = $this->getCacheValue($key, 0);

        if ($current >= $limit) {
            return false;
        }

        $this->setCacheValue($key, $current + 1, $window);
        return true;
    }

    /**
     * Generate security token for field operations
     */
    public function generateSecurityToken($fieldId, $operation, $expiry = 3600)
    {
        $data = [
            'field_id' => $fieldId,
            'operation' => $operation,
            'user_id' => auth()->id(),
            'expires_at' => time() + $expiry,
            'nonce' => bin2hex(random_bytes(16))
        ];

        return base64_encode(json_encode($data));
    }

    /**
     * Validate security token
     */
    public function validateSecurityToken($token, $fieldId, $operation)
    {
        try {
            $data = json_decode(base64_decode($token), true);

            if (!$data || !is_array($data)) {
                return false;
            }

            // Check expiry
            if ($data['expires_at'] < time()) {
                return false;
            }

            // Check field ID
            if ($data['field_id'] != $fieldId) {
                return false;
            }

            // Check operation
            if ($data['operation'] !== $operation) {
                return false;
            }

            // Check user
            if ($data['user_id'] != auth()->id()) {
                return false;
            }

            return true;

        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Apply security rules
     */
    private function applySecurityRules($fieldType, $value, $context)
    {
        $rules = $this->securityRules[$fieldType] ?? $this->securityRules['default'];

        foreach ($rules as $rule) {
            $value = $this->applySecurityRule($rule, $value, $context);
        }

        return $value;
    }

    /**
     * Apply individual security rule
     */
    private function applySecurityRule($rule, $value, $context)
    {
        switch ($rule['type']) {
            case 'length_limit':
                return mb_substr($value, 0, $rule['max_length']);

            case 'character_filter':
                return preg_replace($rule['pattern'], '', $value);

            case 'encoding_normalize':
                return mb_convert_encoding($value, 'UTF-8', 'UTF-8');

            case 'null_byte_removal':
                return str_replace("\0", '', $value);

            case 'control_char_removal':
                return preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $value);

            default:
                return $value;
        }
    }

    /**
     * Detect XSS attempts
     */
    private function detectXss($value)
    {
        $xssPatterns = [
            '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi',
            '/javascript:/i',
            '/vbscript:/i',
            '/on\w+\s*=/i',
            '/<iframe/i',
            '/<object/i',
            '/<embed/i',
            '/<applet/i',
            '/expression\s*\(/i',
            '/url\s*\(/i',
            '/@import/i'
        ];

        foreach ($xssPatterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Detect SQL injection attempts
     */
    private function detectSqlInjection($value)
    {
        $sqlPatterns = [
            '/(\bUNION\b|\bSELECT\b|\bINSERT\b|\bUPDATE\b|\bDELETE\b|\bDROP\b|\bCREATE\b|\bALTER\b)/i',
            '/(\bOR\b|\bAND\b)\s+\d+\s*=\s*\d+/i',
            '/\'\s*(OR|AND)\s*\'/i',
            '/;\s*(DROP|DELETE|UPDATE|INSERT)/i',
            '/\/\*.*\*\//s',
            '/--.*$/m',
            '/\bxp_cmdshell\b/i',
            '/\bsp_executesql\b/i'
        ];

        foreach ($sqlPatterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Detect script injection
     */
    private function detectScriptInjection($value)
    {
        $scriptPatterns = [
            '/\$\{.*\}/s',
            '/<%.*%>/s',
            '/<\?.*\?>/s',
            '/\{\{.*\}\}/s',
            '/eval\s*\(/i',
            '/exec\s*\(/i',
            '/system\s*\(/i',
            '/shell_exec\s*\(/i',
            '/passthru\s*\(/i',
            '/file_get_contents\s*\(/i'
        ];

        foreach ($scriptPatterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Detect path traversal attempts
     */
    private function detectPathTraversal($value)
    {
        $pathPatterns = [
            '/\.\.\//i',
            '/\.\.\\\/i',
            '/%2e%2e%2f/i',
            '/%2e%2e%5c/i',
            '/\.\.%2f/i',
            '/\.\.%5c/i'
        ];

        foreach ($pathPatterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Detect command injection
     */
    private function detectCommandInjection($value)
    {
        $commandPatterns = [
            '/[;&|`$()]/i',
            '/\bnc\b|\bnetcat\b/i',
            '/\bwget\b|\bcurl\b/i',
            '/\bchmod\b|\bchown\b/i',
            '/\brm\b|\bdel\b/i',
            '/\bkill\b|\bkillall\b/i'
        ];

        foreach ($commandPatterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Detect LDAP injection
     */
    private function detectLdapInjection($value)
    {
        $ldapPatterns = [
            '/[()&|!*]/i',
            '/\\\\/i'
        ];

        foreach ($ldapPatterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if file extension is dangerous
     */
    private function isDangerousExtension($extension)
    {
        $dangerousExtensions = [
            'php', 'php3', 'php4', 'php5', 'phtml', 'asp', 'aspx', 'jsp', 'jspx',
            'exe', 'bat', 'cmd', 'com', 'scr', 'vbs', 'js', 'jar', 'sh', 'py',
            'pl', 'rb', 'cgi', 'htaccess', 'htpasswd'
        ];

        return in_array($extension, $dangerousExtensions);
    }

    /**
     * Check if MIME type is dangerous
     */
    private function isDangerousMimeType($mimeType)
    {
        $dangerousMimeTypes = [
            'application/x-php',
            'application/x-httpd-php',
            'text/x-php',
            'application/x-executable',
            'application/x-msdownload',
            'application/x-sh',
            'text/x-shellscript'
        ];

        return in_array($mimeType, $dangerousMimeTypes);
    }

    /**
     * Check if content contains malicious code
     */
    private function containsMaliciousCode($content)
    {
        $maliciousPatterns = [
            '/<\?php/i',
            '/<script/i',
            '/eval\s*\(/i',
            '/exec\s*\(/i',
            '/system\s*\(/i',
            '/shell_exec\s*\(/i',
            '/base64_decode\s*\(/i'
        ];

        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if field type is sensitive
     */
    private function isSensitiveField($fieldType)
    {
        $sensitiveFields = ['password', 'ssn', 'credit_card', 'api_key', 'token'];
        return in_array($fieldType, $sensitiveFields);
    }

    /**
     * Check if value is encrypted
     */
    private function isEncrypted($value)
    {
        return strpos($value, 'enc:') === 0;
    }

    /**
     * Encrypt value
     */
    private function encrypt($value)
    {
        $key = $this->getEncryptionKey();
        $encrypted = openssl_encrypt($value, 'AES-256-CBC', $key, 0, $iv = random_bytes(16));
        return 'enc:' . base64_encode($iv . $encrypted);
    }

    /**
     * Decrypt value
     */
    private function decrypt($value)
    {
        if (!$this->isEncrypted($value)) {
            return $value;
        }

        $key = $this->getEncryptionKey();
        $data = base64_decode(substr($value, 4));
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        
        return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
    }

    /**
     * Get encryption key
     */
    private function getEncryptionKey()
    {
        return hash('sha256', config('app.key', 'default-key'), true);
    }

    /**
     * Get cache value
     */
    private function getCacheValue($key, $default = null)
    {
        // Simple file-based cache implementation
        $cacheFile = sys_get_temp_dir() . '/cms_cache_' . md5($key);
        
        if (file_exists($cacheFile)) {
            $data = json_decode(file_get_contents($cacheFile), true);
            if ($data && $data['expires'] > time()) {
                return $data['value'];
            }
        }
        
        return $default;
    }

    /**
     * Set cache value
     */
    private function setCacheValue($key, $value, $ttl = 3600)
    {
        $cacheFile = sys_get_temp_dir() . '/cms_cache_' . md5($key);
        $data = [
            'value' => $value,
            'expires' => time() + $ttl
        ];
        
        file_put_contents($cacheFile, json_encode($data));
    }

    /**
     * Get maximum file size
     */
    private function getMaxFileSize()
    {
        return 10 * 1024 * 1024; // 10MB
    }

    /**
     * Log security event
     */
    private function logSecurityEvent($fieldType, $originalValue, $securedValue, $context)
    {
        if ($originalValue !== $securedValue) {
            $threats = $this->detectMaliciousContent($originalValue);
            
            if (!empty($threats)) {
                $this->db->insert('security_logs', [
                    'event_type' => 'field_security_violation',
                    'field_type' => $fieldType,
                    'threats' => json_encode($threats),
                    'user_id' => auth()->id(),
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                    'context' => json_encode($context),
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }
        }
    }

    /**
     * Initialize security rules
     */
    private function initializeSecurityRules()
    {
        $this->securityRules = [
            'text' => [
                ['type' => 'length_limit', 'max_length' => 1000],
                ['type' => 'null_byte_removal'],
                ['type' => 'control_char_removal']
            ],
            'textarea' => [
                ['type' => 'length_limit', 'max_length' => 10000],
                ['type' => 'null_byte_removal'],
                ['type' => 'control_char_removal']
            ],
            'wysiwyg' => [
                ['type' => 'length_limit', 'max_length' => 100000],
                ['type' => 'null_byte_removal']
            ],
            'email' => [
                ['type' => 'length_limit', 'max_length' => 255],
                ['type' => 'character_filter', 'pattern' => '/[^\w@.-]/']
            ],
            'url' => [
                ['type' => 'length_limit', 'max_length' => 2000],
                ['type' => 'null_byte_removal']
            ],
            'default' => [
                ['type' => 'null_byte_removal'],
                ['type' => 'control_char_removal'],
                ['type' => 'encoding_normalize']
            ]
        ];
    }
}
