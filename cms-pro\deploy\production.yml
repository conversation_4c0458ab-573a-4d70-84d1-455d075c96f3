# Production Deployment Configuration
# Docker Compose for production environment

version: '3.8'

services:
  # Web Server (Nginx)
  nginx:
    image: nginx:alpine
    container_name: cms_pro_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/sites-available:/etc/nginx/sites-available:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ../public:/var/www/html/public:ro
      - ../storage/logs/nginx:/var/log/nginx
    depends_on:
      - php-fpm
    networks:
      - cms_network

  # PHP-FPM
  php-fpm:
    build:
      context: .
      dockerfile: Dockerfile.php
    container_name: cms_pro_php
    restart: unless-stopped
    volumes:
      - ../:/var/www/html
      - ./php/php.ini:/usr/local/etc/php/php.ini:ro
      - ./php/php-fpm.conf:/usr/local/etc/php-fpm.conf:ro
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - DB_HOST=mysql
      - DB_DATABASE=cms_pro
      - DB_USERNAME=cms_user
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
    networks:
      - cms_network

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: cms_pro_mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=cms_pro
      - MYSQL_USER=cms_user
      - MYSQL_PASSWORD=${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/my.cnf:/etc/mysql/conf.d/my.cnf:ro
      - ./mysql/init:/docker-entrypoint-initdb.d:ro
    ports:
      - "3306:3306"
    networks:
      - cms_network

  # Redis Cache
  redis:
    image: redis:alpine
    container_name: cms_pro_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    ports:
      - "6379:6379"
    networks:
      - cms_network

  # Elasticsearch (for search)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: cms_pro_elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - cms_network

  # Monitoring (Prometheus)
  prometheus:
    image: prom/prometheus
    container_name: cms_pro_prometheus
    restart: unless-stopped
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - cms_network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana
    container_name: cms_pro_grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning:ro
    ports:
      - "3000:3000"
    networks:
      - cms_network

  # Log Management (ELK Stack)
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: cms_pro_logstash
    restart: unless-stopped
    volumes:
      - ./logging/logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
      - ../storage/logs:/var/log/app:ro
    depends_on:
      - elasticsearch
    networks:
      - cms_network

  # Backup Service
  backup:
    build:
      context: .
      dockerfile: Dockerfile.backup
    container_name: cms_pro_backup
    restart: unless-stopped
    environment:
      - BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
      - S3_BUCKET=${BACKUP_S3_BUCKET}
      - S3_ACCESS_KEY=${BACKUP_S3_ACCESS_KEY}
      - S3_SECRET_KEY=${BACKUP_S3_SECRET_KEY}
    volumes:
      - ../:/var/www/html:ro
      - mysql_data:/backup/mysql:ro
      - ./backup/scripts:/backup/scripts:ro
    depends_on:
      - mysql
    networks:
      - cms_network

  # SSL Certificate Management
  certbot:
    image: certbot/certbot
    container_name: cms_pro_certbot
    volumes:
      - ./ssl:/etc/letsencrypt
      - ../public:/var/www/html/public
    command: certonly --webroot --webroot-path=/var/www/html/public --email ${SSL_EMAIL} --agree-tos --no-eff-email -d ${DOMAIN_NAME}

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  cms_network:
    driver: bridge
