<?php

namespace CmsPro\Core;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\DriverManager;
use Doctrine\DBAL\Exception;

/**
 * Database Connection Manager
 * 
 * @package CmsPro\Core
 */
class Database
{
    private $connections = [];
    private $config;
    private $defaultConnection;

    public function __construct(Config $config)
    {
        $this->config = $config;
        $this->defaultConnection = $config->get('database.default', 'mysql');
    }

    /**
     * Get database connection
     */
    public function connection($name = null)
    {
        $name = $name ?: $this->defaultConnection;

        if (!isset($this->connections[$name])) {
            $this->connections[$name] = $this->createConnection($name);
        }

        return $this->connections[$name];
    }

    /**
     * Create new database connection
     */
    private function createConnection($name)
    {
        $config = $this->config->get("database.connections.{$name}");

        if (!$config) {
            throw new \InvalidArgumentException("Database connection [{$name}] not configured.");
        }

        try {
            return DriverManager::getConnection($config);
        } catch (Exception $e) {
            throw new \RuntimeException("Could not connect to database [{$name}]: " . $e->getMessage());
        }
    }

    /**
     * Execute query
     */
    public function query($sql, $params = [])
    {
        return $this->connection()->executeQuery($sql, $params);
    }

    /**
     * Execute statement
     */
    public function execute($sql, $params = [])
    {
        return $this->connection()->executeStatement($sql, $params);
    }

    /**
     * Insert record
     */
    public function insert($table, $data)
    {
        $connection = $this->connection();
        
        $columns = array_keys($data);
        $placeholders = array_map(function($col) { return ':' . $col; }, $columns);
        
        $sql = sprintf(
            'INSERT INTO %s (%s) VALUES (%s)',
            $table,
            implode(', ', $columns),
            implode(', ', $placeholders)
        );

        return $connection->executeStatement($sql, $data);
    }

    /**
     * Update record
     */
    public function update($table, $data, $where, $whereParams = [])
    {
        $connection = $this->connection();
        
        $setParts = [];
        foreach (array_keys($data) as $column) {
            $setParts[] = $column . ' = :' . $column;
        }
        
        $sql = sprintf(
            'UPDATE %s SET %s WHERE %s',
            $table,
            implode(', ', $setParts),
            $where
        );

        $params = array_merge($data, $whereParams);
        return $connection->executeStatement($sql, $params);
    }

    /**
     * Delete record
     */
    public function delete($table, $where, $params = [])
    {
        $sql = sprintf('DELETE FROM %s WHERE %s', $table, $where);
        return $this->connection()->executeStatement($sql, $params);
    }

    /**
     * Select records
     */
    public function select($sql, $params = [])
    {
        return $this->connection()->fetchAllAssociative($sql, $params);
    }

    /**
     * Select single record
     */
    public function selectOne($sql, $params = [])
    {
        return $this->connection()->fetchAssociative($sql, $params);
    }

    /**
     * Get last insert ID
     */
    public function lastInsertId()
    {
        return $this->connection()->lastInsertId();
    }

    /**
     * Begin transaction
     */
    public function beginTransaction()
    {
        return $this->connection()->beginTransaction();
    }

    /**
     * Commit transaction
     */
    public function commit()
    {
        return $this->connection()->commit();
    }

    /**
     * Rollback transaction
     */
    public function rollback()
    {
        return $this->connection()->rollBack();
    }

    /**
     * Execute in transaction
     */
    public function transaction(callable $callback)
    {
        $connection = $this->connection();
        
        $connection->beginTransaction();
        
        try {
            $result = $callback($this);
            $connection->commit();
            return $result;
        } catch (\Exception $e) {
            $connection->rollBack();
            throw $e;
        }
    }

    /**
     * Get table schema
     */
    public function getTableSchema($table)
    {
        return $this->connection()->getSchemaManager()->listTableDetails($table);
    }

    /**
     * Check if table exists
     */
    public function tableExists($table)
    {
        return $this->connection()->getSchemaManager()->tablesExist([$table]);
    }

    /**
     * Get all tables
     */
    public function getTables()
    {
        return $this->connection()->getSchemaManager()->listTableNames();
    }
}
