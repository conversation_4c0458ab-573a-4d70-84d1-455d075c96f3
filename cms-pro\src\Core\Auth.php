<?php

namespace CmsPro\Core;

use CmsPro\Models\User;
use CmsPro\Core\Session;
use CmsPro\Services\TwoFactorService;
use Symfony\Component\HttpFoundation\Request;

/**
 * Authentication Manager
 * 
 * @package CmsPro\Core
 */
class Auth
{
    private $session;
    private $user = null;
    private $userLoaded = false;
    private $twoFactorService;

    public function __construct(Session $session = null)
    {
        $this->session = $session ?: app()->getSession();
        $this->twoFactorService = new TwoFactorService();
        $this->loadUser();
    }

    /**
     * Attempt to authenticate user
     */
    public function attempt($credentials, $remember = false)
    {
        $identifier = $credentials['email'] ?? $credentials['username'] ?? null;
        $password = $credentials['password'] ?? null;

        if (!$identifier || !$password) {
            return false;
        }

        // Find user by email or username
        $user = User::findByEmail($identifier) ?: User::findByUsername($identifier);

        if (!$user) {
            return false;
        }

        // Check if account is locked
        if ($user->isLocked()) {
            throw new \RuntimeException('Account is temporarily locked due to too many failed login attempts.');
        }

        // Check if account is active
        if ($user->getStatus() !== 'active') {
            throw new \RuntimeException('Account is not active.');
        }

        // Verify password
        if (!$user->verifyPassword($password)) {
            $user->incrementLoginAttempts();
            return false;
        }

        // Login successful
        $this->login($user, $remember);
        
        return true;
    }

    /**
     * Login user
     */
    public function login(User $user, $remember = false)
    {
        // Update last login
        $request = app()->getRequest();
        $ipAddress = $request->getClientIp();
        $user->updateLastLogin($ipAddress);

        // Set session
        $this->session->put('user_id', $user->getId());
        $this->session->regenerate(true);

        // Set remember token if requested
        if ($remember) {
            $token = $user->generateRememberToken();
            setcookie('remember_token', $token, time() + (86400 * 30), '/', '', false, true); // 30 days
        }

        // Load user
        $this->user = $user;
        $this->userLoaded = true;

        // Log activity
        $this->logActivity('login', 'User logged in', [
            'ip_address' => $ipAddress,
            'user_agent' => $request->headers->get('User-Agent')
        ]);
    }

    /**
     * Logout user
     */
    public function logout()
    {
        if ($this->user) {
            // Clear remember token
            $this->user->clearRememberToken();
            
            // Log activity
            $this->logActivity('logout', 'User logged out');
        }

        // Clear session
        $this->session->forget('user_id');
        $this->session->regenerate(true);

        // Clear remember cookie
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/', '', false, true);
        }

        $this->user = null;
        $this->userLoaded = false;
    }

    /**
     * Check if user is authenticated
     */
    public function check()
    {
        return $this->user !== null;
    }

    /**
     * Check if user is guest (not authenticated)
     */
    public function guest()
    {
        return !$this->check();
    }

    /**
     * Get authenticated user
     */
    public function user()
    {
        return $this->user;
    }

    /**
     * Get user ID
     */
    public function id()
    {
        return $this->user ? $this->user->getId() : null;
    }

    /**
     * Load user from session or remember token
     */
    private function loadUser()
    {
        if ($this->userLoaded) {
            return;
        }

        // Try to load from session
        $userId = $this->session->get('user_id');
        if ($userId) {
            $this->user = User::find($userId);
            if ($this->user && $this->user->getStatus() === 'active') {
                $this->userLoaded = true;
                return;
            }
        }

        // Try to load from remember token
        if (isset($_COOKIE['remember_token'])) {
            $token = $_COOKIE['remember_token'];
            $this->user = User::findByRememberToken($token);
            
            if ($this->user && $this->user->getStatus() === 'active') {
                // Re-login user
                $this->session->put('user_id', $this->user->getId());
                $this->userLoaded = true;
                return;
            } else {
                // Invalid token, clear cookie
                setcookie('remember_token', '', time() - 3600, '/', '', false, true);
            }
        }

        $this->userLoaded = true;
    }

    /**
     * Check if user has permission
     */
    public function can($permission)
    {
        return $this->user ? $this->user->hasPermission($permission) : false;
    }

    /**
     * Check if user has role
     */
    public function hasRole($role)
    {
        return $this->user ? $this->user->hasRole($role) : false;
    }

    /**
     * Require authentication
     */
    public function requireAuth()
    {
        if ($this->guest()) {
            throw new \RuntimeException('Authentication required.', 401);
        }
    }

    /**
     * Require permission
     */
    public function requirePermission($permission)
    {
        $this->requireAuth();
        
        if (!$this->can($permission)) {
            throw new \RuntimeException('Insufficient permissions.', 403);
        }
    }

    /**
     * Require role
     */
    public function requireRole($role)
    {
        $this->requireAuth();
        
        if (!$this->hasRole($role)) {
            throw new \RuntimeException('Insufficient role.', 403);
        }
    }

    /**
     * Log user activity
     */
    private function logActivity($action, $description, $properties = [])
    {
        if (!$this->user) {
            return;
        }

        $request = app()->getRequest();
        
        app()->getDatabase()->insert('user_activity_log', [
            'user_id' => $this->user->getId(),
            'action' => $action,
            'description' => $description,
            'ip_address' => $request->getClientIp(),
            'user_agent' => $request->headers->get('User-Agent'),
            'properties' => json_encode($properties)
        ]);
    }

    /**
     * Get user activity log
     */
    public function getActivityLog($limit = 50)
    {
        if (!$this->user) {
            return [];
        }

        return app()->getDatabase()->select(
            "SELECT * FROM user_activity_log WHERE user_id = ? ORDER BY created_at DESC LIMIT ?",
            [$this->user->getId(), $limit]
        );
    }

    /**
     * Validate current password
     */
    public function validateCurrentPassword($password)
    {
        return $this->user ? $this->user->verifyPassword($password) : false;
    }

    /**
     * Change password
     */
    public function changePassword($currentPassword, $newPassword)
    {
        if (!$this->user) {
            throw new \RuntimeException('User not authenticated.');
        }

        if (!$this->validateCurrentPassword($currentPassword)) {
            throw new \RuntimeException('Current password is incorrect.');
        }

        $this->user->update(['password' => $newPassword]);
        
        $this->logActivity('password_changed', 'User changed password');
        
        return true;
    }
}
