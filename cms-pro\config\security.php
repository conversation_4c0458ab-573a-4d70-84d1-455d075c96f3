<?php

/**
 * Security Configuration
 * 
 * Security settings and hardening options
 */

return [
    
    /*
    |--------------------------------------------------------------------------
    | Authentication Security
    |--------------------------------------------------------------------------
    */
    'auth' => [
        'password_min_length' => 8,
        'password_require_uppercase' => true,
        'password_require_lowercase' => true,
        'password_require_numbers' => true,
        'password_require_symbols' => false,
        'password_history_count' => 5, // Prevent reusing last N passwords
        
        'login_attempts' => [
            'max_attempts' => 5,
            'lockout_duration' => 900, // 15 minutes
            'reset_time' => 3600 // 1 hour
        ],
        
        'session' => [
            'timeout' => 7200, // 2 hours
            'regenerate_interval' => 300, // 5 minutes
            'secure_cookies' => true,
            'httponly_cookies' => true,
            'samesite' => 'Strict'
        ],
        
        'two_factor' => [
            'enabled' => false,
            'required_for_admin' => true,
            'backup_codes_count' => 8
        ]
    ],
    
    /*
    |--------------------------------------------------------------------------
    | CSRF Protection
    |--------------------------------------------------------------------------
    */
    'csrf' => [
        'enabled' => true,
        'token_lifetime' => 3600, // 1 hour
        'regenerate_on_auth' => true,
        'exclude_routes' => [
            '/api/webhooks/*'
        ]
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Content Security Policy
    |--------------------------------------------------------------------------
    */
    'csp' => [
        'enabled' => true,
        'report_only' => false,
        'report_uri' => '/csp-report',
        
        'directives' => [
            'default-src' => ["'self'"],
            'script-src' => [
                "'self'",
                "'unsafe-inline'", // For inline scripts - consider removing in production
                'https://cdn.jsdelivr.net',
                'https://cdnjs.cloudflare.com'
            ],
            'style-src' => [
                "'self'",
                "'unsafe-inline'", // For inline styles
                'https://cdn.jsdelivr.net',
                'https://cdnjs.cloudflare.com',
                'https://fonts.googleapis.com'
            ],
            'img-src' => [
                "'self'",
                'data:',
                'https:',
                'blob:'
            ],
            'font-src' => [
                "'self'",
                'https://fonts.gstatic.com',
                'https://cdnjs.cloudflare.com'
            ],
            'connect-src' => [
                "'self'",
                'https://api.example.com'
            ],
            'media-src' => ["'self'"],
            'object-src' => ["'none'"],
            'frame-src' => ["'none'"],
            'base-uri' => ["'self'"],
            'form-action' => ["'self'"]
        ]
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Input Validation & Sanitization
    |--------------------------------------------------------------------------
    */
    'input' => [
        'max_input_vars' => 1000,
        'max_post_size' => '10M',
        'max_file_size' => '5M',
        
        'allowed_html_tags' => [
            'p', 'br', 'strong', 'em', 'u', 'i', 'b',
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'ul', 'ol', 'li', 'blockquote',
            'a', 'img', 'table', 'thead', 'tbody', 'tr', 'td', 'th'
        ],
        
        'allowed_attributes' => [
            'href', 'src', 'alt', 'title', 'class', 'id',
            'width', 'height', 'target', 'rel'
        ],
        
        'blocked_extensions' => [
            'php', 'phtml', 'php3', 'php4', 'php5', 'php7', 'php8',
            'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js',
            'jar', 'sh', 'py', 'pl', 'cgi', 'asp', 'aspx'
        ]
    ],
    
    /*
    |--------------------------------------------------------------------------
    | File Upload Security
    |--------------------------------------------------------------------------
    */
    'uploads' => [
        'scan_for_viruses' => false, // Requires ClamAV
        'check_mime_type' => true,
        'verify_file_extension' => true,
        'quarantine_suspicious' => true,
        
        'allowed_types' => [
            'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'],
            'document' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
            'archive' => ['zip', 'rar', '7z', 'tar', 'gz'],
            'video' => ['mp4', 'avi', 'mov', 'wmv', 'flv'],
            'audio' => ['mp3', 'wav', 'ogg', 'flac']
        ],
        
        'max_dimensions' => [
            'width' => 4000,
            'height' => 4000
        ]
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    */
    'rate_limiting' => [
        'enabled' => true,
        
        'limits' => [
            'api' => [
                'authenticated' => 1000, // per hour
                'unauthenticated' => 100  // per hour
            ],
            'login' => [
                'attempts' => 5, // per 15 minutes
                'window' => 900
            ],
            'contact_form' => [
                'submissions' => 3, // per hour
                'window' => 3600
            ],
            'search' => [
                'queries' => 100, // per hour
                'window' => 3600
            ]
        ]
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Security Headers
    |--------------------------------------------------------------------------
    */
    'headers' => [
        'x_frame_options' => 'DENY',
        'x_content_type_options' => 'nosniff',
        'x_xss_protection' => '1; mode=block',
        'referrer_policy' => 'strict-origin-when-cross-origin',
        'permissions_policy' => 'geolocation=(), microphone=(), camera=()',
        
        'hsts' => [
            'enabled' => true,
            'max_age' => 31536000, // 1 year
            'include_subdomains' => true,
            'preload' => true
        ]
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Database Security
    |--------------------------------------------------------------------------
    */
    'database' => [
        'use_prepared_statements' => true,
        'escape_output' => true,
        'log_queries' => false, // Only in development
        'encrypt_sensitive_data' => true,
        
        'sensitive_fields' => [
            'password', 'token', 'secret', 'key',
            'ssn', 'credit_card', 'bank_account'
        ]
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Logging & Monitoring
    |--------------------------------------------------------------------------
    */
    'logging' => [
        'log_failed_logins' => true,
        'log_admin_actions' => true,
        'log_file_uploads' => true,
        'log_security_events' => true,
        
        'alert_on' => [
            'multiple_failed_logins' => 5,
            'admin_login_from_new_ip' => true,
            'suspicious_file_upload' => true,
            'sql_injection_attempt' => true,
            'xss_attempt' => true
        ],
        
        'retention_days' => 90
    ],
    
    /*
    |--------------------------------------------------------------------------
    | IP Filtering
    |--------------------------------------------------------------------------
    */
    'ip_filtering' => [
        'enabled' => false,
        
        'whitelist' => [
            // '***********/24',
            // '10.0.0.0/8'
        ],
        
        'blacklist' => [
            // '*************',
            // '*********'
        ],
        
        'admin_whitelist' => [
            // Restrict admin access to specific IPs
            // '************',
            // '***********/24'
        ]
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Backup Security
    |--------------------------------------------------------------------------
    */
    'backup' => [
        'encrypt_backups' => true,
        'encryption_key' => env('BACKUP_ENCRYPTION_KEY'),
        'secure_storage' => true,
        'retention_policy' => [
            'daily' => 7,   // Keep 7 daily backups
            'weekly' => 4,  // Keep 4 weekly backups
            'monthly' => 12 // Keep 12 monthly backups
        ]
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Development Security
    |--------------------------------------------------------------------------
    */
    'development' => [
        'disable_in_production' => [
            'debug_mode',
            'error_display',
            'query_logging',
            'profiler'
        ],
        
        'secure_development' => [
            'use_https_locally' => false,
            'validate_ssl_certs' => true,
            'secure_session_cookies' => false // Set to true in production
        ]
    ]
];
