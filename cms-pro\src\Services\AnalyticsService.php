<?php

namespace CmsPro\Services;

use CmsPro\Core\Database;

/**
 * Analytics Service
 * 
 * Provides content analytics and performance metrics
 */
class AnalyticsService
{
    private $db;
    private $cacheService;

    public function __construct()
    {
        $this->db = app()->getDatabase();
        $this->cacheService = new CacheService();
    }

    /**
     * Get content overview statistics
     */
    public function getContentOverview()
    {
        $cacheKey = 'analytics_content_overview';
        $cached = $this->cacheService->get($cacheKey);
        
        if ($cached) {
            return $cached;
        }

        $overview = [
            'pages' => $this->getPageStatistics(),
            'blog_posts' => $this->getBlogPostStatistics(),
            'categories' => $this->getCategoryStatistics(),
            'tags' => $this->getTagStatistics(),
            'comments' => $this->getCommentStatistics()
        ];

        // Cache for 1 hour
        $this->cacheService->set($cacheKey, $overview, 3600);
        
        return $overview;
    }

    /**
     * Get page statistics
     */
    public function getPageStatistics()
    {
        $stats = $this->db->selectOne(
            "SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published,
                SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft,
                SUM(views) as total_views,
                AVG(views) as avg_views
             FROM pages"
        );

        return [
            'total' => (int) $stats['total'],
            'published' => (int) $stats['published'],
            'draft' => (int) $stats['draft'],
            'total_views' => (int) $stats['total_views'],
            'avg_views' => round($stats['avg_views'], 2)
        ];
    }

    /**
     * Get blog post statistics
     */
    public function getBlogPostStatistics()
    {
        $stats = $this->db->selectOne(
            "SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published,
                SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft,
                SUM(CASE WHEN status = 'scheduled' THEN 1 ELSE 0 END) as scheduled,
                SUM(views) as total_views,
                AVG(views) as avg_views,
                SUM(likes) as total_likes,
                SUM(comments_count) as total_comments
             FROM blog_posts"
        );

        return [
            'total' => (int) $stats['total'],
            'published' => (int) $stats['published'],
            'draft' => (int) $stats['draft'],
            'scheduled' => (int) $stats['scheduled'],
            'total_views' => (int) $stats['total_views'],
            'avg_views' => round($stats['avg_views'], 2),
            'total_likes' => (int) $stats['total_likes'],
            'total_comments' => (int) $stats['total_comments']
        ];
    }

    /**
     * Get category statistics
     */
    public function getCategoryStatistics()
    {
        $stats = $this->db->selectOne("SELECT COUNT(*) as total FROM categories");
        
        $withPosts = $this->db->selectOne(
            "SELECT COUNT(DISTINCT c.id) as count 
             FROM categories c 
             INNER JOIN blog_posts bp ON c.id = bp.category_id 
             WHERE bp.status = 'published'"
        );

        return [
            'total' => (int) $stats['total'],
            'with_posts' => (int) $withPosts['count'],
            'empty' => (int) $stats['total'] - (int) $withPosts['count']
        ];
    }

    /**
     * Get tag statistics
     */
    public function getTagStatistics()
    {
        $stats = $this->db->selectOne("SELECT COUNT(*) as total FROM tags");
        
        $withPosts = $this->db->selectOne(
            "SELECT COUNT(DISTINCT t.id) as count 
             FROM tags t 
             INNER JOIN blog_post_tags bpt ON t.id = bpt.tag_id 
             INNER JOIN blog_posts bp ON bpt.post_id = bp.id 
             WHERE bp.status = 'published'"
        );

        return [
            'total' => (int) $stats['total'],
            'with_posts' => (int) $withPosts['count'],
            'empty' => (int) $stats['total'] - (int) $withPosts['count']
        ];
    }

    /**
     * Get comment statistics
     */
    public function getCommentStatistics()
    {
        $stats = $this->db->selectOne(
            "SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'spam' THEN 1 ELSE 0 END) as spam
             FROM comments"
        );

        return [
            'total' => (int) $stats['total'],
            'approved' => (int) $stats['approved'],
            'pending' => (int) $stats['pending'],
            'spam' => (int) $stats['spam']
        ];
    }

    /**
     * Get top performing content
     */
    public function getTopPerformingContent($type = 'all', $limit = 10, $period = '30')
    {
        $cacheKey = "analytics_top_content_{$type}_{$limit}_{$period}";
        $cached = $this->cacheService->get($cacheKey);
        
        if ($cached) {
            return $cached;
        }

        $dateFilter = $this->getDateFilter($period);
        $results = [];

        if ($type === 'all' || $type === 'pages') {
            $pages = $this->db->select(
                "SELECT 'page' as type, id, title, slug, views, created_at
                 FROM pages 
                 WHERE status = 'published' {$dateFilter}
                 ORDER BY views DESC 
                 LIMIT ?",
                [$limit]
            );
            $results = array_merge($results, $pages);
        }

        if ($type === 'all' || $type === 'blog_posts') {
            $posts = $this->db->select(
                "SELECT 'blog_post' as type, id, title, slug, views, likes, comments_count, created_at
                 FROM blog_posts 
                 WHERE status = 'published' {$dateFilter}
                 ORDER BY views DESC 
                 LIMIT ?",
                [$limit]
            );
            $results = array_merge($results, $posts);
        }

        // Sort by views if getting all types
        if ($type === 'all') {
            usort($results, function($a, $b) {
                return $b['views'] <=> $a['views'];
            });
            $results = array_slice($results, 0, $limit);
        }

        // Cache for 30 minutes
        $this->cacheService->set($cacheKey, $results, 1800);
        
        return $results;
    }

    /**
     * Get content performance over time
     */
    public function getContentPerformanceOverTime($period = '30')
    {
        $cacheKey = "analytics_performance_time_{$period}";
        $cached = $this->cacheService->get($cacheKey);
        
        if ($cached) {
            return $cached;
        }

        $dateFormat = $period <= 7 ? '%Y-%m-%d' : '%Y-%m';
        $dateFilter = $this->getDateFilter($period);

        // Get page views over time
        $pageViews = $this->db->select(
            "SELECT DATE_FORMAT(created_at, '{$dateFormat}') as date, 
                    COUNT(*) as pages_created,
                    SUM(views) as total_views
             FROM pages 
             WHERE status = 'published' {$dateFilter}
             GROUP BY DATE_FORMAT(created_at, '{$dateFormat}')
             ORDER BY date"
        );

        // Get blog post performance over time
        $blogViews = $this->db->select(
            "SELECT DATE_FORMAT(created_at, '{$dateFormat}') as date, 
                    COUNT(*) as posts_created,
                    SUM(views) as total_views,
                    SUM(likes) as total_likes,
                    SUM(comments_count) as total_comments
             FROM blog_posts 
             WHERE status = 'published' {$dateFilter}
             GROUP BY DATE_FORMAT(created_at, '{$dateFormat}')
             ORDER BY date"
        );

        $performance = [
            'pages' => $pageViews,
            'blog_posts' => $blogViews
        ];

        // Cache for 1 hour
        $this->cacheService->set($cacheKey, $performance, 3600);
        
        return $performance;
    }

    /**
     * Get popular categories
     */
    public function getPopularCategories($limit = 10)
    {
        $cacheKey = "analytics_popular_categories_{$limit}";
        $cached = $this->cacheService->get($cacheKey);
        
        if ($cached) {
            return $cached;
        }

        $categories = $this->db->select(
            "SELECT c.id, c.name, c.slug, 
                    COUNT(bp.id) as posts_count,
                    SUM(bp.views) as total_views,
                    AVG(bp.views) as avg_views
             FROM categories c
             INNER JOIN blog_posts bp ON c.id = bp.category_id
             WHERE bp.status = 'published'
             GROUP BY c.id, c.name, c.slug
             ORDER BY total_views DESC, posts_count DESC
             LIMIT ?",
            [$limit]
        );

        foreach ($categories as &$category) {
            $category['posts_count'] = (int) $category['posts_count'];
            $category['total_views'] = (int) $category['total_views'];
            $category['avg_views'] = round($category['avg_views'], 2);
        }

        // Cache for 2 hours
        $this->cacheService->set($cacheKey, $categories, 7200);
        
        return $categories;
    }

    /**
     * Get popular tags
     */
    public function getPopularTags($limit = 20)
    {
        $cacheKey = "analytics_popular_tags_{$limit}";
        $cached = $this->cacheService->get($cacheKey);
        
        if ($cached) {
            return $cached;
        }

        $tags = $this->db->select(
            "SELECT t.id, t.name, t.slug, 
                    COUNT(bpt.post_id) as posts_count,
                    SUM(bp.views) as total_views,
                    AVG(bp.views) as avg_views
             FROM tags t
             INNER JOIN blog_post_tags bpt ON t.id = bpt.tag_id
             INNER JOIN blog_posts bp ON bpt.post_id = bp.id
             WHERE bp.status = 'published'
             GROUP BY t.id, t.name, t.slug
             ORDER BY total_views DESC, posts_count DESC
             LIMIT ?",
            [$limit]
        );

        foreach ($tags as &$tag) {
            $tag['posts_count'] = (int) $tag['posts_count'];
            $tag['total_views'] = (int) $tag['total_views'];
            $tag['avg_views'] = round($tag['avg_views'], 2);
        }

        // Cache for 2 hours
        $this->cacheService->set($cacheKey, $tags, 7200);
        
        return $tags;
    }

    /**
     * Get author performance
     */
    public function getAuthorPerformance($limit = 10)
    {
        $cacheKey = "analytics_author_performance_{$limit}";
        $cached = $this->cacheService->get($cacheKey);
        
        if ($cached) {
            return $cached;
        }

        $authors = $this->db->select(
            "SELECT u.id, u.first_name, u.last_name, u.email,
                    COUNT(bp.id) as posts_count,
                    SUM(bp.views) as total_views,
                    AVG(bp.views) as avg_views,
                    SUM(bp.likes) as total_likes,
                    SUM(bp.comments_count) as total_comments
             FROM users u
             INNER JOIN blog_posts bp ON u.id = bp.author_id
             WHERE bp.status = 'published'
             GROUP BY u.id, u.first_name, u.last_name, u.email
             ORDER BY total_views DESC, posts_count DESC
             LIMIT ?",
            [$limit]
        );

        foreach ($authors as &$author) {
            $author['full_name'] = trim($author['first_name'] . ' ' . $author['last_name']);
            $author['posts_count'] = (int) $author['posts_count'];
            $author['total_views'] = (int) $author['total_views'];
            $author['avg_views'] = round($author['avg_views'], 2);
            $author['total_likes'] = (int) $author['total_likes'];
            $author['total_comments'] = (int) $author['total_comments'];
        }

        // Cache for 2 hours
        $this->cacheService->set($cacheKey, $authors, 7200);
        
        return $authors;
    }

    /**
     * Get engagement metrics
     */
    public function getEngagementMetrics($period = '30')
    {
        $cacheKey = "analytics_engagement_{$period}";
        $cached = $this->cacheService->get($cacheKey);
        
        if ($cached) {
            return $cached;
        }

        $dateFilter = $this->getDateFilter($period);

        $metrics = $this->db->selectOne(
            "SELECT 
                COUNT(DISTINCT bp.id) as total_posts,
                SUM(bp.views) as total_views,
                SUM(bp.likes) as total_likes,
                SUM(bp.comments_count) as total_comments,
                AVG(bp.views) as avg_views_per_post,
                AVG(bp.likes) as avg_likes_per_post,
                AVG(bp.comments_count) as avg_comments_per_post
             FROM blog_posts bp
             WHERE bp.status = 'published' {$dateFilter}"
        );

        $engagement = [
            'total_posts' => (int) $metrics['total_posts'],
            'total_views' => (int) $metrics['total_views'],
            'total_likes' => (int) $metrics['total_likes'],
            'total_comments' => (int) $metrics['total_comments'],
            'avg_views_per_post' => round($metrics['avg_views_per_post'], 2),
            'avg_likes_per_post' => round($metrics['avg_likes_per_post'], 2),
            'avg_comments_per_post' => round($metrics['avg_comments_per_post'], 2),
            'engagement_rate' => 0
        ];

        // Calculate engagement rate (likes + comments) / views
        if ($engagement['total_views'] > 0) {
            $engagement['engagement_rate'] = round(
                (($engagement['total_likes'] + $engagement['total_comments']) / $engagement['total_views']) * 100,
                2
            );
        }

        // Cache for 1 hour
        $this->cacheService->set($cacheKey, $engagement, 3600);
        
        return $engagement;
    }

    /**
     * Get date filter for SQL queries
     */
    private function getDateFilter($period)
    {
        if (!is_numeric($period)) {
            return '';
        }

        $days = (int) $period;
        return "AND created_at >= DATE_SUB(NOW(), INTERVAL {$days} DAY)";
    }

    /**
     * Clear analytics cache
     */
    public function clearCache()
    {
        return $this->cacheService->flush('analytics_*');
    }

    /**
     * Get real-time statistics
     */
    public function getRealTimeStats()
    {
        // Get stats for today
        $today = date('Y-m-d');
        
        $stats = [
            'today_views' => $this->getTodayViews(),
            'today_posts' => $this->getTodayPosts(),
            'today_comments' => $this->getTodayComments(),
            'online_users' => $this->getOnlineUsers()
        ];

        return $stats;
    }

    /**
     * Get today's page views
     */
    private function getTodayViews()
    {
        $pageViews = $this->db->selectOne(
            "SELECT SUM(views) as views FROM pages WHERE DATE(updated_at) = CURDATE()"
        );
        
        $blogViews = $this->db->selectOne(
            "SELECT SUM(views) as views FROM blog_posts WHERE DATE(updated_at) = CURDATE()"
        );

        return (int) ($pageViews['views'] ?? 0) + (int) ($blogViews['views'] ?? 0);
    }

    /**
     * Get today's new posts
     */
    private function getTodayPosts()
    {
        $result = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM blog_posts WHERE DATE(created_at) = CURDATE() AND status = 'published'"
        );

        return (int) $result['count'];
    }

    /**
     * Get today's new comments
     */
    private function getTodayComments()
    {
        $result = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM comments WHERE DATE(created_at) = CURDATE() AND status = 'approved'"
        );

        return (int) $result['count'];
    }

    /**
     * Get online users (simplified - users active in last 15 minutes)
     */
    private function getOnlineUsers()
    {
        $result = $this->db->selectOne(
            "SELECT COUNT(DISTINCT user_id) as count 
             FROM activity_logs 
             WHERE created_at >= DATE_SUB(NOW(), INTERVAL 15 MINUTE)"
        );

        return (int) ($result['count'] ?? 0);
    }
}
