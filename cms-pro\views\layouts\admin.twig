<!DOCTYPE html>
<html lang="{{ app.locale }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    
    <title>{% block title %}{{ title ?? 'Admin Panel' }} - {{ app.name }}{% endblock %}</title>
    
    <meta name="description" content="{{ meta_description ?? 'Admin Panel' }}">
    <meta name="robots" content="noindex, nofollow">
    <meta name="csrf-token" content="{{ csrf_token }}">
    
    {% block styles %}
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Admin CSS -->
    <link href="{{ asset('css/admin.css') }}" rel="stylesheet">
    {% endblock %}
</head>
<body class="admin-body">
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <i class="fas fa-cube me-2"></i>
                <span class="brand-text">{{ app.name }}</span>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <div class="sidebar-menu">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{ request.pathInfo starts with '/admin/dashboard' ? 'active' : '' }}" 
                       href="{{ url('/admin/dashboard') }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <span class="nav-text">{{ __('Dashboard') }}</span>
                    </a>
                </li>
                
                {% if user.hasPermission('content.view') %}
                <li class="nav-item">
                    <a class="nav-link {{ request.pathInfo starts with '/admin/content' ? 'active' : '' }}" 
                       href="{{ url('/admin/content') }}">
                        <i class="fas fa-file-alt"></i>
                        <span class="nav-text">{{ __('Content') }}</span>
                    </a>
                </li>
                {% endif %}
                
                {% if user.hasPermission('pages.view') %}
                <li class="nav-item">
                    <a class="nav-link {{ request.pathInfo starts with '/admin/pages' ? 'active' : '' }}" 
                       href="{{ url('/admin/pages') }}">
                        <i class="fas fa-copy"></i>
                        <span class="nav-text">{{ __('Pages') }}</span>
                    </a>
                </li>
                {% endif %}
                
                {% if user.hasPermission('blog.view') %}
                <li class="nav-item">
                    <a class="nav-link {{ request.pathInfo starts with '/admin/blog' ? 'active' : '' }}" 
                       href="{{ url('/admin/blog') }}">
                        <i class="fas fa-blog"></i>
                        <span class="nav-text">{{ __('Blog') }}</span>
                    </a>
                </li>
                {% endif %}
                
                {% if user.hasPermission('fields.view') %}
                <li class="nav-item">
                    <a class="nav-link {{ request.pathInfo starts with '/admin/fields' ? 'active' : '' }}" 
                       href="{{ url('/admin/fields') }}">
                        <i class="fas fa-cogs"></i>
                        <span class="nav-text">{{ __('Dynamic Fields') }}</span>
                    </a>
                </li>
                {% endif %}
                
                {% if user.hasPermission('media.view') %}
                <li class="nav-item">
                    <a class="nav-link {{ request.pathInfo starts with '/admin/media' ? 'active' : '' }}" 
                       href="{{ url('/admin/media') }}">
                        <i class="fas fa-images"></i>
                        <span class="nav-text">{{ __('Media') }}</span>
                    </a>
                </li>
                {% endif %}
                
                {% if user.hasPermission('users.view') %}
                <li class="nav-item">
                    <a class="nav-link {{ request.pathInfo starts with '/admin/users' ? 'active' : '' }}" 
                       href="{{ url('/admin/users') }}">
                        <i class="fas fa-users"></i>
                        <span class="nav-text">{{ __('Users') }}</span>
                    </a>
                </li>
                {% endif %}
                
                {% if user.hasPermission('settings.view') %}
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="collapse" data-bs-target="#settingsMenu">
                        <i class="fas fa-cog"></i>
                        <span class="nav-text">{{ __('Settings') }}</span>
                    </a>
                    <div class="collapse" id="settingsMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url('/admin/settings') }}">
                                    <i class="fas fa-sliders-h"></i>
                                    <span class="nav-text">{{ __('General') }}</span>
                                </a>
                            </li>
                            {% if user.hasPermission('themes.manage') %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url('/admin/themes') }}">
                                    <i class="fas fa-palette"></i>
                                    <span class="nav-text">{{ __('Themes') }}</span>
                                </a>
                            </li>
                            {% endif %}
                            {% if user.hasPermission('languages.manage') %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url('/admin/languages') }}">
                                    <i class="fas fa-globe"></i>
                                    <span class="nav-text">{{ __('Languages') }}</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                </li>
                {% endif %}
                
                {% if user.hasPermission('system.view') %}
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="collapse" data-bs-target="#systemMenu">
                        <i class="fas fa-server"></i>
                        <span class="nav-text">{{ __('System') }}</span>
                    </a>
                    <div class="collapse" id="systemMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url('/admin/system') }}">
                                    <i class="fas fa-info-circle"></i>
                                    <span class="nav-text">{{ __('System Info') }}</span>
                                </a>
                            </li>
                            {% if user.hasPermission('backups.manage') %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url('/admin/backups') }}">
                                    <i class="fas fa-download"></i>
                                    <span class="nav-text">{{ __('Backups') }}</span>
                                </a>
                            </li>
                            {% endif %}
                            {% if user.hasPermission('logs.view') %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url('/admin/system/logs') }}">
                                    <i class="fas fa-list-alt"></i>
                                    <span class="nav-text">{{ __('Logs') }}</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                </li>
                {% endif %}
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Top Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom">
            <div class="container-fluid">
                <button class="btn btn-link sidebar-toggle-btn d-lg-none" id="sidebarToggleMobile">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="navbar-nav ms-auto">
                    <!-- View Site -->
                    <a class="nav-link" href="{{ url('/') }}" target="_blank" title="{{ __('View Site') }}">
                        <i class="fas fa-external-link-alt"></i>
                    </a>
                    
                    <!-- User Dropdown -->
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" data-bs-toggle="dropdown">
                            <img src="{{ user.avatarUrl }}" alt="{{ user.fullName }}" class="rounded-circle me-2" width="32" height="32">
                            <span>{{ user.fullName }}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ url('/admin/profile') }}">
                                <i class="fas fa-user me-2"></i>{{ __('Profile') }}
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="{{ url('/admin/logout') }}" class="d-inline">
                                    {{ csrf_field() | raw }}
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-sign-out-alt me-2"></i>{{ __('Logout') }}
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <div class="content-wrapper">
            {% block alerts %}
            <div class="container-fluid">
                {% for type, messages in session.getAllFlash() %}
                    {% for message in messages %}
                    <div class="alert alert-{{ type == 'error' ? 'danger' : type }} alert-dismissible fade show mt-3" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    {% endfor %}
                {% endfor %}
            </div>
            {% endblock %}
            
            {% block content %}{% endblock %}
        </div>
    </div>

    {% block scripts %}
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Admin JS -->
    <script src="{{ asset('js/admin.js') }}"></script>
    {% endblock %}
</body>
</html>
