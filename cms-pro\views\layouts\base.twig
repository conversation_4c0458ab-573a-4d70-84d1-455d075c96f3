<!DOCTYPE html>
<html lang="{{ app.locale }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    
    <title>{% block title %}{{ title ?? app.name }}{% endblock %}</title>
    
    {% block meta %}
    <meta name="description" content="{{ meta_description ?? 'Professional Content Management System' }}">
    <meta name="keywords" content="{{ meta_keywords ?? 'CMS, Content Management, PHP' }}">
    <meta name="author" content="{{ app.name }}">
    {% endblock %}
    
    {% block og_tags %}
    <meta property="og:title" content="{{ title ?? app.name }}">
    <meta property="og:description" content="{{ meta_description ?? 'Professional Content Management System' }}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url() }}">
    <meta property="og:site_name" content="{{ app.name }}">
    {% endblock %}
    
    <link rel="canonical" href="{{ canonical_url ?? url() }}">
    
    {% block styles %}
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ asset('css/app.css') }}" rel="stylesheet">
    {% endblock %}
    
    {% block head_scripts %}{% endblock %}
</head>
<body class="{% block body_class %}{% endblock %}">
    {% block header %}
    <header class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url('/') }}">
                <i class="fas fa-cube me-2"></i>
                {{ app.name }}
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url('/') }}">{{ __('Home') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url('/blog') }}">{{ __('Blog') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url('/contact') }}">{{ __('Contact') }}</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    {% if user %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            {{ user.name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url('/profile') }}">{{ __('Profile') }}</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="{{ url('/logout') }}" class="d-inline">
                                    {{ csrf_field() | raw }}
                                    <button type="submit" class="dropdown-item">{{ __('Logout') }}</button>
                                </form>
                            </li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url('/login') }}">{{ __('Login') }}</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </header>
    {% endblock %}
    
    {% block alerts %}
    <div class="container mt-3">
        {% for type, messages in session.getAllFlash() %}
            {% for message in messages %}
            <div class="alert alert-{{ type == 'error' ? 'danger' : type }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        {% endfor %}
    </div>
    {% endblock %}
    
    <main>
        {% block content %}{% endblock %}
    </main>
    
    {% block footer %}
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>{{ app.name }}</h5>
                    <p class="text-muted">{{ __('Professional Content Management System') }}</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted">
                        &copy; {{ "now"|date("Y") }} {{ app.name }}. {{ __('All rights reserved.') }}
                    </p>
                </div>
            </div>
        </div>
    </footer>
    {% endblock %}
    
    {% block scripts %}
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ asset('js/app.js') }}"></script>
    {% endblock %}
    
    {% block footer_scripts %}{% endblock %}
</body>
</html>
