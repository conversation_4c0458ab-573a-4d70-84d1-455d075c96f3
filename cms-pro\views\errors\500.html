<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Error - CMS Pro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .error-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
            animation: fadeInUp 0.8s ease-out;
        }
        
        .error-code {
            font-size: 6rem;
            font-weight: 900;
            color: #dc3545;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        
        .error-title {
            color: #495057;
            margin-bottom: 1.5rem;
        }
        
        .error-message {
            color: #6c757d;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .btn-home {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        }
        
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,123,255,0.4);
            color: white;
        }
        
        .error-icon {
            font-size: 4rem;
            color: #ffc107;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }
        
        .support-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
            border-left: 4px solid #007bff;
        }
        
        .support-info h6 {
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        .support-info p {
            color: #6c757d;
            margin-bottom: 0;
            font-size: 0.9rem;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }
        
        @media (max-width: 768px) {
            .error-container {
                margin: 1rem;
                padding: 2rem;
            }
            
            .error-code {
                font-size: 4rem;
            }
            
            .error-icon {
                font-size: 3rem;
            }
        }
        
        /* Loading animation for retry button */
        .btn-retry {
            position: relative;
            overflow: hidden;
        }
        
        .btn-retry.loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: loading 1.5s infinite;
        }
        
        @keyframes loading {
            0% { left: -100%; }
            100% { left: 100%; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="error-container">
                    <!-- Error Icon -->
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    
                    <!-- Error Code -->
                    <div class="error-code">500</div>
                    
                    <!-- Error Title -->
                    <h2 class="error-title">Internal Server Error</h2>
                    
                    <!-- Error Message -->
                    <p class="error-message">
                        We're experiencing some technical difficulties right now. 
                        Our team has been notified and is working to resolve the issue.
                    </p>
                    
                    <!-- Action Buttons -->
                    <div class="mb-4">
                        <a href="/" class="btn-home me-3">
                            <i class="fas fa-home me-2"></i>Go Home
                        </a>
                        <button type="button" class="btn btn-outline-secondary btn-retry" onclick="retryPage()">
                            <i class="fas fa-redo me-2"></i>Try Again
                        </button>
                    </div>
                    
                    <!-- Support Information -->
                    <div class="support-info">
                        <h6><i class="fas fa-life-ring me-2"></i>Need Help?</h6>
                        <p>
                            If this problem persists, please contact our support team at 
                            <strong><EMAIL></strong> or try again in a few minutes.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Retry functionality
        function retryPage() {
            const retryBtn = document.querySelector('.btn-retry');
            retryBtn.classList.add('loading');
            retryBtn.disabled = true;
            
            // Add a small delay to show loading animation
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }
        
        // Auto-retry after 30 seconds (optional)
        let autoRetryTimer = setTimeout(() => {
            if (confirm('Would you like to automatically retry loading the page?')) {
                retryPage();
            }
        }, 30000);
        
        // Clear auto-retry if user interacts with page
        document.addEventListener('click', () => {
            clearTimeout(autoRetryTimer);
        });
        
        // Report error to analytics if available
        if (typeof gtag !== 'undefined') {
            gtag('event', 'exception', {
                'description': 'Server Error 500',
                'fatal': false
            });
        }
        
        // Console message for developers
        console.log('%cCMS Pro - Server Error', 'color: #dc3545; font-size: 16px; font-weight: bold;');
        console.log('If you are a developer, check the server logs for more details.');
        console.log('Error occurred at:', new Date().toISOString());
        console.log('User Agent:', navigator.userAgent);
        console.log('URL:', window.location.href);
    </script>
</body>
</html>
