-- Create blog_posts table
CREATE TABLE IF NOT EXISTS blog_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    content LONGTEXT NOT NULL,
    excerpt TEXT,
    status ENUM('draft', 'published', 'scheduled', 'archived') DEFAULT 'draft',
    author_id INT NOT NULL,
    category_id INT NULL,
    featured_image VARCHAR(500),
    meta_title VARCHAR(60),
    meta_description VARCHAR(160),
    meta_keywords VARCHAR(255),
    published_at TIMESTAMP NULL,
    scheduled_at TIMESTAMP NULL,
    views INT DEFAULT 0,
    likes INT DEFAULT 0,
    comments_count INT DEFAULT 0,
    reading_time INT DEFAULT 1,
    settings JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_slug (slug),
    INDEX idx_status (status),
    INDEX idx_author (author_id),
    INDEX idx_category (category_id),
    INDEX idx_published (published_at),
    INDEX idx_created (created_at),
    FULLTEXT idx_search (title, content, excerpt),
    
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create blog_post_tags junction table
CREATE TABLE IF NOT EXISTS blog_post_tags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    post_id INT NOT NULL,
    tag_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_post_tag (post_id, tag_id),
    INDEX idx_post (post_id),
    INDEX idx_tag (tag_id),
    
    FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create comments table
CREATE TABLE IF NOT EXISTS comments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    post_id INT NOT NULL,
    post_type ENUM('blog_post', 'page') DEFAULT 'blog_post',
    parent_id INT NULL,
    author_name VARCHAR(100) NOT NULL,
    author_email VARCHAR(255) NOT NULL,
    author_url VARCHAR(255),
    author_ip VARCHAR(45),
    content TEXT NOT NULL,
    status ENUM('pending', 'approved', 'spam', 'trash') DEFAULT 'pending',
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_post (post_id, post_type),
    INDEX idx_status (status),
    INDEX idx_parent (parent_id),
    INDEX idx_email (author_email),
    INDEX idx_created (created_at),
    
    FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Update categories table for blog support
ALTER TABLE categories 
ADD COLUMN IF NOT EXISTS color VARCHAR(7) DEFAULT '#007bff',
ADD COLUMN IF NOT EXISTS icon VARCHAR(50),
ADD COLUMN IF NOT EXISTS meta_title VARCHAR(60),
ADD COLUMN IF NOT EXISTS meta_description VARCHAR(160),
ADD COLUMN IF NOT EXISTS meta_keywords VARCHAR(255);

-- Update tags table for blog support  
ALTER TABLE tags
ADD COLUMN IF NOT EXISTS color VARCHAR(7) DEFAULT '#6c757d',
ADD COLUMN IF NOT EXISTS meta_title VARCHAR(60),
ADD COLUMN IF NOT EXISTS meta_description VARCHAR(160),
ADD COLUMN IF NOT EXISTS meta_keywords VARCHAR(255);

-- Insert sample blog data
INSERT INTO categories (name, slug, description, sort_order, color, icon) VALUES
('Technology', 'technology', 'Latest technology news and tutorials', 1, '#007bff', 'fas fa-laptop-code'),
('Web Development', 'web-development', 'Web development tips and tricks', 2, '#28a745', 'fas fa-code'),
('Design', 'design', 'UI/UX design inspiration and resources', 3, '#dc3545', 'fas fa-paint-brush'),
('Business', 'business', 'Business insights and strategies', 4, '#ffc107', 'fas fa-briefcase')
ON DUPLICATE KEY UPDATE name = VALUES(name);

INSERT INTO tags (name, slug, description, color) VALUES
('PHP', 'php', 'PHP programming language', '#777bb4'),
('JavaScript', 'javascript', 'JavaScript programming language', '#f7df1e'),
('CSS', 'css', 'Cascading Style Sheets', '#1572b6'),
('HTML', 'html', 'HyperText Markup Language', '#e34f26'),
('MySQL', 'mysql', 'MySQL database', '#4479a1'),
('Bootstrap', 'bootstrap', 'Bootstrap CSS framework', '#7952b3'),
('Tutorial', 'tutorial', 'Step-by-step tutorials', '#17a2b8'),
('Tips', 'tips', 'Helpful tips and tricks', '#28a745')
ON DUPLICATE KEY UPDATE name = VALUES(name);

-- Insert sample blog posts
INSERT INTO blog_posts (title, slug, content, excerpt, status, author_id, category_id, published_at, views, reading_time) VALUES
('Getting Started with PHP Development', 'getting-started-php-development', 
'<h2>Introduction to PHP</h2><p>PHP is a popular server-side scripting language that is especially suited for web development...</p><h3>Setting up your environment</h3><p>Before you start coding in PHP, you need to set up your development environment...</p>', 
'Learn the basics of PHP development and set up your first development environment.',
'published', 1, 2, NOW(), 150, 5),

('Modern CSS Techniques for 2024', 'modern-css-techniques-2024',
'<h2>CSS Grid and Flexbox</h2><p>Modern CSS layout techniques have revolutionized how we build responsive websites...</p><h3>Container Queries</h3><p>Container queries are a game-changer for responsive design...</p>',
'Discover the latest CSS techniques and best practices for modern web development.',
'published', 1, 3, NOW(), 89, 7),

('Building Responsive Web Applications', 'building-responsive-web-applications',
'<h2>Mobile-First Approach</h2><p>In today''s mobile-centric world, building responsive web applications is crucial...</p><h3>Breakpoints and Media Queries</h3><p>Understanding breakpoints is essential for responsive design...</p>',
'Learn how to build responsive web applications that work seamlessly across all devices.',
'published', 1, 2, NOW(), 234, 8)
ON DUPLICATE KEY UPDATE title = VALUES(title);

-- Link blog posts with tags
INSERT INTO blog_post_tags (post_id, tag_id) VALUES
(1, 1), (1, 7), -- PHP post with PHP and Tutorial tags
(2, 3), (2, 8), -- CSS post with CSS and Tips tags  
(3, 2), (3, 3), (3, 6), (3, 7) -- Responsive post with JavaScript, CSS, Bootstrap, Tutorial tags
ON DUPLICATE KEY UPDATE post_id = VALUES(post_id);
