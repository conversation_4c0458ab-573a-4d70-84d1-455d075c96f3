<?php

/**
 * Database Migration Script
 * 
 * Runs all database migrations for CMS Pro
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Database configuration
$config = [
    'host' => $_ENV['DB_HOST'] ?? '127.0.0.1',
    'port' => $_ENV['DB_PORT'] ?? '3306',
    'database' => $_ENV['DB_DATABASE'] ?? 'cms_pro',
    'username' => $_ENV['DB_USERNAME'] ?? 'root',
    'password' => $_ENV['DB_PASSWORD'] ?? '',
    'charset' => 'utf8mb4'
];

try {
    // Connect to database
    $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$config['charset']}"
    ]);

    echo "✅ Database connection successful!\n";

    // Migration files in order
    $migrations = [
        '001_create_users_table.sql',
        '002_create_roles_table.sql', 
        '003_create_permissions_table.sql',
        '004_create_pages_table.sql',
        '005_create_media_table.sql',
        '006_create_settings_table.sql',
        '007_create_activity_logs_table.sql',
        '008_create_categories_tags_table.sql',
        '009_create_blog_tables.sql'
    ];

    // Run migrations
    foreach ($migrations as $migration) {
        $migrationPath = __DIR__ . '/database/migrations/' . $migration;
        
        if (!file_exists($migrationPath)) {
            echo "⚠️  Migration file not found: {$migration}\n";
            continue;
        }

        echo "🔄 Running migration: {$migration}\n";
        
        $sql = file_get_contents($migrationPath);
        
        // Split by semicolon and execute each statement
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                try {
                    $pdo->exec($statement);
                } catch (PDOException $e) {
                    // Skip if table already exists
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        throw $e;
                    }
                }
            }
        }
        
        echo "✅ Migration completed: {$migration}\n";
    }

    // Insert default data
    echo "🔄 Inserting default data...\n";
    
    // Create admin role
    $pdo->exec("INSERT IGNORE INTO roles (id, name, slug, description, created_at, updated_at) VALUES 
        (1, 'Administrator', 'administrator', 'Full system access', NOW(), NOW())");
    
    // Create editor role  
    $pdo->exec("INSERT IGNORE INTO roles (id, name, slug, description, created_at, updated_at) VALUES 
        (2, 'Editor', 'editor', 'Content management access', NOW(), NOW())");
    
    // Create admin user
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $pdo->exec("INSERT IGNORE INTO users (id, first_name, last_name, email, password, role_id, status, created_at, updated_at) VALUES 
        (1, 'Admin', 'User', '<EMAIL>', '{$adminPassword}', 1, 'active', NOW(), NOW())");
    
    // Create basic settings
    $settings = [
        ['site_name', 'CMS Pro'],
        ['site_description', 'Modern Content Management System'],
        ['site_url', 'http://localhost/cms-pro'],
        ['admin_email', '<EMAIL>'],
        ['timezone', 'Europe/Istanbul'],
        ['language', 'tr'],
        ['theme', 'default'],
        ['posts_per_page', '10'],
        ['allow_comments', '1'],
        ['moderate_comments', '1']
    ];
    
    foreach ($settings as $setting) {
        $pdo->exec("INSERT IGNORE INTO settings (`key`, `value`, created_at, updated_at) VALUES 
            ('{$setting[0]}', '{$setting[1]}', NOW(), NOW())");
    }
    
    // Create sample category
    $pdo->exec("INSERT IGNORE INTO categories (id, name, slug, description, sort_order, created_at, updated_at) VALUES 
        (1, 'Genel', 'genel', 'Genel kategori', 1, NOW(), NOW())");
    
    // Create sample tag
    $pdo->exec("INSERT IGNORE INTO tags (id, name, slug, description, created_at, updated_at) VALUES 
        (1, 'Örnek', 'ornek', 'Örnek etiket', NOW(), NOW())");
    
    // Create sample page
    $pdo->exec("INSERT IGNORE INTO pages (id, title, slug, content, excerpt, status, author_id, created_at, updated_at) VALUES 
        (1, 'Ana Sayfa', 'ana-sayfa', '<h1>CMS Pro\'ya Hoş Geldiniz!</h1><p>Bu modern içerik yönetim sistemi ile web sitenizi kolayca yönetebilirsiniz.</p>', 'CMS Pro ana sayfası', 'published', 1, NOW(), NOW())");
    
    // Create sample blog post
    $pdo->exec("INSERT IGNORE INTO blog_posts (id, title, slug, content, excerpt, status, author_id, category_id, reading_time, created_at, updated_at) VALUES 
        (1, 'İlk Blog Yazısı', 'ilk-blog-yazisi', '<p>Bu CMS Pro ile oluşturulmuş ilk blog yazısıdır. Sistem başarıyla kurulmuş ve çalışmaya hazırdır!</p>', 'CMS Pro ile ilk blog yazısı', 'published', 1, 1, 1, NOW(), NOW())");

    echo "✅ Default data inserted successfully!\n";
    echo "\n🎉 CMS Pro installation completed successfully!\n";
    echo "\n📋 Login Information:\n";
    echo "   URL: http://localhost/cms-pro/public/admin\n";
    echo "   Email: <EMAIL>\n";
    echo "   Password: admin123\n";
    echo "\n🌐 Frontend: http://localhost/cms-pro/public/\n";

} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
