<?php

namespace CmsPro\Models;

use CmsPro\Core\Database;

/**
 * Activity Log Model
 * 
 * @package CmsPro\Models
 */
class ActivityLog
{
    private $db;
    private $data = [];

    public function __construct(Database $db = null)
    {
        $this->db = $db ?: app()->getDatabase();
    }

    /**
     * Find activity log by ID
     */
    public static function find($id)
    {
        $instance = new static();
        $logData = $instance->db->selectOne(
            "SELECT ual.*, u.first_name, u.last_name, u.username, u.email 
             FROM user_activity_log ual 
             LEFT JOIN users u ON ual.user_id = u.id 
             WHERE ual.id = ?",
            [$id]
        );

        if ($logData) {
            $instance->data = $logData;
            return $instance;
        }

        return null;
    }

    /**
     * Get all activity logs with pagination
     */
    public static function paginate($page = 1, $perPage = 25, $filters = [])
    {
        $instance = new static();
        $offset = ($page - 1) * $perPage;

        $where = [];
        $params = [];

        // Apply filters
        if (isset($filters['user_id']) && $filters['user_id']) {
            $where[] = 'ual.user_id = ?';
            $params[] = $filters['user_id'];
        }

        if (isset($filters['action']) && $filters['action']) {
            $where[] = 'ual.action = ?';
            $params[] = $filters['action'];
        }

        if (isset($filters['date_from']) && $filters['date_from']) {
            $where[] = 'ual.created_at >= ?';
            $params[] = $filters['date_from'];
        }

        if (isset($filters['date_to']) && $filters['date_to']) {
            $where[] = 'ual.created_at <= ?';
            $params[] = $filters['date_to'];
        }

        if (isset($filters['ip_address']) && $filters['ip_address']) {
            $where[] = 'ual.ip_address = ?';
            $params[] = $filters['ip_address'];
        }

        if (isset($filters['search']) && $filters['search']) {
            $where[] = '(ual.description LIKE ? OR ual.action LIKE ? OR u.username LIKE ? OR u.email LIKE ?)';
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        $whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';

        // Get total count
        $countSql = "SELECT COUNT(*) as total 
                     FROM user_activity_log ual 
                     LEFT JOIN users u ON ual.user_id = u.id 
                     {$whereClause}";
        $totalResult = $instance->db->selectOne($countSql, $params);
        $total = $totalResult['total'];

        // Get data
        $sql = "SELECT ual.*, u.first_name, u.last_name, u.username, u.email 
                FROM user_activity_log ual 
                LEFT JOIN users u ON ual.user_id = u.id 
                {$whereClause}
                ORDER BY ual.created_at DESC 
                LIMIT ? OFFSET ?";
        
        $params[] = $perPage;
        $params[] = $offset;

        $logsData = $instance->db->select($sql, $params);

        $logs = [];
        foreach ($logsData as $logData) {
            $log = new static();
            $log->data = $logData;
            $logs[] = $log;
        }

        return [
            'data' => $logs,
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => ceil($total / $perPage),
            'from' => $offset + 1,
            'to' => min($offset + $perPage, $total)
        ];
    }

    /**
     * Get activity logs by user
     */
    public static function getByUser($userId, $limit = 50)
    {
        $instance = new static();
        $logsData = $instance->db->select(
            "SELECT ual.*, u.first_name, u.last_name, u.username, u.email 
             FROM user_activity_log ual 
             LEFT JOIN users u ON ual.user_id = u.id 
             WHERE ual.user_id = ? 
             ORDER BY ual.created_at DESC 
             LIMIT ?",
            [$userId, $limit]
        );

        $logs = [];
        foreach ($logsData as $logData) {
            $log = new static();
            $log->data = $logData;
            $logs[] = $log;
        }

        return $logs;
    }

    /**
     * Get recent activities
     */
    public static function getRecent($limit = 10)
    {
        $instance = new static();
        $logsData = $instance->db->select(
            "SELECT ual.*, u.first_name, u.last_name, u.username, u.email 
             FROM user_activity_log ual 
             LEFT JOIN users u ON ual.user_id = u.id 
             ORDER BY ual.created_at DESC 
             LIMIT ?",
            [$limit]
        );

        $logs = [];
        foreach ($logsData as $logData) {
            $log = new static();
            $log->data = $logData;
            $logs[] = $log;
        }

        return $logs;
    }

    /**
     * Get activity statistics
     */
    public static function getStatistics($days = 7)
    {
        $instance = new static();
        
        // Activity count by day
        $dailyStats = $instance->db->select(
            "SELECT DATE(created_at) as date, COUNT(*) as count 
             FROM user_activity_log 
             WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
             GROUP BY DATE(created_at) 
             ORDER BY date DESC",
            [$days]
        );

        // Activity count by action
        $actionStats = $instance->db->select(
            "SELECT action, COUNT(*) as count 
             FROM user_activity_log 
             WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
             GROUP BY action 
             ORDER BY count DESC 
             LIMIT 10",
            [$days]
        );

        // Top active users
        $userStats = $instance->db->select(
            "SELECT u.id, u.first_name, u.last_name, u.username, COUNT(ual.id) as count 
             FROM users u 
             INNER JOIN user_activity_log ual ON u.id = ual.user_id 
             WHERE ual.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
             GROUP BY u.id 
             ORDER BY count DESC 
             LIMIT 10",
            [$days]
        );

        return [
            'daily' => $dailyStats,
            'actions' => $actionStats,
            'users' => $userStats
        ];
    }

    /**
     * Get unique actions
     */
    public static function getUniqueActions()
    {
        $instance = new static();
        $actions = $instance->db->select(
            "SELECT DISTINCT action FROM user_activity_log ORDER BY action"
        );

        return array_column($actions, 'action');
    }

    /**
     * Delete old logs
     */
    public static function deleteOld($days = 90)
    {
        $instance = new static();
        return $instance->db->execute(
            "DELETE FROM user_activity_log WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)",
            [$days]
        );
    }

    /**
     * Get properties as array
     */
    public function getProperties()
    {
        $properties = $this->data['properties'] ?? '{}';
        return json_decode($properties, true) ?: [];
    }

    /**
     * Get user information
     */
    public function getUser()
    {
        if (!$this->getUserId()) {
            return null;
        }

        return [
            'id' => $this->getUserId(),
            'name' => trim($this->data['first_name'] . ' ' . $this->data['last_name']),
            'username' => $this->data['username'],
            'email' => $this->data['email']
        ];
    }

    /**
     * Format created date
     */
    public function getFormattedDate($format = 'Y-m-d H:i:s')
    {
        return date($format, strtotime($this->getCreatedAt()));
    }

    /**
     * Get time ago
     */
    public function getTimeAgo()
    {
        $time = strtotime($this->getCreatedAt());
        $diff = time() - $time;

        if ($diff < 60) {
            return __('Just now');
        } elseif ($diff < 3600) {
            $minutes = floor($diff / 60);
            return __(':count minutes ago', ['count' => $minutes]);
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return __(':count hours ago', ['count' => $hours]);
        } else {
            $days = floor($diff / 86400);
            return __(':count days ago', ['count' => $days]);
        }
    }

    // Getters
    public function getId() { return $this->data['id'] ?? null; }
    public function getUserId() { return $this->data['user_id'] ?? null; }
    public function getAction() { return $this->data['action'] ?? null; }
    public function getDescription() { return $this->data['description'] ?? null; }
    public function getIpAddress() { return $this->data['ip_address'] ?? null; }
    public function getUserAgent() { return $this->data['user_agent'] ?? null; }
    public function getCreatedAt() { return $this->data['created_at'] ?? null; }

    /**
     * Convert to array
     */
    public function toArray()
    {
        return $this->data;
    }

    /**
     * Magic getter
     */
    public function __get($key)
    {
        return $this->data[$key] ?? null;
    }

    /**
     * Magic isset
     */
    public function __isset($key)
    {
        return isset($this->data[$key]);
    }
}
