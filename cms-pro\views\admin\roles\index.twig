{% extends "layouts/admin.twig" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="roles-index-container">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ title }}</h1>
            <p class="text-muted mb-0">{{ __('Manage user roles and their permissions') }}</p>
        </div>
        <div>
            {% if auth().can('roles.create') %}
            <a href="{{ url('/admin/roles/create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>{{ __('New Role') }}
            </a>
            {% endif %}
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon bg-primary">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="stat-value">{{ stats.total }}</div>
                            <div class="stat-label">{{ __('Total Roles') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon bg-success">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="stat-value">{{ stats.active }}</div>
                            <div class="stat-label">{{ __('Active Roles') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon bg-info">
                                <i class="fas fa-cog"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="stat-value">{{ stats.system }}</div>
                            <div class="stat-label">{{ __('System Roles') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon bg-warning">
                                <i class="fas fa-user-cog"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="stat-value">{{ stats.custom }}</div>
                            <div class="stat-label">{{ __('Custom Roles') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ url('/admin/roles') }}" class="row g-3" id="filters-form">
                <div class="col-md-4">
                    <label for="search" class="form-label">{{ __('Search') }}</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ current_filters.search }}" 
                           placeholder="{{ __('Search roles...') }}">
                </div>
                
                <div class="col-md-2">
                    <label for="status" class="form-label">{{ __('Status') }}</label>
                    <select class="form-select" id="status" name="status">
                        <option value="all" {{ current_filters.status == 'all' ? 'selected' : '' }}>
                            {{ __('All Statuses') }}
                        </option>
                        {% for key, label in statuses %}
                        <option value="{{ key }}" {{ current_filters.status == key ? 'selected' : '' }}>
                            {{ label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="type" class="form-label">{{ __('Type') }}</label>
                    <select class="form-select" id="type" name="type">
                        <option value="all" {{ current_filters.type == 'all' ? 'selected' : '' }}>
                            {{ __('All Types') }}
                        </option>
                        <option value="system" {{ current_filters.type == 'system' ? 'selected' : '' }}>
                            {{ __('System Roles') }}
                        </option>
                        <option value="custom" {{ current_filters.type == 'custom' ? 'selected' : '' }}>
                            {{ __('Custom Roles') }}
                        </option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search me-1"></i>{{ __('Filter') }}
                        </button>
                        <a href="{{ url('/admin/roles') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>{{ __('Clear') }}
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Roles Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-body p-0">
            {% if roles and roles|length > 0 %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>{{ __('Role') }}</th>
                            <th width="100">{{ __('Level') }}</th>
                            <th width="120">{{ __('Type') }}</th>
                            <th width="120">{{ __('Status') }}</th>
                            <th width="100">{{ __('Users') }}</th>
                            <th width="200">{{ __('Permissions') }}</th>
                            <th width="120">{{ __('Actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for role in roles %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="role-icon me-3">
                                        <div class="role-level-badge level-{{ role.level }}">
                                            {{ role.level }}
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold">
                                            {% if auth().can('roles.edit') %}
                                            <a href="{{ url('/admin/roles/' ~ role.id ~ '/edit') }}" 
                                               class="text-decoration-none">
                                                {{ role.name }}
                                            </a>
                                            {% else %}
                                            {{ role.name }}
                                            {% endif %}
                                        </div>
                                        {% if role.description %}
                                        <div class="small text-muted">{{ role.description }}</div>
                                        {% endif %}
                                        <div class="small text-muted">
                                            <code>{{ role.slug }}</code>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ role.level }}</span>
                            </td>
                            <td>
                                {% if role.is_system %}
                                <span class="badge bg-info">
                                    <i class="fas fa-cog me-1"></i>{{ __('System') }}
                                </span>
                                {% else %}
                                <span class="badge bg-primary">
                                    <i class="fas fa-user-cog me-1"></i>{{ __('Custom') }}
                                </span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-{{ role.status == 'active' ? 'success' : 'warning' }}">
                                    {{ statuses[role.status] }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-light text-dark">
                                    {{ role.users_count }} {{ __('users') }}
                                </span>
                            </td>
                            <td>
                                {% if role.permission_names %}
                                <div class="permissions-preview">
                                    {% set permission_list = role.permission_names|split(', ') %}
                                    {% for permission in permission_list|slice(0, 3) %}
                                    <span class="badge bg-light text-dark me-1 mb-1">{{ permission }}</span>
                                    {% endfor %}
                                    {% if permission_list|length > 3 %}
                                    <span class="badge bg-secondary">+{{ permission_list|length - 3 }} {{ __('more') }}</span>
                                    {% endif %}
                                </div>
                                {% else %}
                                <span class="text-muted">{{ __('No permissions') }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    {% if auth().can('roles.edit') %}
                                    <a href="{{ url('/admin/roles/' ~ role.id ~ '/edit') }}" 
                                       class="btn btn-outline-primary" 
                                       data-bs-toggle="tooltip" 
                                       title="{{ __('Edit') }}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                    
                                    {% if auth().can('roles.delete') and not role.is_system and role.users_count == 0 %}
                                    <button type="button" 
                                            class="btn btn-outline-danger delete-role" 
                                            data-role-id="{{ role.id }}"
                                            data-role-name="{{ role.name }}"
                                            data-bs-toggle="tooltip" 
                                            title="{{ __('Delete') }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-shield-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">{{ __('No roles found') }}</h5>
                <p class="text-muted">
                    {% if current_filters.search or current_filters.status != 'all' or current_filters.type != 'all' %}
                        {{ __('Try adjusting your filters or') }}
                        <a href="{{ url('/admin/roles') }}">{{ __('clear all filters') }}</a>
                    {% else %}
                        {{ __('Get started by creating your first custom role.') }}
                    {% endif %}
                </p>
                {% if auth().can('roles.create') %}
                <a href="{{ url('/admin/roles/create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>{{ __('Create First Role') }}
                </a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Confirm Delete') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>{{ __('Are you sure you want to delete this role?') }}</p>
                <p><strong class="role-name-placeholder"></strong></p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {{ __('This action cannot be undone. All permissions associated with this role will be removed.') }}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ __('Cancel') }}
                </button>
                <button type="button" class="btn btn-danger" id="confirmDelete">
                    <i class="fas fa-trash me-2"></i>{{ __('Delete Role') }}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
/**
 * Roles Index Management
 * Enhanced with security and user experience features
 */
class RolesIndex {
    constructor() {
        this.deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        this.currentDeleteId = null;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupTooltips();
        this.setupAutoSubmit();
    }

    setupEventListeners() {
        // Delete buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.delete-role') || e.target.closest('.delete-role')) {
                e.preventDefault();
                const button = e.target.matches('.delete-role') ? e.target : e.target.closest('.delete-role');
                this.showDeleteModal(button.dataset.roleId, button.dataset.roleName);
            }
        });

        // Confirm delete
        document.getElementById('confirmDelete').addEventListener('click', () => {
            this.deleteRole(this.currentDeleteId);
        });
    }

    setupTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    setupAutoSubmit() {
        // Auto-submit filters on change (with debounce)
        let timeout;
        const searchInput = document.getElementById('search');

        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    document.getElementById('filters-form').submit();
                }, 500);
            });
        }

        // Auto-submit on select changes
        const selects = document.querySelectorAll('#status, #type');
        selects.forEach(select => {
            select.addEventListener('change', () => {
                document.getElementById('filters-form').submit();
            });
        });
    }

    showDeleteModal(roleId, roleName) {
        this.currentDeleteId = roleId;
        document.querySelector('.role-name-placeholder').textContent = roleName;
        this.deleteModal.show();
    }

    async deleteRole(roleId) {
        try {
            const response = await fetch(`/admin/roles/${roleId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': this.getCsrfToken()
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');
                this.deleteModal.hide();

                // Remove row from table
                const row = document.querySelector(`button[data-role-id="${roleId}"]`).closest('tr');
                if (row) {
                    row.remove();
                }

                // Update statistics
                this.updateStatistics();

            } else {
                this.showNotification(result.message, 'error');
            }

        } catch (error) {
            console.error('Delete error:', error);
            this.showNotification('An error occurred while deleting the role.', 'error');
        }
    }

    updateStatistics() {
        // Reload page to update statistics
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    }

    getCsrfToken() {
        return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
        notification.innerHTML = `
            ${this.escapeHtml(message)}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    new RolesIndex();
});
</script>

<style>
/* Roles Index Styles */
.roles-index-container .table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: #718096;
    margin-top: 0.25rem;
}

.role-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.role-level-badge {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.75rem;
    color: white;
}

/* Role level colors */
.role-level-badge.level-100 { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.role-level-badge.level-80 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.role-level-badge.level-60 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.role-level-badge.level-40 { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
.role-level-badge.level-20 { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }

/* Default for other levels */
.role-level-badge:not([class*="level-"]) {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #2d3748;
}

.permissions-preview {
    max-width: 200px;
}

.permissions-preview .badge {
    font-size: 0.7rem;
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.025);
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

/* Status badges */
.badge.bg-success {
    background-color: #28a745 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529;
}

.badge.bg-info {
    background-color: #17a2b8 !important;
}

.badge.bg-primary {
    background-color: #007bff !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .btn-group-sm .btn {
        padding: 0.125rem 0.25rem;
    }

    .stat-value {
        font-size: 1.25rem;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .role-level-badge {
        width: 28px;
        height: 28px;
        font-size: 0.7rem;
    }

    .permissions-preview {
        max-width: 150px;
    }
}

/* Dark mode support */
[data-bs-theme="dark"] .table th {
    color: #adb5bd;
}

[data-bs-theme="dark"] .stat-value {
    color: #e2e8f0;
}

[data-bs-theme="dark"] .stat-label {
    color: #a0aec0;
}

[data-bs-theme="dark"] .badge.bg-warning {
    color: #000;
}

[data-bs-theme="dark"] .badge.bg-light {
    background-color: #495057 !important;
    color: #e2e8f0 !important;
}
</style>
{% endblock %}
