<?php

namespace CmsPro\Controllers\Admin;

use CmsPro\Controllers\BaseController;
use CmsPro\Models\User;
use CmsPro\Models\Role;
use CmsPro\Models\Permission;
use CmsPro\Services\ValidationService;
use CmsPro\Services\SanitizationService;
use CmsPro\Services\ActivityLogger;
use CmsPro\Services\SecurityService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * User Controller for Admin Panel
 * 
 * @package CmsPro\Controllers\Admin
 */
class UserController extends BaseController
{
    private $validationService;
    private $sanitizationService;
    private $activityLogger;
    private $securityService;

    public function __construct()
    {
        parent::__construct();
        
        $this->validationService = new ValidationService();
        $this->sanitizationService = new SanitizationService();
        $this->activityLogger = new ActivityLogger();
        $this->securityService = new SecurityService();
    }

    /**
     * Display users list
     */
    public function index(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('users.view')) {
                return $this->forbidden('You do not have permission to view users.');
            }

            // Get filters
            $status = $request->query->get('status', 'all');
            $role = $request->query->get('role', '');
            $search = $request->query->get('search', '');
            $perPage = (int) $request->query->get('per_page', 20);
            $page = (int) $request->query->get('page', 1);

            // Build query
            $db = app()->getDatabase();
            $whereConditions = [];
            $params = [];

            // Apply filters
            if ($status !== 'all') {
                $whereConditions[] = "u.status = ?";
                $params[] = $status;
            }

            if ($role) {
                $whereConditions[] = "EXISTS (SELECT 1 FROM user_roles ur INNER JOIN roles r ON ur.role_id = r.id WHERE ur.user_id = u.id AND r.slug = ?)";
                $params[] = $role;
            }

            if ($search) {
                $search = $this->sanitizationService->sanitizeInput($search);
                $whereConditions[] = "(u.first_name LIKE ? OR u.last_name LIKE ? OR u.email LIKE ? OR u.username LIKE ?)";
                $searchTerm = "%{$search}%";
                $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
            }

            $whereClause = $whereConditions ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

            // Get total count
            $totalQuery = "SELECT COUNT(*) as total FROM users u {$whereClause}";
            $totalResult = $db->selectOne($totalQuery, $params);
            $total = $totalResult['total'];

            // Calculate pagination
            $offset = ($page - 1) * $perPage;
            $totalPages = ceil($total / $perPage);

            // Get users with roles
            $usersQuery = "
                SELECT u.*, 
                       GROUP_CONCAT(r.name SEPARATOR ', ') as role_names,
                       GROUP_CONCAT(r.slug SEPARATOR ',') as role_slugs
                FROM users u 
                LEFT JOIN user_roles ur ON u.id = ur.user_id 
                LEFT JOIN roles r ON ur.role_id = r.id 
                {$whereClause}
                GROUP BY u.id 
                ORDER BY u.created_at DESC 
                LIMIT {$perPage} OFFSET {$offset}
            ";

            $users = $db->select($usersQuery, $params);

            // Get available roles for filter
            $roles = Role::all();

            // Get user statuses
            $statuses = User::getStatuses();

            // Log activity
            $this->activityLogger->log('users_viewed', [
                'user_id' => auth()->id(),
                'filters' => compact('status', 'role', 'search')
            ]);

            $data = [
                'title' => __('Users'),
                'users' => $users,
                'roles' => $roles,
                'statuses' => $statuses,
                'current_filters' => compact('status', 'role', 'search'),
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $total,
                    'pages' => $totalPages
                ]
            ];

            return $this->view('admin/users/index.twig', $data);

        } catch (\Exception $e) {
            $this->logError('users_index_error', $e, $request);
            $this->flashError(__('An error occurred while loading users.'));
            return $this->redirectToRoute('admin.dashboard');
        }
    }

    /**
     * Show create user form
     */
    public function create(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('users.create')) {
                return $this->forbidden('You do not have permission to create users.');
            }

            // Get available roles
            $roles = Role::all();

            // Get available permissions (for direct assignment)
            $permissions = Permission::getGroupedPermissions();

            $data = [
                'title' => __('Create New User'),
                'user' => new User(), // Empty user for form
                'roles' => $roles,
                'permissions' => $permissions,
                'statuses' => User::getStatuses()
            ];

            return $this->view('admin/users/create.twig', $data);

        } catch (\Exception $e) {
            $this->logError('user_create_form_error', $e, $request);
            $this->flashError(__('An error occurred while loading the create form.'));
            return $this->redirectToRoute('admin.users.index');
        }
    }

    /**
     * Store new user
     */
    public function store(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('users.create')) {
                return $this->forbidden('You do not have permission to create users.');
            }

            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Invalid CSRF token'
                    ], 403);
                }
                
                $this->flashError(__('Security token mismatch. Please try again.'));
                return $this->back();
            }

            // Validate input
            $rules = [
                'first_name' => 'required|string|max:100',
                'last_name' => 'required|string|max:100',
                'email' => 'required|email|max:255|unique:users,email',
                'username' => 'nullable|string|max:50|unique:users,username',
                'password' => 'required|string|min:8|max:255',
                'password_confirmation' => 'required|same:password',
                'status' => 'required|in:' . implode(',', array_keys(User::getStatuses())),
                'roles' => 'nullable|array',
                'permissions' => 'nullable|array'
            ];

            $data = $this->validationService->validate($request->request->all(), $rules);

            // Sanitize input
            $data['first_name'] = $this->sanitizationService->sanitizeInput($data['first_name']);
            $data['last_name'] = $this->sanitizationService->sanitizeInput($data['last_name']);
            $data['email'] = $this->sanitizationService->sanitizeEmail($data['email']);
            
            if (!empty($data['username'])) {
                $data['username'] = $this->sanitizationService->sanitizeInput($data['username']);
            }

            // Hash password
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
            
            // Remove confirmation field
            unset($data['password_confirmation']);

            // Set additional fields
            $data['created_by'] = auth()->id();
            $data['email_verified_at'] = now(); // Auto-verify for admin created users

            // Create user
            $user = User::create($data);

            if ($user) {
                // Assign roles
                if (!empty($data['roles'])) {
                    foreach ($data['roles'] as $roleId) {
                        $user->assignRole($roleId);
                    }
                }

                // Assign direct permissions
                if (!empty($data['permissions'])) {
                    foreach ($data['permissions'] as $permissionId) {
                        $user->assignPermission($permissionId);
                    }
                }

                // Log activity
                $this->activityLogger->log('user_created', [
                    'user_id' => auth()->id(),
                    'created_user_id' => $user->id,
                    'created_user_email' => $user->email,
                    'roles' => $data['roles'] ?? [],
                    'permissions' => $data['permissions'] ?? []
                ]);

                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => true,
                        'message' => __('User created successfully.'),
                        'redirect' => route('admin.users.edit', ['id' => $user->id])
                    ]);
                }

                $this->flashSuccess(__('User created successfully.'));
                
                // Redirect based on action
                $action = $request->request->get('action', 'save');
                if ($action === 'save_and_continue') {
                    return $this->redirectToRoute('admin.users.edit', ['id' => $user->id]);
                } elseif ($action === 'save_and_new') {
                    return $this->redirectToRoute('admin.users.create');
                }
                
                return $this->redirectToRoute('admin.users.index');
            }

            throw new \Exception('Failed to create user');

        } catch (\Exception $e) {
            $this->logError('user_store_error', $e, $request);
            
            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => __('An error occurred while creating the user.')
                ], 500);
            }
            
            $this->flashError(__('An error occurred while creating the user.'));
            return $this->back();
        }
    }

    /**
     * Validate CSRF token
     */
    private function validateCsrfToken(Request $request)
    {
        $token = $request->request->get('_token') ?: $request->headers->get('X-CSRF-TOKEN');
        
        if (!$token) {
            return false;
        }

        $sessionToken = session()->get('_token');
        return $sessionToken && hash_equals($sessionToken, $token);
    }

    /**
     * Show edit user form
     */
    public function edit(Request $request, $id)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('users.edit')) {
                return $this->forbidden('You do not have permission to edit users.');
            }

            // Find user
            $user = User::find($id);
            if (!$user) {
                $this->flashError(__('User not found.'));
                return $this->redirectToRoute('admin.users.index');
            }

            // Check if user can edit this user (prevent editing higher level users)
            if (!$this->canEditUser($user)) {
                return $this->forbidden('You cannot edit this user.');
            }

            // Get user's current roles and permissions
            $userRoles = $user->getRoles();
            $userPermissions = $user->getPermissions();

            // Get available roles and permissions
            $roles = Role::all();
            $permissions = Permission::getGroupedPermissions();

            // Get user activity log
            $activityLog = $user->getActivityLog(20);

            $data = [
                'title' => __('Edit User: :name', ['name' => $user->getFullName()]),
                'user' => $user,
                'user_roles' => array_column($userRoles, 'id'),
                'user_permissions' => array_column($userPermissions, 'id'),
                'roles' => $roles,
                'permissions' => $permissions,
                'statuses' => User::getStatuses(),
                'activity_log' => $activityLog
            ];

            return $this->view('admin/users/edit.twig', $data);

        } catch (\Exception $e) {
            $this->logError('user_edit_form_error', $e, $request);
            $this->flashError(__('An error occurred while loading the edit form.'));
            return $this->redirectToRoute('admin.users.index');
        }
    }

    /**
     * Update user
     */
    public function update(Request $request, $id)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('users.edit')) {
                return $this->forbidden('You do not have permission to edit users.');
            }

            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Invalid CSRF token'
                    ], 403);
                }

                $this->flashError(__('Security token mismatch. Please try again.'));
                return $this->back();
            }

            // Find user
            $user = User::find($id);
            if (!$user) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'User not found'
                    ], 404);
                }

                $this->flashError(__('User not found.'));
                return $this->redirectToRoute('admin.users.index');
            }

            // Check if user can edit this user
            if (!$this->canEditUser($user)) {
                return $this->forbidden('You cannot edit this user.');
            }

            // Validate input
            $rules = [
                'first_name' => 'required|string|max:100',
                'last_name' => 'required|string|max:100',
                'email' => 'required|email|max:255|unique:users,email,' . $user->id,
                'username' => 'nullable|string|max:50|unique:users,username,' . $user->id,
                'password' => 'nullable|string|min:8|max:255',
                'password_confirmation' => 'nullable|same:password',
                'status' => 'required|in:' . implode(',', array_keys(User::getStatuses())),
                'roles' => 'nullable|array',
                'permissions' => 'nullable|array'
            ];

            $data = $this->validationService->validate($request->request->all(), $rules);

            // Sanitize input
            $data['first_name'] = $this->sanitizationService->sanitizeInput($data['first_name']);
            $data['last_name'] = $this->sanitizationService->sanitizeInput($data['last_name']);
            $data['email'] = $this->sanitizationService->sanitizeEmail($data['email']);

            if (!empty($data['username'])) {
                $data['username'] = $this->sanitizationService->sanitizeInput($data['username']);
            }

            // Handle password update
            if (!empty($data['password'])) {
                $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
            } else {
                unset($data['password']);
            }

            // Remove confirmation field
            unset($data['password_confirmation']);

            // Update user
            $user->update($data);

            // Update roles (if user has permission)
            if (auth()->can('users.manage_roles') && isset($data['roles'])) {
                $this->updateUserRoles($user, $data['roles']);
            }

            // Update permissions (if user has permission)
            if (auth()->can('users.manage_permissions') && isset($data['permissions'])) {
                $this->updateUserPermissions($user, $data['permissions']);
            }

            // Log activity
            $this->activityLogger->log('user_updated', [
                'user_id' => auth()->id(),
                'updated_user_id' => $user->id,
                'updated_user_email' => $user->email,
                'changes' => array_keys($data)
            ]);

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => true,
                    'message' => __('User updated successfully.'),
                    'redirect' => route('admin.users.edit', ['id' => $user->id])
                ]);
            }

            $this->flashSuccess(__('User updated successfully.'));

            // Redirect based on action
            $action = $request->request->get('action', 'save');
            if ($action === 'save_and_continue') {
                return $this->redirectToRoute('admin.users.edit', ['id' => $user->id]);
            } elseif ($action === 'save_and_new') {
                return $this->redirectToRoute('admin.users.create');
            }

            return $this->redirectToRoute('admin.users.index');

        } catch (\Exception $e) {
            $this->logError('user_update_error', $e, $request);

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => __('An error occurred while updating the user.')
                ], 500);
            }

            $this->flashError(__('An error occurred while updating the user.'));
            return $this->back();
        }
    }

    /**
     * Delete user
     */
    public function destroy(Request $request, $id)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('users.delete')) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'You do not have permission to delete users.'
                    ], 403);
                }

                return $this->forbidden('You do not have permission to delete users.');
            }

            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Invalid CSRF token'
                    ], 403);
                }

                $this->flashError(__('Security token mismatch. Please try again.'));
                return $this->back();
            }

            // Find user
            $user = User::find($id);
            if (!$user) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'User not found'
                    ], 404);
                }

                $this->flashError(__('User not found.'));
                return $this->redirectToRoute('admin.users.index');
            }

            // Prevent self-deletion
            if ($user->id == auth()->id()) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'You cannot delete your own account.'
                    ], 400);
                }

                $this->flashError(__('You cannot delete your own account.'));
                return $this->back();
            }

            // Check if user can delete this user
            if (!$this->canEditUser($user)) {
                return $this->forbidden('You cannot delete this user.');
            }

            $userEmail = $user->email;

            // Soft delete (deactivate) instead of hard delete
            $user->update(['status' => 'deleted']);

            // Log activity
            $this->activityLogger->log('user_deleted', [
                'user_id' => auth()->id(),
                'deleted_user_id' => $user->id,
                'deleted_user_email' => $userEmail
            ]);

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => true,
                    'message' => __('User deleted successfully.')
                ]);
            }

            $this->flashSuccess(__('User deleted successfully.'));
            return $this->redirectToRoute('admin.users.index');

        } catch (\Exception $e) {
            $this->logError('user_delete_error', $e, $request);

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => __('An error occurred while deleting the user.')
                ], 500);
            }

            $this->flashError(__('An error occurred while deleting the user.'));
            return $this->back();
        }
    }

    /**
     * Check if current user can edit target user
     */
    private function canEditUser(User $targetUser)
    {
        $currentUser = auth()->user();

        // Super admin can edit anyone
        if ($currentUser->isSuperAdmin()) {
            return true;
        }

        // Cannot edit super admin unless you are super admin
        if ($targetUser->isSuperAdmin()) {
            return false;
        }

        // Admin can edit non-admin users
        if ($currentUser->isAdmin() && !$targetUser->isAdmin()) {
            return true;
        }

        // Users can only edit themselves (if they have edit permission)
        return $currentUser->id === $targetUser->id;
    }

    /**
     * Update user roles
     */
    private function updateUserRoles(User $user, array $roleIds)
    {
        $db = app()->getDatabase();

        // Remove all existing roles
        $db->delete("DELETE FROM user_roles WHERE user_id = ?", [$user->id]);

        // Add new roles
        foreach ($roleIds as $roleId) {
            $user->assignRole($roleId);
        }
    }

    /**
     * Update user permissions
     */
    private function updateUserPermissions(User $user, array $permissionIds)
    {
        $db = app()->getDatabase();

        // Remove all existing direct permissions
        $db->delete("DELETE FROM user_permissions WHERE user_id = ?", [$user->id]);

        // Add new permissions
        foreach ($permissionIds as $permissionId) {
            $user->assignPermission($permissionId);
        }
    }

    /**
     * Log errors
     */
    private function logError($event, \Exception $e, Request $request)
    {
        $logData = [
            'event' => $event,
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'user_id' => auth()->id(),
            'ip_address' => $request->getClientIp(),
            'user_agent' => $request->headers->get('User-Agent'),
            'url' => $request->getUri()
        ];

        error_log(json_encode($logData), 3, storage_path('logs/users.log'));

        if ($this->activityLogger) {
            $this->activityLogger->log($event, $logData);
        }
    }
}
