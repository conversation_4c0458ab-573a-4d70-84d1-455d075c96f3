-- Create field_types table
CREATE TABLE IF NOT EXISTS `field_types` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `slug` varchar(100) NOT NULL,
    `description` text DEFAULT NULL,
    `icon` varchar(50) DEFAULT NULL,
    `component` varchar(100) NOT NULL,
    `validation_rules` json DEFAULT NULL,
    `default_settings` json DEFAULT NULL,
    `is_active` tinyint(1) NOT NULL DEFAULT 1,
    `sort_order` int(11) NOT NULL DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `field_types_slug_unique` (`slug`),
    KEY `field_types_is_active_index` (`is_active`),
    KEY `field_types_sort_order_index` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create field_groups table
CREATE TABLE IF NOT EXISTS `field_groups` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `slug` varchar(100) NOT NULL,
    `description` text DEFAULT NULL,
    `location` varchar(100) NOT NULL DEFAULT 'content',
    `rules` json DEFAULT NULL,
    `settings` json DEFAULT NULL,
    `is_active` tinyint(1) NOT NULL DEFAULT 1,
    `sort_order` int(11) NOT NULL DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `field_groups_slug_unique` (`slug`),
    KEY `field_groups_location_index` (`location`),
    KEY `field_groups_is_active_index` (`is_active`),
    KEY `field_groups_sort_order_index` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create fields table
CREATE TABLE IF NOT EXISTS `fields` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `field_group_id` int(11) NOT NULL,
    `field_type_id` int(11) NOT NULL,
    `name` varchar(100) NOT NULL,
    `slug` varchar(100) NOT NULL,
    `label` varchar(200) NOT NULL,
    `description` text DEFAULT NULL,
    `placeholder` varchar(255) DEFAULT NULL,
    `default_value` text DEFAULT NULL,
    `validation_rules` json DEFAULT NULL,
    `settings` json DEFAULT NULL,
    `is_required` tinyint(1) NOT NULL DEFAULT 0,
    `is_active` tinyint(1) NOT NULL DEFAULT 1,
    `sort_order` int(11) NOT NULL DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `fields_slug_group_unique` (`slug`, `field_group_id`),
    KEY `fields_field_group_id_foreign` (`field_group_id`),
    KEY `fields_field_type_id_foreign` (`field_type_id`),
    KEY `fields_is_active_index` (`is_active`),
    KEY `fields_sort_order_index` (`sort_order`),
    CONSTRAINT `fields_field_group_id_foreign` FOREIGN KEY (`field_group_id`) REFERENCES `field_groups` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fields_field_type_id_foreign` FOREIGN KEY (`field_type_id`) REFERENCES `field_types` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create field_values table
CREATE TABLE IF NOT EXISTS `field_values` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `field_id` int(11) NOT NULL,
    `entity_type` varchar(100) NOT NULL,
    `entity_id` int(11) NOT NULL,
    `value` longtext DEFAULT NULL,
    `meta` json DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `field_values_unique` (`field_id`, `entity_type`, `entity_id`),
    KEY `field_values_field_id_foreign` (`field_id`),
    KEY `field_values_entity_index` (`entity_type`, `entity_id`),
    CONSTRAINT `field_values_field_id_foreign` FOREIGN KEY (`field_id`) REFERENCES `fields` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create field_options table (for select, radio, checkbox fields)
CREATE TABLE IF NOT EXISTS `field_options` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `field_id` int(11) NOT NULL,
    `label` varchar(255) NOT NULL,
    `value` varchar(255) NOT NULL,
    `is_default` tinyint(1) NOT NULL DEFAULT 0,
    `sort_order` int(11) NOT NULL DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `field_options_field_id_foreign` (`field_id`),
    KEY `field_options_sort_order_index` (`sort_order`),
    CONSTRAINT `field_options_field_id_foreign` FOREIGN KEY (`field_id`) REFERENCES `fields` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create content table (main content entities)
CREATE TABLE IF NOT EXISTS `content` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `uuid` varchar(36) NOT NULL,
    `title` varchar(255) NOT NULL,
    `slug` varchar(255) NOT NULL,
    `content_type` varchar(100) NOT NULL DEFAULT 'page',
    `status` enum('draft','published','archived','scheduled') NOT NULL DEFAULT 'draft',
    `featured_image` varchar(255) DEFAULT NULL,
    `excerpt` text DEFAULT NULL,
    `meta_title` varchar(255) DEFAULT NULL,
    `meta_description` text DEFAULT NULL,
    `meta_keywords` text DEFAULT NULL,
    `author_id` int(11) NOT NULL,
    `parent_id` int(11) DEFAULT NULL,
    `template` varchar(100) DEFAULT NULL,
    `published_at` timestamp NULL DEFAULT NULL,
    `scheduled_at` timestamp NULL DEFAULT NULL,
    `views_count` int(11) NOT NULL DEFAULT 0,
    `sort_order` int(11) NOT NULL DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `content_uuid_unique` (`uuid`),
    UNIQUE KEY `content_slug_unique` (`slug`),
    KEY `content_author_id_foreign` (`author_id`),
    KEY `content_parent_id_foreign` (`parent_id`),
    KEY `content_status_index` (`status`),
    KEY `content_content_type_index` (`content_type`),
    KEY `content_published_at_index` (`published_at`),
    KEY `content_deleted_at_index` (`deleted_at`),
    CONSTRAINT `content_author_id_foreign` FOREIGN KEY (`author_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT,
    CONSTRAINT `content_parent_id_foreign` FOREIGN KEY (`parent_id`) REFERENCES `content` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create content_categories table
CREATE TABLE IF NOT EXISTS `content_categories` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `slug` varchar(100) NOT NULL,
    `description` text DEFAULT NULL,
    `parent_id` int(11) DEFAULT NULL,
    `color` varchar(7) DEFAULT NULL,
    `icon` varchar(50) DEFAULT NULL,
    `sort_order` int(11) NOT NULL DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `content_categories_slug_unique` (`slug`),
    KEY `content_categories_parent_id_foreign` (`parent_id`),
    KEY `content_categories_sort_order_index` (`sort_order`),
    CONSTRAINT `content_categories_parent_id_foreign` FOREIGN KEY (`parent_id`) REFERENCES `content_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create content_category_relations table
CREATE TABLE IF NOT EXISTS `content_category_relations` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `content_id` int(11) NOT NULL,
    `category_id` int(11) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `content_category_relations_unique` (`content_id`, `category_id`),
    KEY `content_category_relations_content_id_foreign` (`content_id`),
    KEY `content_category_relations_category_id_foreign` (`category_id`),
    CONSTRAINT `content_category_relations_content_id_foreign` FOREIGN KEY (`content_id`) REFERENCES `content` (`id`) ON DELETE CASCADE,
    CONSTRAINT `content_category_relations_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `content_categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create content_tags table
CREATE TABLE IF NOT EXISTS `content_tags` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `slug` varchar(100) NOT NULL,
    `description` text DEFAULT NULL,
    `color` varchar(7) DEFAULT NULL,
    `usage_count` int(11) NOT NULL DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `content_tags_slug_unique` (`slug`),
    KEY `content_tags_usage_count_index` (`usage_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create content_tag_relations table
CREATE TABLE IF NOT EXISTS `content_tag_relations` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `content_id` int(11) NOT NULL,
    `tag_id` int(11) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `content_tag_relations_unique` (`content_id`, `tag_id`),
    KEY `content_tag_relations_content_id_foreign` (`content_id`),
    KEY `content_tag_relations_tag_id_foreign` (`tag_id`),
    CONSTRAINT `content_tag_relations_content_id_foreign` FOREIGN KEY (`content_id`) REFERENCES `content` (`id`) ON DELETE CASCADE,
    CONSTRAINT `content_tag_relations_tag_id_foreign` FOREIGN KEY (`tag_id`) REFERENCES `content_tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
