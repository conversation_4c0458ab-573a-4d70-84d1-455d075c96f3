{% extends "layouts/base.twig" %}

{% block title %}{{ __('Two-Factor Authentication') }} - {{ app.name }}{% endblock %}

{% block body_class %}auth-page{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-lg border-0 mt-5">
                <div class="card-header bg-warning text-dark text-center py-4">
                    <h3 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        {{ __('Two-Factor Authentication') }}
                    </h3>
                </div>
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <p class="text-muted">
                            {{ __('Please enter your authentication code to continue.') }}
                        </p>
                    </div>

                    <form method="POST" action="{{ url('/2fa') }}" class="needs-validation" novalidate>
                        {{ csrf_field() | raw }}
                        
                        <div class="mb-4">
                            <label for="code" class="form-label">{{ __('Authentication Code') }}</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-key"></i>
                                </span>
                                <input 
                                    type="text" 
                                    class="form-control text-center" 
                                    id="code" 
                                    name="code" 
                                    required 
                                    autofocus
                                    autocomplete="one-time-code"
                                    placeholder="000000"
                                    maxlength="8"
                                    pattern="[0-9A-Za-z]{6,8}"
                                >
                                <div class="invalid-feedback">
                                    {{ __('Please enter a valid authentication code.') }}
                                </div>
                            </div>
                            <div class="form-text">
                                {{ __('Enter the 6-digit code from your authenticator app or an 8-character backup code.') }}
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-warning btn-lg">
                                <i class="fas fa-check me-2"></i>
                                {{ __('Verify') }}
                            </button>
                        </div>
                    </form>
                </div>
                
                <div class="card-footer text-center py-3">
                    <div class="row">
                        <div class="col">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                {% if backup_codes_count > 0 %}
                                    {{ __('You have :count backup codes remaining.', {count: backup_codes_count}) }}
                                {% else %}
                                    {{ __('No backup codes remaining.') }}
                                {% endif %}
                            </small>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col">
                            <form method="POST" action="{{ url('/logout') }}" class="d-inline">
                                {{ csrf_field() | raw }}
                                <button type="submit" class="btn btn-link btn-sm text-muted">
                                    <i class="fas fa-sign-out-alt me-1"></i>
                                    {{ __('Logout') }}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Help Section -->
            <div class="card mt-3 border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        {{ __('Need Help?') }}
                    </h6>
                </div>
                <div class="card-body py-3">
                    <div class="accordion" id="helpAccordion">
                        <div class="accordion-item border-0">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#helpApp">
                                    {{ __('Using Authenticator App') }}
                                </button>
                            </h2>
                            <div id="helpApp" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
                                <div class="accordion-body">
                                    <small class="text-muted">
                                        {{ __('Open your authenticator app (Google Authenticator, Authy, etc.) and enter the 6-digit code shown for this account.') }}
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item border-0">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#helpBackup">
                                    {{ __('Using Backup Codes') }}
                                </button>
                            </h2>
                            <div id="helpBackup" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
                                <div class="accordion-body">
                                    <small class="text-muted">
                                        {{ __('If you don\'t have access to your authenticator app, you can use one of your 8-character backup codes instead.') }}
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item border-0">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#helpLost">
                                    {{ __('Lost Access?') }}
                                </button>
                            </h2>
                            <div id="helpLost" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
                                <div class="accordion-body">
                                    <small class="text-muted">
                                        {{ __('If you\'ve lost access to both your authenticator app and backup codes, please contact your system administrator for assistance.') }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
// Auto-format code input
document.getElementById('code').addEventListener('input', function(e) {
    let value = e.target.value.replace(/[^0-9A-Za-z]/g, '');
    
    // Limit to 8 characters
    if (value.length > 8) {
        value = value.substring(0, 8);
    }
    
    e.target.value = value.toUpperCase();
});

// Auto-submit when 6 or 8 characters are entered
document.getElementById('code').addEventListener('input', function(e) {
    const value = e.target.value;
    if (value.length === 6 || value.length === 8) {
        // Small delay to allow user to see the complete code
        setTimeout(() => {
            if (e.target.value === value) { // Make sure value hasn't changed
                e.target.form.submit();
            }
        }, 500);
    }
});

// Focus on code input when page loads
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('code').focus();
});
</script>
{% endblock %}

{% block styles %}
{{ parent() }}
<style>
.auth-page .card-header.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%) !important;
}

.auth-page .btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
    border: none;
    color: #212529;
    font-weight: 600;
}

.auth-page .btn-warning:hover {
    background: linear-gradient(135deg, #ff8f00 0%, #f57c00 100%);
    color: #212529;
    transform: translateY(-1px);
}

#code {
    font-family: 'Courier New', monospace;
    font-size: 1.2rem;
    letter-spacing: 0.2em;
    font-weight: bold;
}

.accordion-button {
    font-size: 0.9rem;
    padding: 0.75rem 1rem;
}

.accordion-button:not(.collapsed) {
    background-color: #e3f2fd;
    color: #1976d2;
}

.accordion-body {
    padding: 0.75rem 1rem;
}
</style>
{% endblock %}
