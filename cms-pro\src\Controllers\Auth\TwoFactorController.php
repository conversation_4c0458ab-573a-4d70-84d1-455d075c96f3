<?php

namespace CmsPro\Controllers\Auth;

use CmsPro\Controllers\BaseController;
use CmsPro\Services\TwoFactorService;
use Symfony\Component\HttpFoundation\Request;

/**
 * Two-Factor Authentication Controller
 * 
 * @package CmsPro\Controllers\Auth
 */
class TwoFactorController extends BaseController
{
    private $twoFactorService;

    public function __construct()
    {
        parent::__construct();
        $this->twoFactorService = new TwoFactorService();
    }

    /**
     * Show 2FA verification form
     */
    public function show(Request $request)
    {
        $this->request = $request;
        
        $user = auth()->user();
        
        if (!$user) {
            return $this->redirectToRoute('login');
        }

        if (!$user->isTwoFactorEnabled()) {
            return $this->redirectToRoute('home');
        }

        if (!$this->twoFactorService->needsVerification($user)) {
            return $this->redirectToRoute('home');
        }

        $data = [
            'title' => __('Two-Factor Authentication'),
            'meta_description' => __('Enter your two-factor authentication code'),
            'backup_codes_count' => $this->twoFactorService->getRemainingBackupCodesCount($user)
        ];

        return $this->view('auth/2fa.twig', $data);
    }

    /**
     * Verify 2FA code
     */
    public function verify(Request $request)
    {
        $this->request = $request;
        
        $user = auth()->user();
        
        if (!$user) {
            return $this->redirectToRoute('login');
        }

        if (!$user->isTwoFactorEnabled()) {
            return $this->redirectToRoute('home');
        }

        if (!$this->twoFactorService->needsVerification($user)) {
            return $this->redirectToRoute('home');
        }

        $code = $request->request->get('code');
        
        if (!$code) {
            $this->flashError(__('Please enter your verification code.'));
            return $this->back();
        }

        try {
            if ($this->twoFactorService->verifyLoginCode($user, $code)) {
                $this->twoFactorService->markAsVerified($user);
                $this->flashSuccess(__('Two-factor authentication verified successfully.'));
                
                // Redirect to intended URL or home
                $intended = session()->get('intended_url', '/');
                session()->forget('intended_url');
                
                return $this->redirect($intended);
            } else {
                $this->flashError(__('Invalid verification code. Please try again.'));
            }
        } catch (\Exception $e) {
            $this->flashError($e->getMessage());
        }

        return $this->back();
    }
}
