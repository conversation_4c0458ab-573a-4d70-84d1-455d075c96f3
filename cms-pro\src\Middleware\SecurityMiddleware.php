<?php

namespace CmsPro\Middleware;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * Security Middleware
 * 
 * @package CmsPro\Middleware
 */
class SecurityMiddleware
{
    /**
     * Handle the request
     */
    public function handle(Request $request, callable $next)
    {
        // Add security headers
        $response = $next($request);
        
        if ($response instanceof Response) {
            $this->addSecurityHeaders($response);
            $this->validateCsrfToken($request);
        }
        
        return $response;
    }

    /**
     * Add security headers
     */
    private function addSecurityHeaders(Response $response)
    {
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        $response->headers->set('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
        
        if (config('app.env') === 'production') {
            $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
        }
    }

    /**
     * Validate CSRF token for POST requests
     */
    private function validateCsrfToken(Request $request)
    {
        if (!config('app.security.csrf_protection', true)) {
            return;
        }

        if (in_array($request->getMethod(), ['POST', 'PUT', 'DELETE', 'PATCH'])) {
            $token = $request->request->get('_token') ?: $request->headers->get('X-CSRF-TOKEN');
            
            if (!$token || !session()->verifyToken($token)) {
                throw new \RuntimeException('CSRF token mismatch.', 419);
            }
        }
    }
}
