{% extends "admin/layout.twig" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="pages-index-container">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ title }}</h1>
            <p class="text-muted mb-0">{{ __('Manage your website pages') }}</p>
        </div>
        <div>
            {% if auth().can('pages.create') %}
            <a href="{{ url('/admin/pages/create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>{{ __('New Page') }}
            </a>
            {% endif %}
        </div>
    </div>

    <!-- Filters Section -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ url('/admin/pages') }}" class="row g-3" id="filters-form">
                <div class="col-md-3">
                    <label for="search" class="form-label">{{ __('Search') }}</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ current_filters.search }}" 
                           placeholder="{{ __('Search pages...') }}">
                </div>
                
                <div class="col-md-2">
                    <label for="status" class="form-label">{{ __('Status') }}</label>
                    <select class="form-select" id="status" name="status">
                        <option value="all" {{ current_filters.status == 'all' ? 'selected' : '' }}>
                            {{ __('All Statuses') }}
                        </option>
                        {% for key, label in statuses %}
                        <option value="{{ key }}" {{ current_filters.status == key ? 'selected' : '' }}>
                            {{ label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="template" class="form-label">{{ __('Template') }}</label>
                    <select class="form-select" id="template" name="template">
                        <option value="">{{ __('All Templates') }}</option>
                        {% for key, label in templates %}
                        <option value="{{ key }}" {{ current_filters.template == key ? 'selected' : '' }}>
                            {{ label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="author" class="form-label">{{ __('Author') }}</label>
                    <select class="form-select" id="author" name="author">
                        <option value="">{{ __('All Authors') }}</option>
                        {% for author in authors %}
                        <option value="{{ author.id }}" {{ current_filters.author == author.id ? 'selected' : '' }}>
                            {{ author.first_name }} {{ author.last_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search me-1"></i>{{ __('Filter') }}
                        </button>
                        <a href="{{ url('/admin/pages') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>{{ __('Clear') }}
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Bulk Actions -->
    {% if auth().can('pages.edit') %}
    <div class="bulk-actions-bar bg-light p-3 rounded mb-3" style="display: none;">
        <div class="d-flex align-items-center justify-content-between">
            <div>
                <span class="selected-count">0</span> {{ __('pages selected') }}
            </div>
            <div class="btn-group">
                <button type="button" class="btn btn-sm btn-success" data-action="publish">
                    <i class="fas fa-eye me-1"></i>{{ __('Publish') }}
                </button>
                <button type="button" class="btn btn-sm btn-warning" data-action="unpublish">
                    <i class="fas fa-eye-slash me-1"></i>{{ __('Unpublish') }}
                </button>
                {% if auth().can('pages.delete') %}
                <button type="button" class="btn btn-sm btn-danger" data-action="trash">
                    <i class="fas fa-trash me-1"></i>{{ __('Move to Trash') }}
                </button>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Pages Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-body p-0">
            {% if pages.items and pages.items|length > 0 %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            {% if auth().can('pages.edit') %}
                            <th width="40">
                                <input type="checkbox" class="form-check-input" id="select-all">
                            </th>
                            {% endif %}
                            <th>{{ __('Title') }}</th>
                            <th width="120">{{ __('Status') }}</th>
                            <th width="150">{{ __('Author') }}</th>
                            <th width="120">{{ __('Template') }}</th>
                            <th width="150">{{ __('Date') }}</th>
                            <th width="120">{{ __('Actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for page in pages.items %}
                        <tr>
                            {% if auth().can('pages.edit') %}
                            <td>
                                <input type="checkbox" class="form-check-input page-checkbox" 
                                       value="{{ page.id }}">
                            </td>
                            {% endif %}
                            <td>
                                <div class="d-flex align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="fw-bold">
                                            {% if auth().can('pages.edit') %}
                                            <a href="{{ url('/admin/pages/' ~ page.id ~ '/edit') }}" 
                                               class="text-decoration-none">
                                                {{ page.title }}
                                            </a>
                                            {% else %}
                                            {{ page.title }}
                                            {% endif %}
                                        </div>
                                        <div class="small text-muted">
                                            <i class="fas fa-link me-1"></i>
                                            <code>{{ page.slug }}</code>
                                            {% if page.parent %}
                                            <span class="ms-2">
                                                <i class="fas fa-level-up-alt me-1"></i>
                                                {{ page.parent.title }}
                                            </span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{{ page.status == 'published' ? 'success' : (page.status == 'draft' ? 'secondary' : (page.status == 'scheduled' ? 'info' : 'warning')) }}">
                                    {{ statuses[page.status] }}
                                </span>
                                {% if page.status == 'scheduled' and page.scheduled_at %}
                                <div class="small text-muted mt-1">
                                    {{ page.scheduled_at|date('M j, Y H:i') }}
                                </div>
                                {% endif %}
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-2">
                                        <div class="avatar-title bg-primary text-white rounded-circle">
                                            {{ page.author.first_name|first|upper }}{{ page.author.last_name|first|upper }}
                                        </div>
                                    </div>
                                    <div>
                                        <div class="fw-medium">{{ page.author.first_name }} {{ page.author.last_name }}</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-light text-dark">
                                    {{ templates[page.template]|default(page.template|default('Default')) }}
                                </span>
                            </td>
                            <td>
                                <div class="small">
                                    <div>{{ page.created_at|date('M j, Y') }}</div>
                                    <div class="text-muted">{{ page.created_at|date('H:i') }}</div>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    {% if auth().can('pages.edit') %}
                                    <a href="{{ url('/admin/pages/' ~ page.id ~ '/edit') }}" 
                                       class="btn btn-outline-primary" 
                                       data-bs-toggle="tooltip" 
                                       title="{{ __('Edit') }}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                    
                                    <a href="{{ page.getUrl() }}" 
                                       class="btn btn-outline-info" 
                                       target="_blank"
                                       data-bs-toggle="tooltip" 
                                       title="{{ __('View') }}">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    
                                    {% if auth().can('pages.delete') %}
                                    <button type="button" 
                                            class="btn btn-outline-danger delete-page" 
                                            data-page-id="{{ page.id }}"
                                            data-page-title="{{ page.title }}"
                                            data-bs-toggle="tooltip" 
                                            title="{{ __('Delete') }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if pagination.pages > 1 %}
            <div class="card-footer bg-white border-top">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="text-muted small">
                        {{ __('Showing :from to :to of :total pages', {
                            from: ((pagination.current_page - 1) * pagination.per_page) + 1,
                            to: min(pagination.current_page * pagination.per_page, pagination.total),
                            total: pagination.total
                        }) }}
                    </div>
                    
                    <nav aria-label="Pages pagination">
                        <ul class="pagination pagination-sm mb-0">
                            {% if pagination.current_page > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url('/admin/pages') }}?{{ http_build_query(current_filters|merge({page: pagination.current_page - 1})) }}">
                                    {{ __('Previous') }}
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for page in range(max(1, pagination.current_page - 2), min(pagination.pages, pagination.current_page + 2)) %}
                            <li class="page-item {{ page == pagination.current_page ? 'active' : '' }}">
                                <a class="page-link" href="{{ url('/admin/pages') }}?{{ http_build_query(current_filters|merge({page: page})) }}">
                                    {{ page }}
                                </a>
                            </li>
                            {% endfor %}
                            
                            {% if pagination.current_page < pagination.pages %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url('/admin/pages') }}?{{ http_build_query(current_filters|merge({page: pagination.current_page + 1})) }}">
                                    {{ __('Next') }}
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            </div>
            {% endif %}
            
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">{{ __('No pages found') }}</h5>
                <p class="text-muted">
                    {% if current_filters.search or current_filters.status != 'all' or current_filters.template or current_filters.author %}
                        {{ __('Try adjusting your filters or') }}
                        <a href="{{ url('/admin/pages') }}">{{ __('clear all filters') }}</a>
                    {% else %}
                        {{ __('Get started by creating your first page.') }}
                    {% endif %}
                </p>
                {% if auth().can('pages.create') %}
                <a href="{{ url('/admin/pages/create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>{{ __('Create First Page') }}
                </a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Confirm Delete') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>{{ __('Are you sure you want to move this page to trash?') }}</p>
                <p><strong class="page-title-placeholder"></strong></p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {{ __('This action can be undone from the trash.') }}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ __('Cancel') }}
                </button>
                <button type="button" class="btn btn-danger" id="confirmDelete">
                    <i class="fas fa-trash me-2"></i>{{ __('Move to Trash') }}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
/**
 * Pages Index Management
 * Enhanced with security and user experience features
 */
class PagesIndex {
    constructor() {
        this.selectedPages = new Set();
        this.deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        this.currentDeleteId = null;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupBulkActions();
        this.setupTooltips();
        this.setupAutoSubmit();
    }

    setupEventListeners() {
        // Select all checkbox
        const selectAll = document.getElementById('select-all');
        if (selectAll) {
            selectAll.addEventListener('change', (e) => {
                this.toggleSelectAll(e.target.checked);
            });
        }

        // Individual checkboxes
        document.addEventListener('change', (e) => {
            if (e.target.matches('.page-checkbox')) {
                this.togglePageSelection(e.target.value, e.target.checked);
            }
        });

        // Delete buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.delete-page') || e.target.closest('.delete-page')) {
                e.preventDefault();
                const button = e.target.matches('.delete-page') ? e.target : e.target.closest('.delete-page');
                this.showDeleteModal(button.dataset.pageId, button.dataset.pageTitle);
            }
        });

        // Confirm delete
        document.getElementById('confirmDelete').addEventListener('click', () => {
            this.deletePage(this.currentDeleteId);
        });

        // Bulk action buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action]')) {
                e.preventDefault();
                const action = e.target.dataset.action;
                this.performBulkAction(action);
            }
        });
    }

    setupBulkActions() {
        this.updateBulkActionsBar();
    }

    setupTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    setupAutoSubmit() {
        // Auto-submit filters on change (with debounce)
        let timeout;
        const searchInput = document.getElementById('search');

        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    document.getElementById('filters-form').submit();
                }, 500);
            });
        }

        // Auto-submit on select changes
        const selects = document.querySelectorAll('#status, #template, #author');
        selects.forEach(select => {
            select.addEventListener('change', () => {
                document.getElementById('filters-form').submit();
            });
        });
    }

    toggleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('.page-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
            this.togglePageSelection(checkbox.value, checked);
        });
    }

    togglePageSelection(pageId, selected) {
        if (selected) {
            this.selectedPages.add(pageId);
        } else {
            this.selectedPages.delete(pageId);
        }

        this.updateBulkActionsBar();
        this.updateSelectAllState();
    }

    updateBulkActionsBar() {
        const bulkBar = document.querySelector('.bulk-actions-bar');
        const countElement = document.querySelector('.selected-count');

        if (bulkBar && countElement) {
            const count = this.selectedPages.size;
            countElement.textContent = count;

            if (count > 0) {
                bulkBar.style.display = 'block';
            } else {
                bulkBar.style.display = 'none';
            }
        }
    }

    updateSelectAllState() {
        const selectAll = document.getElementById('select-all');
        const checkboxes = document.querySelectorAll('.page-checkbox');

        if (selectAll && checkboxes.length > 0) {
            const checkedCount = document.querySelectorAll('.page-checkbox:checked').length;

            if (checkedCount === 0) {
                selectAll.checked = false;
                selectAll.indeterminate = false;
            } else if (checkedCount === checkboxes.length) {
                selectAll.checked = true;
                selectAll.indeterminate = false;
            } else {
                selectAll.checked = false;
                selectAll.indeterminate = true;
            }
        }
    }

    showDeleteModal(pageId, pageTitle) {
        this.currentDeleteId = pageId;
        document.querySelector('.page-title-placeholder').textContent = pageTitle;
        this.deleteModal.show();
    }

    async deletePage(pageId) {
        try {
            const response = await fetch(`/admin/pages/${pageId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': this.getCsrfToken()
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');
                this.deleteModal.hide();

                // Remove row from table
                const row = document.querySelector(`input[value="${pageId}"]`).closest('tr');
                if (row) {
                    row.remove();
                }

                // Update counts
                this.selectedPages.delete(pageId);
                this.updateBulkActionsBar();
                this.updateSelectAllState();

            } else {
                this.showNotification(result.message, 'error');
            }

        } catch (error) {
            console.error('Delete error:', error);
            this.showNotification('An error occurred while deleting the page.', 'error');
        }
    }

    async performBulkAction(action) {
        if (this.selectedPages.size === 0) {
            this.showNotification('No pages selected.', 'warning');
            return;
        }

        // Confirm dangerous actions
        if (action === 'trash') {
            if (!confirm(`Are you sure you want to move ${this.selectedPages.size} pages to trash?`)) {
                return;
            }
        }

        try {
            const response = await fetch('/admin/pages/bulk-action', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': this.getCsrfToken()
                },
                body: JSON.stringify({
                    action: action,
                    page_ids: Array.from(this.selectedPages)
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');

                // Reload page to show updated data
                setTimeout(() => {
                    window.location.reload();
                }, 1000);

            } else {
                this.showNotification(result.message, 'error');
            }

        } catch (error) {
            console.error('Bulk action error:', error);
            this.showNotification('An error occurred while processing pages.', 'error');
        }
    }

    getCsrfToken() {
        return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
        notification.innerHTML = `
            ${this.escapeHtml(message)}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    new PagesIndex();
});
</script>

<style>
/* Pages Index Styles */
.pages-index-container .table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.avatar-sm {
    width: 32px;
    height: 32px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
}

.bulk-actions-bar {
    border: 1px solid #dee2e6;
    animation: slideDown 0.3s ease-out;
}

.page-checkbox:checked + td {
    background-color: rgba(13, 110, 253, 0.1);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.025);
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive improvements */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .btn-group-sm .btn {
        padding: 0.125rem 0.25rem;
    }

    .bulk-actions-bar {
        flex-direction: column;
        gap: 1rem;
    }

    .bulk-actions-bar .btn-group {
        width: 100%;
    }

    .bulk-actions-bar .btn-group .btn {
        flex: 1;
    }
}

/* Dark mode support */
[data-bs-theme="dark"] .table th {
    color: #adb5bd;
}

[data-bs-theme="dark"] .bulk-actions-bar {
    background-color: #343a40 !important;
    border-color: #495057;
}

[data-bs-theme="dark"] .avatar-title {
    background-color: #0d6efd !important;
}
</style>
{% endblock %}
