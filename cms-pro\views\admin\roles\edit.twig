{% extends "admin/roles/create.twig" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="role-form-container">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ title }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ url('/admin/roles') }}">{{ __('Roles') }}</a>
                    </li>
                    <li class="breadcrumb-item active">{{ __('Edit') }}</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <a href="{{ url('/admin/roles') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>{{ __('Back to Roles') }}
            </a>
        </div>
    </div>

    <!-- Role Info Bar -->
    <div class="alert alert-info d-flex align-items-center mb-4">
        <div class="flex-grow-1">
            <div class="d-flex align-items-center">
                <i class="fas fa-info-circle me-2"></i>
                <div>
                    <strong>{{ __('Role ID') }}:</strong> {{ role.id }} |
                    <strong>{{ __('Created') }}:</strong> {{ role.created_at|date('M j, Y H:i') }} |
                    <strong>{{ __('Type') }}:</strong> {{ role.is_system ? __('System Role') : __('Custom Role') }}
                    {% if role.level %}
                    | <strong>{{ __('Level') }}:</strong> {{ role.level }}
                    {% endif %}
                </div>
            </div>
        </div>
        {% if users_with_role and users_with_role|length > 0 %}
        <div>
            <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#usersModal">
                <i class="fas fa-users me-1"></i>{{ __('Users') }} ({{ users_with_role|length }})
            </button>
        </div>
        {% endif %}
    </div>

    <form method="POST" action="{{ url('/admin/roles/' ~ role.id) }}" class="role-form" id="role-form" novalidate>
        {{ csrf_field() | raw }}
        <input type="hidden" name="_method" value="PUT">
        
        <div class="row">
            <!-- Main Content Column -->
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-shield-alt me-2"></i>{{ __('Role Information') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Name -->
                        <div class="mb-3">
                            <label for="name" class="form-label">
                                {{ __('Role Name') }} <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control {{ errors.name ? 'is-invalid' : '' }}" 
                                   id="name" 
                                   name="name" 
                                   value="{{ old('name', role.name) }}" 
                                   required
                                   maxlength="100"
                                   {{ role.is_system ? 'readonly' : '' }}
                                   placeholder="{{ __('Enter role name...') }}">
                            {% if errors.name %}
                                <div class="invalid-feedback">{{ errors.name }}</div>
                            {% endif %}
                            {% if role.is_system %}
                            <div class="form-text text-warning">
                                <i class="fas fa-lock me-1"></i>{{ __('System role names cannot be changed') }}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Slug -->
                        <div class="mb-3">
                            <label for="slug" class="form-label">{{ __('Role Slug') }}</label>
                            <input type="text" 
                                   class="form-control {{ errors.slug ? 'is-invalid' : '' }}" 
                                   id="slug" 
                                   name="slug" 
                                   value="{{ old('slug', role.slug) }}"
                                   maxlength="100"
                                   {{ role.is_system ? 'readonly' : '' }}
                                   placeholder="{{ __('auto-generated') }}">
                            {% if errors.slug %}
                                <div class="invalid-feedback">{{ errors.slug }}</div>
                            {% endif %}
                            <div class="form-text">
                                {% if role.is_system %}
                                <i class="fas fa-lock me-1"></i>{{ __('System role slugs cannot be changed') }}
                                {% else %}
                                {{ __('Used for programmatic access. Change with caution.') }}
                                {% endif %}
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label for="description" class="form-label">{{ __('Description') }}</label>
                            <textarea class="form-control {{ errors.description ? 'is-invalid' : '' }}" 
                                      id="description" 
                                      name="description" 
                                      rows="3"
                                      maxlength="500"
                                      placeholder="{{ __('Brief description of this role...') }}">{{ old('description', role.description) }}</textarea>
                            {% if errors.description %}
                                <div class="invalid-feedback">{{ errors.description }}</div>
                            {% endif %}
                            <div class="form-text">
                                {{ __('Help users understand what this role is for') }}
                            </div>
                        </div>

                        <!-- Level -->
                        <div class="mb-3">
                            <label for="level" class="form-label">
                                {{ __('Role Level') }} <span class="text-danger">*</span>
                            </label>
                            <input type="number" 
                                   class="form-control {{ errors.level ? 'is-invalid' : '' }}" 
                                   id="level" 
                                   name="level" 
                                   value="{{ old('level', role.level) }}" 
                                   required
                                   min="1"
                                   max="99"
                                   {{ role.is_system ? 'readonly' : '' }}
                                   placeholder="50">
                            {% if errors.level %}
                                <div class="invalid-feedback">{{ errors.level }}</div>
                            {% endif %}
                            <div class="form-text">
                                {% if role.is_system %}
                                <i class="fas fa-lock me-1"></i>{{ __('System role levels cannot be changed') }}
                                {% else %}
                                {{ __('Higher levels have more authority. Be careful when changing this.') }}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Permissions -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-success">
                            <i class="fas fa-key me-2"></i>{{ __('Permissions') }}
                        </h6>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-primary" id="select-all-permissions">
                                {{ __('Select All') }}
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="deselect-all-permissions">
                                {{ __('Deselect All') }}
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="permissions-container">
                            {% for category, categoryPermissions in permissions %}
                            <div class="permission-category mb-4">
                                <div class="category-header d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="text-primary mb-0">
                                        <i class="fas fa-folder me-2"></i>{{ category|title }}
                                    </h6>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary btn-sm select-category" 
                                                data-category="{{ category }}">
                                            {{ __('Select All') }}
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm deselect-category" 
                                                data-category="{{ category }}">
                                            {{ __('Deselect All') }}
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    {% for permission in categoryPermissions %}
                                    <div class="col-md-6 mb-2">
                                        <div class="form-check permission-item">
                                            <input class="form-check-input permission-checkbox" 
                                                   type="checkbox" 
                                                   id="permission_{{ permission.id }}" 
                                                   name="permissions[]" 
                                                   value="{{ permission.id }}"
                                                   data-category="{{ category }}"
                                                   {{ (old('permissions') and permission.id in old('permissions')) or (not old('permissions') and permission.id in role_permissions) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="permission_{{ permission.id }}">
                                                <strong>{{ permission.name }}</strong>
                                                {% if permission.description and permission.description != permission.name %}
                                                <br><small class="text-muted">{{ permission.description }}</small>
                                                {% endif %}
                                                <br><code class="small">{{ permission.slug }}</code>
                                            </label>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar Column -->
            <div class="col-lg-4">
                <!-- Role Settings -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-warning">
                            <i class="fas fa-cog me-2"></i>{{ __('Role Settings') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Status -->
                        <div class="mb-3">
                            <label for="status" class="form-label">{{ __('Status') }}</label>
                            <select class="form-select {{ errors.status ? 'is-invalid' : '' }}" 
                                    id="status" 
                                    name="status" 
                                    required>
                                {% for key, label in statuses %}
                                <option value="{{ key }}" {{ old('status', role.status) == key ? 'selected' : '' }}>
                                    {{ label }}
                                </option>
                                {% endfor %}
                            </select>
                            {% if errors.status %}
                                <div class="invalid-feedback">{{ errors.status }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Permission Summary -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-info">
                            <i class="fas fa-chart-pie me-2"></i>{{ __('Permission Summary') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="permission-summary">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" name="action" value="save" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ __('Update Role') }}
                            </button>
                            
                            <div class="btn-group">
                                <button type="submit" name="action" value="save_and_continue" class="btn btn-outline-primary">
                                    <i class="fas fa-save me-2"></i>{{ __('Save & Continue') }}
                                </button>
                                <button type="submit" name="action" value="save_and_new" class="btn btn-outline-primary">
                                    <i class="fas fa-plus me-2"></i>{{ __('Save & New') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Danger Zone -->
                {% if auth().can('roles.delete') and not role.is_system and role.getUsersCount() == 0 %}
                <div class="card border-danger shadow-sm">
                    <div class="card-header bg-danger text-white border-0">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-exclamation-triangle me-2"></i>{{ __('Danger Zone') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">{{ __('Permanently delete this role and all associated permissions.') }}</p>
                        <button type="button" class="btn btn-outline-danger w-100" id="delete-role" 
                                data-role-id="{{ role.id }}" data-role-name="{{ role.name }}">
                            <i class="fas fa-trash me-2"></i>{{ __('Delete Role') }}
                        </button>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </form>
</div>

<!-- Users with Role Modal -->
{% if users_with_role and users_with_role|length > 0 %}
<div class="modal fade" id="usersModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Users with :role Role', {role: role.name}) }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>{{ __('Name') }}</th>
                                <th>{{ __('Email') }}</th>
                                <th>{{ __('Status') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users_with_role %}
                            <tr>
                                <td>{{ user.first_name }} {{ user.last_name }}</td>
                                <td>{{ user.email }}</td>
                                <td>
                                    <span class="badge bg-{{ user.status == 'active' ? 'success' : 'warning' }}">
                                        {{ user.status|title }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Confirm Delete') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>{{ __('Are you sure you want to delete this role?') }}</p>
                <p><strong class="role-name-placeholder">{{ role.name }}</strong></p>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {{ __('This action cannot be undone. All permissions associated with this role will be removed.') }}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ __('Cancel') }}
                </button>
                <button type="button" class="btn btn-danger" id="confirmDelete">
                    <i class="fas fa-trash me-2"></i>{{ __('Delete Role') }}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
// Extend RoleForm for edit functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize base form functionality
    new RoleForm();

    // Add edit-specific functionality
    const editEnhancements = {
        init() {
            this.setupDeleteButton();
            this.updatePermissionSummary(); // Initial summary update
        },

        setupDeleteButton() {
            const deleteButton = document.getElementById('delete-role');
            if (deleteButton) {
                deleteButton.addEventListener('click', () => {
                    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
                    modal.show();
                });
            }

            const confirmDelete = document.getElementById('confirmDelete');
            if (confirmDelete) {
                confirmDelete.addEventListener('click', async () => {
                    try {
                        const response = await fetch(`/admin/roles/{{ role.id }}`, {
                            method: 'DELETE',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest',
                                'X-CSRF-TOKEN': this.getCsrfToken()
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            this.showNotification(result.message, 'success');
                            setTimeout(() => {
                                window.location.href = '/admin/roles';
                            }, 1000);
                        } else {
                            this.showNotification(result.message, 'error');
                        }

                    } catch (error) {
                        console.error('Delete error:', error);
                        this.showNotification('An error occurred while deleting the role.', 'error');
                    }
                });
            }
        },

        updatePermissionSummary() {
            const checkedPermissions = document.querySelectorAll('.permission-checkbox:checked');
            const summaryContainer = document.getElementById('permission-summary');

            if (checkedPermissions.length === 0) {
                summaryContainer.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-info-circle fa-2x mb-2"></i>
                        <p class="mb-0">{{ __('No permissions assigned') }}</p>
                    </div>
                `;
                return;
            }

            // Group permissions by category
            const permissionsByCategory = {};
            checkedPermissions.forEach(checkbox => {
                const category = checkbox.dataset.category;
                if (!permissionsByCategory[category]) {
                    permissionsByCategory[category] = 0;
                }
                permissionsByCategory[category]++;
            });

            // Generate summary HTML
            let summaryHtml = `
                <div class="permission-stats mb-3">
                    <div class="stat-item">
                        <span class="stat-number">${checkedPermissions.length}</span>
                        <span class="stat-label">{{ __('Total Permissions') }}</span>
                    </div>
                </div>
                <div class="permission-breakdown">
            `;

            Object.entries(permissionsByCategory).forEach(([category, count]) => {
                const totalInCategory = document.querySelectorAll(`[data-category="${category}"]`).length;
                const percentage = Math.round((count / totalInCategory) * 100);

                summaryHtml += `
                    <div class="category-stat mb-2">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span class="category-name">${category.charAt(0).toUpperCase() + category.slice(1)}</span>
                            <span class="category-count">${count}/${totalInCategory}</span>
                        </div>
                        <div class="progress" style="height: 4px;">
                            <div class="progress-bar" style="width: ${percentage}%"></div>
                        </div>
                    </div>
                `;
            });

            summaryHtml += '</div>';
            summaryContainer.innerHTML = summaryHtml;
        },

        getCsrfToken() {
            return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
        },

        showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
            notification.innerHTML = `
                ${this.escapeHtml(message)}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        },

        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    };

    // Initialize edit enhancements
    editEnhancements.init();

    // Override the updatePermissionSummary method in RoleForm
    if (window.roleFormInstance) {
        window.roleFormInstance.updatePermissionSummary = editEnhancements.updatePermissionSummary;
    }

    // Listen for permission changes to update summary
    document.addEventListener('change', (e) => {
        if (e.target.matches('.permission-checkbox')) {
            editEnhancements.updatePermissionSummary();
        }
    });
});
</script>

<style>
/* Edit-specific styles */
.role-form-container .alert-info {
    font-size: 0.875rem;
}

.role-form-container .breadcrumb {
    font-size: 0.875rem;
    margin-bottom: 0;
}

.role-form-container .breadcrumb-item + .breadcrumb-item::before {
    content: "›";
}

.card.border-danger {
    border-color: #dc3545 !important;
}

.card-header.bg-danger {
    background-color: #dc3545 !important;
}

/* System role indicators */
.form-control[readonly] {
    background-color: #f8f9fa;
    opacity: 0.8;
}

.form-text.text-warning {
    color: #856404 !important;
}

/* Users modal styles */
.modal-lg .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

/* Dark mode support */
[data-bs-theme="dark"] .alert-info {
    background-color: #1e3a5f;
    border-color: #2c5aa0;
    color: #9ec5fe;
}

[data-bs-theme="dark"] .card.border-danger {
    border-color: #dc3545 !important;
}

[data-bs-theme="dark"] .card-header.bg-danger {
    background-color: #dc3545 !important;
    color: white;
}

[data-bs-theme="dark"] .form-control[readonly] {
    background-color: #374151;
    color: #9ca3af;
}

[data-bs-theme="dark"] .form-text.text-warning {
    color: #fbbf24 !important;
}
</style>
{% endblock %}
