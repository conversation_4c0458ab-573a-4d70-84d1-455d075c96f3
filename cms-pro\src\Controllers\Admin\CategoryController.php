<?php

namespace CmsPro\Controllers\Admin;

use CmsPro\Controllers\BaseController;
use CmsPro\Models\Category;
use CmsPro\Services\ValidationService;
use CmsPro\Services\SanitizationService;
use CmsPro\Services\ActivityLogger;
use CmsPro\Services\SeoService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Category Controller for Admin Panel
 * 
 * @package CmsPro\Controllers\Admin
 */
class CategoryController extends BaseController
{
    private $validationService;
    private $sanitizationService;
    private $activityLogger;
    private $seoService;

    public function __construct()
    {
        parent::__construct();
        
        $this->validationService = new ValidationService();
        $this->sanitizationService = new SanitizationService();
        $this->activityLogger = new ActivityLogger();
        $this->seoService = new SeoService();
    }

    /**
     * Display categories list
     */
    public function index(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('categories.view')) {
                return $this->forbidden('You do not have permission to view categories.');
            }

            // Get filters
            $filters = [
                'search' => $request->query->get('search', ''),
                'parent_id' => $request->query->get('parent_id', ''),
                'page' => max(1, (int) $request->query->get('page', 1)),
                'per_page' => 20
            ];

            // Get categories
            $result = Category::getFiltered($filters);
            $categories = $result['categories'];
            $pagination = $result['pagination'];

            // Get parent categories for filter
            $parentCategories = Category::getParentOptions();

            // Log activity
            $this->activityLogger->log('categories_viewed', [
                'user_id' => auth()->id(),
                'filters' => $filters
            ]);

            $data = [
                'title' => __('Categories'),
                'categories' => $categories,
                'pagination' => $pagination,
                'parent_categories' => $parentCategories,
                'current_filters' => $filters
            ];

            return $this->view('admin/categories/index.twig', $data);

        } catch (\Exception $e) {
            $this->logError('categories_index_error', $e, $request);
            $this->flashError(__('An error occurred while loading categories.'));
            return $this->redirectToRoute('admin.dashboard');
        }
    }

    /**
     * Show create category form
     */
    public function create(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('categories.create')) {
                return $this->forbidden('You do not have permission to create categories.');
            }

            // Get parent categories
            $parentCategories = Category::getParentOptions();

            $data = [
                'title' => __('Create Category'),
                'category' => new Category(),
                'parent_categories' => $parentCategories,
                'action' => 'create'
            ];

            return $this->view('admin/categories/form.twig', $data);

        } catch (\Exception $e) {
            $this->logError('categories_create_form_error', $e, $request);
            $this->flashError(__('An error occurred while loading the form.'));
            return $this->redirectToRoute('admin.categories.index');
        }
    }

    /**
     * Store new category
     */
    public function store(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('categories.create')) {
                return $this->forbidden('You do not have permission to create categories.');
            }

            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                $this->flashError(__('Security token mismatch. Please try again.'));
                return $this->back();
            }

            // Validation rules
            $rules = [
                'name' => 'required|string|max:100',
                'description' => 'nullable|string|max:500',
                'parent_id' => 'nullable|integer',
                'sort_order' => 'nullable|integer|min:0',
                'color' => 'nullable|string|max:7',
                'icon' => 'nullable|string|max:50',
                'meta_title' => 'nullable|string|max:60',
                'meta_description' => 'nullable|string|max:160',
                'meta_keywords' => 'nullable|string|max:255'
            ];

            // Validate input
            $validatedData = $this->validationService->validate($request->request->all(), $rules);

            // Sanitize input
            $sanitizedData = $this->sanitizeCategoryData($validatedData);

            // Generate slug
            $slug = $this->seoService->generateSlug($sanitizedData['name'], 'category');
            $sanitizedData['slug'] = $slug;

            // Create category
            $category = Category::create($sanitizedData);

            if ($category) {
                // Log activity
                $this->activityLogger->log('category_created', [
                    'user_id' => auth()->id(),
                    'category_id' => $category->getId(),
                    'category_name' => $category->getName()
                ]);

                $this->flashSuccess(__('Category created successfully.'));
                
                if ($request->request->get('save_and_continue')) {
                    return $this->redirectToRoute('admin.categories.edit', ['id' => $category->getId()]);
                }
                
                return $this->redirectToRoute('admin.categories.index');
            }

            $this->flashError(__('Failed to create category.'));
            return $this->back();

        } catch (\Exception $e) {
            $this->logError('categories_store_error', $e, $request);
            $this->flashError(__('An error occurred while creating the category.'));
            return $this->back();
        }
    }

    /**
     * Show edit category form
     */
    public function edit(Request $request, $id)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('categories.edit')) {
                return $this->forbidden('You do not have permission to edit categories.');
            }

            // Find category
            $category = Category::find($id);
            if (!$category) {
                $this->flashError(__('Category not found.'));
                return $this->redirectToRoute('admin.categories.index');
            }

            // Get parent categories (excluding current category and its descendants)
            $parentCategories = Category::getParentOptions($id);

            $data = [
                'title' => __('Edit Category'),
                'category' => $category,
                'parent_categories' => $parentCategories,
                'action' => 'edit'
            ];

            return $this->view('admin/categories/form.twig', $data);

        } catch (\Exception $e) {
            $this->logError('categories_edit_form_error', $e, $request);
            $this->flashError(__('An error occurred while loading the category.'));
            return $this->redirectToRoute('admin.categories.index');
        }
    }

    /**
     * Update category
     */
    public function update(Request $request, $id)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('categories.edit')) {
                return $this->forbidden('You do not have permission to edit categories.');
            }

            // Find category
            $category = Category::find($id);
            if (!$category) {
                $this->flashError(__('Category not found.'));
                return $this->redirectToRoute('admin.categories.index');
            }

            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                $this->flashError(__('Security token mismatch. Please try again.'));
                return $this->back();
            }

            // Validation rules
            $rules = [
                'name' => 'required|string|max:100',
                'description' => 'nullable|string|max:500',
                'parent_id' => 'nullable|integer',
                'sort_order' => 'nullable|integer|min:0',
                'color' => 'nullable|string|max:7',
                'icon' => 'nullable|string|max:50',
                'meta_title' => 'nullable|string|max:60',
                'meta_description' => 'nullable|string|max:160',
                'meta_keywords' => 'nullable|string|max:255'
            ];

            // Validate input
            $validatedData = $this->validationService->validate($request->request->all(), $rules);

            // Sanitize input
            $sanitizedData = $this->sanitizeCategoryData($validatedData);

            // Update slug if name changed
            if ($sanitizedData['name'] !== $category->getName()) {
                $slug = $this->seoService->generateSlug($sanitizedData['name'], 'category', $id);
                $sanitizedData['slug'] = $slug;
            }

            // Prevent circular parent relationship
            if (!empty($sanitizedData['parent_id']) && $sanitizedData['parent_id'] == $id) {
                $this->flashError(__('A category cannot be its own parent.'));
                return $this->back();
            }

            // Update category
            $updated = $category->update($sanitizedData);

            if ($updated) {
                // Log activity
                $this->activityLogger->log('category_updated', [
                    'user_id' => auth()->id(),
                    'category_id' => $category->getId(),
                    'category_name' => $category->getName()
                ]);

                $this->flashSuccess(__('Category updated successfully.'));
                
                if ($request->request->get('save_and_continue')) {
                    return $this->redirectToRoute('admin.categories.edit', ['id' => $id]);
                }
                
                return $this->redirectToRoute('admin.categories.index');
            }

            $this->flashError(__('Failed to update category.'));
            return $this->back();

        } catch (\Exception $e) {
            $this->logError('categories_update_error', $e, $request);
            $this->flashError(__('An error occurred while updating the category.'));
            return $this->back();
        }
    }

    /**
     * Delete category
     */
    public function destroy(Request $request, $id)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('categories.delete')) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'You do not have permission to delete categories.'
                    ], 403);
                }
                
                return $this->forbidden('You do not have permission to delete categories.');
            }

            // Find category
            $category = Category::find($id);
            if (!$category) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Category not found.'
                    ], 404);
                }
                
                $this->flashError(__('Category not found.'));
                return $this->redirectToRoute('admin.categories.index');
            }

            // Store category info for logging
            $categoryName = $category->getName();

            // Delete category
            $deleted = $category->delete();

            if ($deleted) {
                // Log activity
                $this->activityLogger->log('category_deleted', [
                    'user_id' => auth()->id(),
                    'category_id' => $id,
                    'category_name' => $categoryName
                ]);

                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => true,
                        'message' => 'Category deleted successfully.'
                    ]);
                }

                $this->flashSuccess(__('Category deleted successfully.'));
                return $this->redirectToRoute('admin.categories.index');
            }

            $message = 'Cannot delete category. It may have posts or subcategories.';

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => $message
                ]);
            }

            $this->flashError(__($message));
            return $this->redirectToRoute('admin.categories.index');

        } catch (\Exception $e) {
            $this->logError('categories_delete_error', $e, $request);
            
            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'An error occurred while deleting the category.'
                ], 500);
            }
            
            $this->flashError(__('An error occurred while deleting the category.'));
            return $this->redirectToRoute('admin.categories.index');
        }
    }

    /**
     * Get categories for AJAX requests
     */
    public function getCategories(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('categories.view')) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'You do not have permission to view categories.'
                ], 403);
            }

            $search = $request->query->get('search', '');
            $parentId = $request->query->get('parent_id', '');

            $filters = [
                'search' => $search,
                'parent_id' => $parentId,
                'per_page' => 50
            ];

            $result = Category::getFiltered($filters);
            $categories = $result['categories'];

            $data = [];
            foreach ($categories as $category) {
                $data[] = [
                    'id' => $category->getId(),
                    'name' => $category->getName(),
                    'slug' => $category->getSlug(),
                    'parent_id' => $category->getParentId(),
                    'posts_count' => $category->getPostsCount(),
                    'color' => $category->getColor(),
                    'icon' => $category->getIcon()
                ];
            }

            return new JsonResponse([
                'success' => true,
                'categories' => $data
            ]);

        } catch (\Exception $e) {
            $this->logError('categories_ajax_error', $e, $request);
            
            return new JsonResponse([
                'success' => false,
                'message' => 'An error occurred while loading categories.'
            ], 500);
        }
    }

    /**
     * Sanitize category data
     */
    private function sanitizeCategoryData($data)
    {
        return [
            'name' => $this->sanitizationService->sanitizeInput($data['name']),
            'description' => $this->sanitizationService->sanitizeInput($data['description'] ?? ''),
            'parent_id' => !empty($data['parent_id']) ? (int) $data['parent_id'] : null,
            'sort_order' => !empty($data['sort_order']) ? (int) $data['sort_order'] : null,
            'color' => $this->sanitizationService->sanitizeInput($data['color'] ?? '#007bff'),
            'icon' => $this->sanitizationService->sanitizeInput($data['icon'] ?? ''),
            'meta_title' => $this->sanitizationService->sanitizeInput($data['meta_title'] ?? ''),
            'meta_description' => $this->sanitizationService->sanitizeInput($data['meta_description'] ?? ''),
            'meta_keywords' => $this->sanitizationService->sanitizeInput($data['meta_keywords'] ?? '')
        ];
    }

    /**
     * Validate CSRF token
     */
    private function validateCsrfToken(Request $request)
    {
        $token = $request->request->get('_token') ?: $request->headers->get('X-CSRF-TOKEN');
        
        if (!$token) {
            return false;
        }

        $sessionToken = session()->get('_token');
        return $sessionToken && hash_equals($sessionToken, $token);
    }

    /**
     * Log errors
     */
    private function logError($event, \Exception $e, Request $request)
    {
        $logData = [
            'event' => $event,
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'user_id' => auth()->id(),
            'ip_address' => $request->getClientIp(),
            'user_agent' => $request->headers->get('User-Agent'),
            'url' => $request->getUri()
        ];

        error_log(json_encode($logData), 3, storage_path('logs/categories.log'));
        
        if ($this->activityLogger) {
            $this->activityLogger->log($event, $logData);
        }
    }
}
