<?php

namespace CmsPro\Middleware;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * Role-Based Access Control Middleware
 * 
 * @package CmsPro\Middleware
 */
class RoleMiddleware
{
    /**
     * Handle the request
     */
    public function handle(Request $request, callable $next, $role = null)
    {
        $auth = auth();
        
        // Check if user is authenticated
        if (!$auth->check()) {
            return $this->redirectToLogin($request);
        }
        
        // Check if specific role is required
        if ($role && !$auth->hasRole($role)) {
            return new Response('Forbidden: Insufficient role privileges', 403);
        }
        
        return $next($request);
    }

    /**
     * Redirect to appropriate login page
     */
    private function redirectToLogin(Request $request)
    {
        $path = $request->getPathInfo();
        
        if (strpos($path, '/admin') === 0) {
            return redirect('/admin/login');
        }
        
        return redirect('/login');
    }
}
