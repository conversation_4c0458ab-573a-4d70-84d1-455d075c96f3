<?php

namespace CmsPro\Controllers\Admin;

use CmsPro\Controllers\BaseController;
use CmsPro\Services\MediaService;
use CmsPro\Services\ValidationService;
use CmsPro\Services\SanitizationService;
use CmsPro\Services\ActivityLogger;
use CmsPro\Services\SecurityService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * Media Admin Controller
 * 
 * @package CmsPro\Controllers\Admin
 */
class MediaController extends BaseController
{
    private $mediaService;
    private $validationService;
    private $sanitizationService;
    private $activityLogger;
    private $securityService;

    public function __construct()
    {
        parent::__construct();
        $this->mediaService = new MediaService();
        $this->validationService = new ValidationService();
        $this->sanitizationService = new SanitizationService();
        $this->activityLogger = new ActivityLogger();
        $this->securityService = new SecurityService();
    }

    /**
     * Media library index
     */
    public function index(Request $request)
    {
        $this->request = $request;

        // Check permission
        if (!auth()->can('media.view')) {
            return $this->forbidden('You do not have permission to view media.');
        }

        $page = (int) $request->query->get('page', 1);
        $perPage = (int) $request->query->get('per_page', 20);
        $type = $request->query->get('type');
        $search = $request->query->get('search');
        $orderBy = $request->query->get('order_by', 'created_at');
        $orderDir = $request->query->get('order_dir', 'DESC');

        $filters = [
            'type' => $type,
            'search' => $search,
            'order_by' => $orderBy,
            'order_dir' => $orderDir
        ];

        $result = $this->mediaService->getMediaList($filters, $page, $perPage);
        $stats = $this->mediaService->getMediaStatistics();

        $data = [
            'title' => __('Media Library'),
            'meta_description' => __('Manage media files'),
            'media' => $result['data'],
            'pagination' => [
                'current_page' => $result['page'],
                'last_page' => $result['last_page'],
                'per_page' => $result['per_page'],
                'total' => $result['total']
            ],
            'filters' => $filters,
            'stats' => $stats,
            'file_types' => ['image', 'video', 'audio', 'document']
        ];

        return $this->view('admin/media/index.twig', $data);
    }

    /**
     * Upload files
     */
    public function upload(Request $request)
    {
        $this->request = $request;

        // Check permission
        if (!auth()->can('media.upload')) {
            return new JsonResponse(['success' => false, 'error' => 'Permission denied'], 403);
        }

        $files = $request->files->get('files', []);
        if (!is_array($files)) {
            $files = [$files];
        }

        $options = [
            'title' => $request->request->get('title'),
            'alt_text' => $request->request->get('alt_text'),
            'description' => $request->request->get('description'),
            'resize' => $request->request->get('resize', true),
            'max_width' => $request->request->get('max_width'),
            'max_height' => $request->request->get('max_height')
        ];

        try {
            if (count($files) === 1 && $files[0] instanceof UploadedFile) {
                // Single file upload
                $result = $this->mediaService->uploadFile($files[0], $options);
                return new JsonResponse(['success' => true, 'media' => $result]);
            } else {
                // Multiple file upload
                $results = $this->mediaService->uploadMultipleFiles($files, $options);
                return new JsonResponse(['success' => true, 'results' => $results]);
            }
        } catch (\Exception $e) {
            return new JsonResponse(['success' => false, 'error' => $e->getMessage()], 400);
        }
    }

    /**
     * Get media details
     */
    public function show(Request $request, $parameters)
    {
        $this->request = $request;
        $mediaId = $parameters['id'] ?? null;

        // Check permission
        if (!auth()->can('media.view')) {
            return new JsonResponse(['success' => false, 'error' => 'Permission denied'], 403);
        }

        $media = $this->mediaService->getMedia($mediaId);
        if (!$media) {
            return new JsonResponse(['success' => false, 'error' => 'Media not found'], 404);
        }

        // Generate thumbnails for images
        if ($media['file_type'] === 'image') {
            $media['thumbnails'] = [
                'small' => $this->mediaService->getThumbnailUrl($media, 150, 150),
                'medium' => $this->mediaService->getThumbnailUrl($media, 300, 300),
                'large' => $this->mediaService->getThumbnailUrl($media, 600, 600)
            ];
        }

        return new JsonResponse(['success' => true, 'media' => $media]);
    }

    /**
     * Update media metadata
     */
    public function update(Request $request, $parameters)
    {
        $this->request = $request;
        $mediaId = $parameters['id'] ?? null;

        // Check permission
        if (!auth()->can('media.edit')) {
            return new JsonResponse(['success' => false, 'error' => 'Permission denied'], 403);
        }

        $media = $this->mediaService->getMedia($mediaId);
        if (!$media) {
            return new JsonResponse(['success' => false, 'error' => 'Media not found'], 404);
        }

        $data = [
            'title' => $request->request->get('title'),
            'alt_text' => $request->request->get('alt_text'),
            'description' => $request->request->get('description'),
            'caption' => $request->request->get('caption')
        ];

        try {
            $this->mediaService->updateMedia($mediaId, $data);
            return new JsonResponse(['success' => true, 'message' => 'Media updated successfully']);
        } catch (\Exception $e) {
            return new JsonResponse(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Delete media
     */
    public function delete(Request $request, $parameters)
    {
        $this->request = $request;
        $mediaId = $parameters['id'] ?? null;

        // Check permission
        if (!auth()->can('media.delete')) {
            return new JsonResponse(['success' => false, 'error' => 'Permission denied'], 403);
        }

        $permanent = $request->request->get('permanent', false);

        try {
            $this->mediaService->deleteMedia($mediaId, $permanent);
            $message = $permanent ? 'Media permanently deleted' : 'Media moved to trash';
            return new JsonResponse(['success' => true, 'message' => $message]);
        } catch (\Exception $e) {
            return new JsonResponse(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Bulk operations
     */
    public function bulkAction(Request $request)
    {
        $this->request = $request;

        // Check permission
        if (!auth()->can('media.edit')) {
            return new JsonResponse(['success' => false, 'error' => 'Permission denied'], 403);
        }

        $action = $request->request->get('action');
        $ids = $request->request->get('ids', []);

        if (empty($ids) || !is_array($ids)) {
            return new JsonResponse(['success' => false, 'error' => 'No items selected'], 400);
        }

        try {
            switch ($action) {
                case 'delete':
                    $permanent = $request->request->get('permanent', false);
                    $deleted = $this->mediaService->bulkDeleteMedia($ids, $permanent);
                    $message = $permanent ? 
                        "Permanently deleted {$deleted} files" : 
                        "Moved {$deleted} files to trash";
                    break;

                case 'restore':
                    $restored = 0;
                    foreach ($ids as $id) {
                        if ($this->mediaService->restoreMedia($id)) {
                            $restored++;
                        }
                    }
                    $message = "Restored {$restored} files";
                    break;

                default:
                    return new JsonResponse(['success' => false, 'error' => 'Invalid action'], 400);
            }

            return new JsonResponse(['success' => true, 'message' => $message]);
        } catch (\Exception $e) {
            return new JsonResponse(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Generate thumbnail
     */
    public function generateThumbnail(Request $request, $parameters)
    {
        $this->request = $request;
        $mediaId = $parameters['id'] ?? null;

        // Check permission
        if (!auth()->can('media.edit')) {
            return new JsonResponse(['success' => false, 'error' => 'Permission denied'], 403);
        }

        $width = (int) $request->query->get('width', 150);
        $height = (int) $request->query->get('height', 150);
        $crop = $request->query->get('crop', true);

        try {
            $thumbnailUrl = $this->mediaService->generateThumbnail($mediaId, $width, $height, $crop);
            
            if ($thumbnailUrl) {
                return new JsonResponse(['success' => true, 'thumbnail_url' => $thumbnailUrl]);
            } else {
                return new JsonResponse(['success' => false, 'error' => 'Failed to generate thumbnail'], 500);
            }
        } catch (\Exception $e) {
            return new JsonResponse(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Search media
     */
    public function search(Request $request)
    {
        $this->request = $request;

        // Check permission
        if (!auth()->can('media.view')) {
            return new JsonResponse(['success' => false, 'error' => 'Permission denied'], 403);
        }

        $query = $request->query->get('q', '');
        $type = $request->query->get('type');
        $limit = (int) $request->query->get('limit', 20);

        if (strlen($query) < 2) {
            return new JsonResponse(['success' => false, 'error' => 'Query too short'], 400);
        }

        try {
            $results = $this->mediaService->searchMedia($query, $type, $limit);
            return new JsonResponse(['success' => true, 'results' => $results]);
        } catch (\Exception $e) {
            return new JsonResponse(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Media library modal for editor
     */
    public function library(Request $request)
    {
        $this->request = $request;

        // Check permission
        if (!auth()->can('media.view')) {
            return $this->forbidden('You do not have permission to view media.');
        }

        $type = $request->query->get('type', 'image');
        $multiple = $request->query->get('multiple', false);

        $filters = ['type' => $type];
        $result = $this->mediaService->getMediaList($filters, 1, 50);

        $data = [
            'title' => __('Media Library'),
            'media' => $result['data'],
            'type' => $type,
            'multiple' => $multiple,
            'layout' => 'modal'
        ];

        return $this->view('admin/media/library.twig', $data);
    }

    /**
     * Media statistics
     */
    public function statistics(Request $request)
    {
        $this->request = $request;

        // Check permission
        if (!auth()->can('media.view')) {
            return new JsonResponse(['success' => false, 'error' => 'Permission denied'], 403);
        }

        try {
            $stats = $this->mediaService->getMediaStatistics();
            return new JsonResponse(['success' => true, 'stats' => $stats]);
        } catch (\Exception $e) {
            return new JsonResponse(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Cleanup orphaned files
     */
    public function cleanup(Request $request)
    {
        $this->request = $request;

        // Check permission
        if (!auth()->can('media.manage')) {
            return new JsonResponse(['success' => false, 'error' => 'Permission denied'], 403);
        }

        try {
            $cleaned = $this->mediaService->cleanupOrphanedFiles();
            return new JsonResponse([
                'success' => true, 
                'message' => "Cleaned up {$cleaned} orphaned files"
            ]);
        } catch (\Exception $e) {
            return new JsonResponse(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Download media file
     */
    public function download(Request $request, $parameters)
    {
        $this->request = $request;
        $mediaId = $parameters['id'] ?? null;

        // Check permission
        if (!auth()->can('media.view')) {
            return $this->forbidden('You do not have permission to download media.');
        }

        $media = $this->mediaService->getMedia($mediaId);
        if (!$media) {
            return $this->notFound('Media not found');
        }

        $filePath = app()->getBasePath() . '/public/uploads/' . $media['file_path'];
        
        if (!file_exists($filePath)) {
            return $this->notFound('File not found');
        }

        $response = new \Symfony\Component\HttpFoundation\BinaryFileResponse($filePath);
        $response->setContentDisposition(
            \Symfony\Component\HttpFoundation\ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            $media['original_name']
        );

        return $response;
    }

    /**
     * Get media info for editor
     */
    public function getMediaInfo(Request $request, $parameters)
    {
        $this->request = $request;
        $mediaId = $parameters['id'] ?? null;

        $media = $this->mediaService->getMedia($mediaId);
        if (!$media) {
            return new JsonResponse(['success' => false, 'error' => 'Media not found'], 404);
        }

        $info = [
            'id' => $media['id'],
            'url' => $media['file_url'],
            'title' => $media['title'],
            'alt' => $media['alt_text'],
            'caption' => $media['caption'],
            'description' => $media['description'],
            'filename' => $media['original_name'],
            'filesize' => $media['file_size'],
            'type' => $media['file_type'],
            'mime_type' => $media['mime_type']
        ];

        if ($media['file_type'] === 'image') {
            $info['width'] = $media['width'];
            $info['height'] = $media['height'];
            $info['thumbnails'] = [
                'thumbnail' => $this->mediaService->getThumbnailUrl($media, 150, 150),
                'medium' => $this->mediaService->getThumbnailUrl($media, 300, 300),
                'large' => $this->mediaService->getThumbnailUrl($media, 600, 600)
            ];
        }

        return new JsonResponse(['success' => true, 'media' => $info]);
    }

    /**
     * Create new folder
     */
    public function createFolder(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('media.upload')) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'You do not have permission to create folders.'
                    ], 403);
                }

                return $this->forbidden('You do not have permission to create folders.');
            }

            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Invalid CSRF token'
                    ], 403);
                }

                $this->flashError(__('Security token mismatch. Please try again.'));
                return $this->back();
            }

            $folderName = $request->request->get('name', '');
            $parentFolder = $request->request->get('parent', '');

            if (empty($folderName)) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Folder name is required.'
                    ]);
                }

                $this->flashError(__('Folder name is required.'));
                return $this->back();
            }

            // Sanitize folder name
            $folderName = $this->sanitizationService->sanitizeFilename($folderName);

            // Create folder
            $result = $this->mediaService->createFolder($folderName, $parentFolder);

            if ($result['success']) {
                // Log activity
                $this->activityLogger->log('media_folder_created', [
                    'user_id' => auth()->id(),
                    'folder_name' => $folderName,
                    'parent_folder' => $parentFolder
                ]);

                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => true,
                        'message' => 'Folder created successfully.',
                        'folder' => $result['folder']
                    ]);
                }

                $this->flashSuccess(__('Folder created successfully.'));
            } else {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => $result['message']
                    ]);
                }

                $this->flashError($result['message']);
            }

            return $this->redirectToRoute('admin.media.index');

        } catch (\Exception $e) {
            $this->logError('media_folder_create_error', $e, $request);

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => __('An error occurred while creating folder.')
                ], 500);
            }

            $this->flashError(__('An error occurred while creating folder.'));
            return $this->back();
        }
    }

    /**
     * Bulk delete media files
     */
    public function bulkDelete(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('media.delete')) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'You do not have permission to delete media.'
                    ], 403);
                }

                return $this->forbidden('You do not have permission to delete media.');
            }

            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Invalid CSRF token'
                    ], 403);
                }

                $this->flashError(__('Security token mismatch. Please try again.'));
                return $this->back();
            }

            $mediaIds = $request->request->get('media_ids', []);

            if (empty($mediaIds)) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'No media files selected.'
                    ]);
                }

                $this->flashError(__('No media files selected.'));
                return $this->back();
            }

            $successCount = 0;
            $errorCount = 0;
            $results = [];

            foreach ($mediaIds as $mediaId) {
                try {
                    $media = $this->mediaService->getMedia($mediaId);

                    if (!$media) {
                        $results[] = [
                            'id' => $mediaId,
                            'success' => false,
                            'message' => 'Media not found'
                        ];
                        $errorCount++;
                        continue;
                    }

                    // Check if user can delete this media
                    if (!auth()->can('media.manage_all') && $media['user_id'] != auth()->id()) {
                        $results[] = [
                            'id' => $mediaId,
                            'success' => false,
                            'message' => 'Permission denied'
                        ];
                        $errorCount++;
                        continue;
                    }

                    $deleteResult = $this->mediaService->deleteMedia($mediaId);

                    if ($deleteResult['success']) {
                        $results[] = [
                            'id' => $mediaId,
                            'success' => true,
                            'message' => 'Deleted successfully'
                        ];
                        $successCount++;

                        // Log activity
                        $this->activityLogger->log('media_deleted', [
                            'user_id' => auth()->id(),
                            'media_id' => $mediaId,
                            'filename' => $media['filename']
                        ]);
                    } else {
                        $results[] = [
                            'id' => $mediaId,
                            'success' => false,
                            'message' => $deleteResult['message']
                        ];
                        $errorCount++;
                    }

                } catch (\Exception $e) {
                    $results[] = [
                        'id' => $mediaId,
                        'success' => false,
                        'message' => 'Delete failed: ' . $e->getMessage()
                    ];
                    $errorCount++;
                }
            }

            $message = sprintf(
                '%d file(s) deleted successfully, %d failed.',
                $successCount,
                $errorCount
            );

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => $successCount > 0,
                    'message' => $message,
                    'results' => $results,
                    'stats' => [
                        'success' => $successCount,
                        'errors' => $errorCount,
                        'total' => count($mediaIds)
                    ]
                ]);
            }

            if ($successCount > 0) {
                $this->flashSuccess($message);
            } else {
                $this->flashError($message);
            }

            return $this->redirectToRoute('admin.media.index');

        } catch (\Exception $e) {
            $this->logError('media_bulk_delete_error', $e, $request);

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => __('An error occurred during bulk delete.')
                ], 500);
            }

            $this->flashError(__('An error occurred during bulk delete.'));
            return $this->back();
        }
    }

    /**
     * Validate CSRF token
     */
    private function validateCsrfToken(Request $request)
    {
        $token = $request->request->get('_token') ?: $request->headers->get('X-CSRF-TOKEN');

        if (!$token) {
            return false;
        }

        $sessionToken = session()->get('_token');
        return $sessionToken && hash_equals($sessionToken, $token);
    }

    /**
     * Log errors
     */
    private function logError($event, \Exception $e, Request $request)
    {
        $logData = [
            'event' => $event,
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'user_id' => auth()->id(),
            'ip_address' => $request->getClientIp(),
            'user_agent' => $request->headers->get('User-Agent'),
            'url' => $request->getUri()
        ];

        error_log(json_encode($logData), 3, storage_path('logs/media.log'));

        if ($this->activityLogger) {
            $this->activityLogger->log($event, $logData);
        }
    }
}
