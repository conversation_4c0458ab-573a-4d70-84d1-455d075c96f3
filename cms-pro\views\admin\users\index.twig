{% extends "admin/layout.twig" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="users-index-container">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ title }}</h1>
            <p class="text-muted mb-0">{{ __('Manage system users and their permissions') }}</p>
        </div>
        <div>
            {% if auth().can('users.create') %}
            <a href="{{ url('/admin/users/create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>{{ __('New User') }}
            </a>
            {% endif %}
        </div>
    </div>

    <!-- Filters Section -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ url('/admin/users') }}" class="row g-3" id="filters-form">
                <div class="col-md-4">
                    <label for="search" class="form-label">{{ __('Search') }}</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ current_filters.search }}" 
                           placeholder="{{ __('Search users...') }}">
                </div>
                
                <div class="col-md-2">
                    <label for="status" class="form-label">{{ __('Status') }}</label>
                    <select class="form-select" id="status" name="status">
                        <option value="all" {{ current_filters.status == 'all' ? 'selected' : '' }}>
                            {{ __('All Statuses') }}
                        </option>
                        {% for key, label in statuses %}
                        <option value="{{ key }}" {{ current_filters.status == key ? 'selected' : '' }}>
                            {{ label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="role" class="form-label">{{ __('Role') }}</label>
                    <select class="form-select" id="role" name="role">
                        <option value="">{{ __('All Roles') }}</option>
                        {% for role in roles %}
                        <option value="{{ role.slug }}" {{ current_filters.role == role.slug ? 'selected' : '' }}>
                            {{ role.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search me-1"></i>{{ __('Filter') }}
                        </button>
                        <a href="{{ url('/admin/users') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>{{ __('Clear') }}
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-body p-0">
            {% if users and users|length > 0 %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>{{ __('User') }}</th>
                            <th width="150">{{ __('Roles') }}</th>
                            <th width="120">{{ __('Status') }}</th>
                            <th width="150">{{ __('Joined') }}</th>
                            <th width="120">{{ __('Actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-container me-3">
                                        {% if user.avatar %}
                                        <img src="{{ asset(user.avatar) }}" alt="{{ user.first_name }} {{ user.last_name }}" 
                                             class="avatar rounded-circle">
                                        {% else %}
                                        <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center">
                                            {{ user.first_name|first|upper }}{{ user.last_name|first|upper }}
                                        </div>
                                        {% endif %}
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold">
                                            {% if auth().can('users.edit') %}
                                            <a href="{{ url('/admin/users/' ~ user.id ~ '/edit') }}" 
                                               class="text-decoration-none">
                                                {{ user.first_name }} {{ user.last_name }}
                                            </a>
                                            {% else %}
                                            {{ user.first_name }} {{ user.last_name }}
                                            {% endif %}
                                        </div>
                                        <div class="small text-muted">
                                            <i class="fas fa-envelope me-1"></i>{{ user.email }}
                                            {% if user.username %}
                                            <br><i class="fas fa-user me-1"></i>{{ user.username }}
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if user.role_names %}
                                    {% set role_list = user.role_names|split(', ') %}
                                    {% for role in role_list %}
                                    <span class="badge bg-primary me-1 mb-1">{{ role }}</span>
                                    {% endfor %}
                                {% else %}
                                    <span class="text-muted">{{ __('No roles') }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-{{ user.status == 'active' ? 'success' : (user.status == 'inactive' ? 'warning' : 'danger') }}">
                                    {{ statuses[user.status] }}
                                </span>
                                {% if user.status == 'banned' %}
                                <div class="small text-danger mt-1">
                                    <i class="fas fa-ban me-1"></i>{{ __('Banned') }}
                                </div>
                                {% endif %}
                            </td>
                            <td>
                                <div class="small">
                                    <div>{{ user.created_at|date('M j, Y') }}</div>
                                    <div class="text-muted">{{ user.created_at|date('H:i') }}</div>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    {% if auth().can('users.edit') %}
                                    <a href="{{ url('/admin/users/' ~ user.id ~ '/edit') }}" 
                                       class="btn btn-outline-primary" 
                                       data-bs-toggle="tooltip" 
                                       title="{{ __('Edit') }}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                    
                                    {% if auth().can('users.delete') and user.id != auth().id() %}
                                    <button type="button" 
                                            class="btn btn-outline-danger delete-user" 
                                            data-user-id="{{ user.id }}"
                                            data-user-name="{{ user.first_name }} {{ user.last_name }}"
                                            data-bs-toggle="tooltip" 
                                            title="{{ __('Delete') }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if pagination.pages > 1 %}
            <div class="card-footer bg-white border-top">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="text-muted small">
                        {{ __('Showing :from to :to of :total users', {
                            from: ((pagination.current_page - 1) * pagination.per_page) + 1,
                            to: min(pagination.current_page * pagination.per_page, pagination.total),
                            total: pagination.total
                        }) }}
                    </div>
                    
                    <nav aria-label="Users pagination">
                        <ul class="pagination pagination-sm mb-0">
                            {% if pagination.current_page > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url('/admin/users') }}?{{ http_build_query(current_filters|merge({page: pagination.current_page - 1})) }}">
                                    {{ __('Previous') }}
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for page in range(max(1, pagination.current_page - 2), min(pagination.pages, pagination.current_page + 2)) %}
                            <li class="page-item {{ page == pagination.current_page ? 'active' : '' }}">
                                <a class="page-link" href="{{ url('/admin/users') }}?{{ http_build_query(current_filters|merge({page: page})) }}">
                                    {{ page }}
                                </a>
                            </li>
                            {% endfor %}
                            
                            {% if pagination.current_page < pagination.pages %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url('/admin/users') }}?{{ http_build_query(current_filters|merge({page: pagination.current_page + 1})) }}">
                                    {{ __('Next') }}
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            </div>
            {% endif %}
            
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">{{ __('No users found') }}</h5>
                <p class="text-muted">
                    {% if current_filters.search or current_filters.status != 'all' or current_filters.role %}
                        {{ __('Try adjusting your filters or') }}
                        <a href="{{ url('/admin/users') }}">{{ __('clear all filters') }}</a>
                    {% else %}
                        {{ __('Get started by creating your first user.') }}
                    {% endif %}
                </p>
                {% if auth().can('users.create') %}
                <a href="{{ url('/admin/users/create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>{{ __('Create First User') }}
                </a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Confirm Delete') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>{{ __('Are you sure you want to delete this user?') }}</p>
                <p><strong class="user-name-placeholder"></strong></p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {{ __('This action will deactivate the user account. This can be undone by changing the user status.') }}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ __('Cancel') }}
                </button>
                <button type="button" class="btn btn-danger" id="confirmDelete">
                    <i class="fas fa-trash me-2"></i>{{ __('Delete User') }}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
/**
 * Users Index Management
 * Enhanced with security and user experience features
 */
class UsersIndex {
    constructor() {
        this.deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        this.currentDeleteId = null;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupTooltips();
        this.setupAutoSubmit();
    }

    setupEventListeners() {
        // Delete buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.delete-user') || e.target.closest('.delete-user')) {
                e.preventDefault();
                const button = e.target.matches('.delete-user') ? e.target : e.target.closest('.delete-user');
                this.showDeleteModal(button.dataset.userId, button.dataset.userName);
            }
        });

        // Confirm delete
        document.getElementById('confirmDelete').addEventListener('click', () => {
            this.deleteUser(this.currentDeleteId);
        });
    }

    setupTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    setupAutoSubmit() {
        // Auto-submit filters on change (with debounce)
        let timeout;
        const searchInput = document.getElementById('search');

        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    document.getElementById('filters-form').submit();
                }, 500);
            });
        }

        // Auto-submit on select changes
        const selects = document.querySelectorAll('#status, #role');
        selects.forEach(select => {
            select.addEventListener('change', () => {
                document.getElementById('filters-form').submit();
            });
        });
    }

    showDeleteModal(userId, userName) {
        this.currentDeleteId = userId;
        document.querySelector('.user-name-placeholder').textContent = userName;
        this.deleteModal.show();
    }

    async deleteUser(userId) {
        try {
            const response = await fetch(`/admin/users/${userId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': this.getCsrfToken()
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');
                this.deleteModal.hide();

                // Remove row from table
                const row = document.querySelector(`button[data-user-id="${userId}"]`).closest('tr');
                if (row) {
                    row.remove();
                }

            } else {
                this.showNotification(result.message, 'error');
            }

        } catch (error) {
            console.error('Delete error:', error);
            this.showNotification('An error occurred while deleting the user.', 'error');
        }
    }

    getCsrfToken() {
        return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
        notification.innerHTML = `
            ${this.escapeHtml(message)}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    new UsersIndex();
});
</script>

<style>
/* Users Index Styles */
.users-index-container .table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.avatar-container {
    width: 48px;
    height: 48px;
}

.avatar {
    width: 48px;
    height: 48px;
    object-fit: cover;
}

.avatar-placeholder {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.025);
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

.badge {
    font-size: 0.75rem;
}

/* Status badges */
.badge.bg-success {
    background-color: #28a745 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

.badge.bg-primary {
    background-color: #007bff !important;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .btn-group-sm .btn {
        padding: 0.125rem 0.25rem;
    }

    .avatar-container {
        width: 40px;
        height: 40px;
    }

    .avatar,
    .avatar-placeholder {
        width: 40px;
        height: 40px;
        font-size: 0.75rem;
    }
}

/* Dark mode support */
[data-bs-theme="dark"] .table th {
    color: #adb5bd;
}

[data-bs-theme="dark"] .avatar-placeholder {
    background: linear-gradient(135deg, #4c63d2 0%, #5a4fcf 100%);
}

[data-bs-theme="dark"] .badge.bg-warning {
    color: #000;
}
</style>
{% endblock %}
