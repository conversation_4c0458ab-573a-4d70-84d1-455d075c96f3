{% extends "layouts/frontend.twig" %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center min-vh-75">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1 class="display-4 fw-bold mb-4">
                        {{ page.title ?? site_settings.site_name }}
                    </h1>
                    {% if page.content %}
                    <p class="lead mb-4">
                        {{ page.content | striptags | slice(0, 200) }}...
                    </p>
                    {% else %}
                    <p class="lead mb-4">
                        {{ site_settings.site_description }}
                    </p>
                    {% endif %}
                    <div class="hero-actions">
                        <a href="#features" class="btn btn-light btn-lg me-3">
                            <i class="fas fa-rocket me-2"></i>{{ __('Get Started') }}
                        </a>
                        <a href="{{ url('/contact') }}" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-envelope me-2"></i>{{ __('Contact Us') }}
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-image-container">
                    {% if page.featured_image %}
                    <img src="{{ page.featured_image }}" alt="{{ page.title }}" class="hero-image img-fluid rounded shadow-lg">
                    {% else %}
                    <div class="hero-placeholder d-flex align-items-center justify-content-center bg-white bg-opacity-10 rounded shadow-lg">
                        <i class="fas fa-image fa-5x text-white-50"></i>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section id="features" class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="display-5 fw-bold">{{ __('Why Choose Us?') }}</h2>
                <p class="lead text-muted">
                    {{ __('Discover the features that make our platform stand out from the rest.') }}
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-4">
                <div class="feature-card h-100 p-4 text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-bolt fa-3x text-primary"></i>
                    </div>
                    <h4>{{ __('Fast & Reliable') }}</h4>
                    <p class="text-muted">
                        {{ __('Built with performance in mind, our platform delivers lightning-fast results.') }}
                    </p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="feature-card h-100 p-4 text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-shield-alt fa-3x text-success"></i>
                    </div>
                    <h4>{{ __('Secure & Safe') }}</h4>
                    <p class="text-muted">
                        {{ __('Your data is protected with enterprise-grade security measures.') }}
                    </p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="feature-card h-100 p-4 text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-users fa-3x text-info"></i>
                    </div>
                    <h4>{{ __('User Friendly') }}</h4>
                    <p class="text-muted">
                        {{ __('Intuitive interface designed for users of all skill levels.') }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Recent Pages Section -->
{% if recent_pages and recent_pages|length > 0 %}
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="display-5 fw-bold">{{ __('Latest Content') }}</h2>
                <p class="lead text-muted">
                    {{ __('Check out our most recent pages and updates.') }}
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            {% for recent_page in recent_pages %}
            <div class="col-lg-4 col-md-6">
                <article class="card h-100 shadow-sm">
                    {% if recent_page.featured_image %}
                    <img src="{{ recent_page.featured_image }}" class="card-img-top" alt="{{ recent_page.title }}" style="height: 200px; object-fit: cover;">
                    {% else %}
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                        <i class="fas fa-file-alt fa-3x text-muted"></i>
                    </div>
                    {% endif %}
                    
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">
                            <a href="{{ url('/' ~ recent_page.slug) }}" class="text-decoration-none">
                                {{ recent_page.title }}
                            </a>
                        </h5>
                        
                        {% if recent_page.excerpt %}
                        <p class="card-text text-muted flex-grow-1">
                            {{ recent_page.excerpt | slice(0, 120) }}...
                        </p>
                        {% endif %}
                        
                        <div class="card-meta mt-auto">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                {{ recent_page.created_at | date(site_settings.date_format) }}
                            </small>
                            {% if recent_page.views > 0 %}
                            <small class="text-muted ms-3">
                                <i class="fas fa-eye me-1"></i>
                                {{ recent_page.views }} {{ __('views') }}
                            </small>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="card-footer bg-transparent">
                        <a href="{{ url('/' ~ recent_page.slug) }}" class="btn btn-outline-primary btn-sm">
                            {{ __('Read More') }} <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </article>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Call to Action Section -->
<section class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h2 class="display-6 fw-bold mb-3">{{ __('Ready to Get Started?') }}</h2>
                <p class="lead mb-0">
                    {{ __('Join thousands of satisfied users who trust our platform for their needs.') }}
                </p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="{{ url('/contact') }}" class="btn btn-light btn-lg">
                    <i class="fas fa-paper-plane me-2"></i>{{ __('Contact Us Today') }}
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="py-5">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-3 col-6 mb-4">
                <div class="stat-item">
                    <div class="stat-number display-4 fw-bold text-primary">1000+</div>
                    <div class="stat-label text-muted">{{ __('Happy Customers') }}</div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-4">
                <div class="stat-item">
                    <div class="stat-number display-4 fw-bold text-success">99.9%</div>
                    <div class="stat-label text-muted">{{ __('Uptime') }}</div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-4">
                <div class="stat-item">
                    <div class="stat-number display-4 fw-bold text-info">24/7</div>
                    <div class="stat-label text-muted">{{ __('Support') }}</div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-4">
                <div class="stat-item">
                    <div class="stat-number display-4 fw-bold text-warning">5★</div>
                    <div class="stat-label text-muted">{{ __('Rating') }}</div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
// Homepage specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Animate statistics on scroll
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const statNumbers = entry.target.querySelectorAll('.stat-number');
                statNumbers.forEach(stat => {
                    stat.style.transform = 'scale(1.1)';
                    stat.style.transition = 'transform 0.3s ease';
                    
                    setTimeout(() => {
                        stat.style.transform = 'scale(1)';
                    }, 300);
                });
            }
        });
    }, observerOptions);
    
    const statsSection = document.querySelector('.stat-item').closest('section');
    if (statsSection) {
        observer.observe(statsSection);
    }
    
    // Smooth scroll for hero CTA
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Add hover effects to feature cards
    document.querySelectorAll('.feature-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.transition = 'transform 0.3s ease';
            this.style.boxShadow = '0 10px 25px rgba(0,0,0,0.1)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
});
</script>

<style>
/* Homepage specific styles */
.hero-section {
    background: linear-gradient(135deg, #007bff, #17a2b8);
    color: white;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.hero-placeholder {
    height: 400px;
}

.feature-card {
    background: white;
    border-radius: 10px;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.feature-icon {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-item {
    padding: 1rem;
}

.stat-number {
    line-height: 1;
    margin-bottom: 0.5rem;
}

.card-meta {
    font-size: 0.875rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-section {
        min-height: 60vh;
        text-align: center;
    }
    
    .hero-actions {
        flex-direction: column;
        gap: 1rem;
    }
    
    .hero-actions .btn {
        width: 100%;
        margin: 0 !important;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .display-5 {
        font-size: 1.75rem;
    }
}

/* Dark mode support */
[data-bs-theme="dark"] .feature-card {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
}

[data-bs-theme="dark"] .text-muted {
    color: #a0aec0 !important;
}
</style>
{% endblock %}
