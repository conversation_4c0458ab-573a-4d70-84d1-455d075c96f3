-- Create settings table
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_setting_key (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default settings
INSERT INTO settings (setting_key, setting_value) VALUES
-- General Settings
('site_name', '"CMS Pro"'),
('site_description', '"A powerful content management system"'),
('site_url', '"http://localhost"'),
('admin_email', '"<EMAIL>"'),
('timezone', '"UTC"'),
('date_format', '"Y-m-d"'),
('time_format', '"H:i:s"'),
('language', '"en"'),
('maintenance_mode', 'false'),
('site_logo', '""'),
('site_favicon', '""'),

-- SEO Settings
('seo_title', '""'),
('seo_description', '""'),
('seo_keywords', '""'),
('robots_txt', '"User-agent: *\\nDisallow:"'),
('google_analytics', '""'),
('google_search_console', '""'),
('facebook_pixel', '""'),

-- Media Settings
('max_upload_size', '10'),
('allowed_file_types', '"jpg,jpeg,png,gif,pdf,doc,docx,txt"'),
('image_quality', '85'),
('thumbnail_width', '300'),
('thumbnail_height', '300'),

-- Email Settings
('mail_driver', '"smtp"'),
('mail_host', '"localhost"'),
('mail_port', '587'),
('mail_username', '""'),
('mail_password', '""'),
('mail_encryption', '"tls"'),
('mail_from_address', '"<EMAIL>"'),
('mail_from_name', '"CMS Pro"'),

-- Security Settings
('password_min_length', '8'),
('password_require_uppercase', 'true'),
('password_require_lowercase', 'true'),
('password_require_numbers', 'true'),
('password_require_symbols', 'false'),
('session_lifetime', '120'),
('max_login_attempts', '5'),
('lockout_duration', '15'),

-- Advanced Settings
('debug_mode', 'false'),
('log_level', '"error"'),
('cache_enabled', 'true'),
('cache_lifetime', '60'),
('compression_enabled', 'true')

ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);
