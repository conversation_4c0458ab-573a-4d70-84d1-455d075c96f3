<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ __('Test Email') }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .email-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .email-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .email-body {
            padding: 40px 30px;
            text-align: center;
        }
        .email-body h2 {
            color: #333;
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 20px;
        }
        .email-body p {
            margin-bottom: 20px;
            color: #666;
        }
        .success-icon {
            font-size: 48px;
            color: #28a745;
            margin-bottom: 20px;
        }
        .test-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        .test-info h3 {
            color: #0056b3;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
        }
        .test-info table {
            width: 100%;
            border-collapse: collapse;
        }
        .test-info td {
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
            color: #495057;
            font-size: 14px;
        }
        .test-info td:first-child {
            font-weight: 600;
            width: 40%;
        }
        .test-info td:last-child {
            font-family: monospace;
        }
        .email-footer {
            background-color: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }
        .email-footer p {
            margin: 0;
            color: #6c757d;
            font-size: 14px;
        }
        @media only screen and (max-width: 600px) {
            body {
                padding: 10px;
            }
            .email-header, .email-body, .email-footer {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <h1>{{ app_name }}</h1>
        </div>
        
        <div class="email-body">
            <div class="success-icon">✅</div>
            
            <h2>{{ __('Email Configuration Test') }}</h2>
            
            <p>{{ __('Congratulations! Your email configuration is working correctly.') }}</p>
            
            <p>{{ __('This test email was sent successfully from your CMS Pro installation.') }}</p>
            
            <div class="test-info">
                <h3>{{ __('Test Details') }}</h3>
                <table>
                    <tr>
                        <td>{{ __('Application') }}:</td>
                        <td>{{ app_name }}</td>
                    </tr>
                    <tr>
                        <td>{{ __('Test Time') }}:</td>
                        <td>{{ test_time }}</td>
                    </tr>
                    <tr>
                        <td>{{ __('Mail Driver') }}:</td>
                        <td>SMTP</td>
                    </tr>
                    <tr>
                        <td>{{ __('Status') }}:</td>
                        <td style="color: #28a745; font-weight: 600;">{{ __('Success') }}</td>
                    </tr>
                </table>
            </div>
            
            <p>{{ __('You can now use email features such as:') }}</p>
            
            <ul style="text-align: left; display: inline-block;">
                <li>{{ __('Password reset emails') }}</li>
                <li>{{ __('User registration confirmations') }}</li>
                <li>{{ __('Contact form notifications') }}</li>
                <li>{{ __('System notifications') }}</li>
                <li>{{ __('Newsletter subscriptions') }}</li>
            </ul>
            
            <p>{{ __('If you received this email, your mail configuration is working perfectly!') }}</p>
        </div>
        
        <div class="email-footer">
            <p>{{ __('This is a test email from your CMS Pro installation.') }}</p>
            <p>&copy; {{ "now"|date("Y") }} {{ app_name }}. {{ __('All rights reserved.') }}</p>
        </div>
    </div>
</body>
</html>
