<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 CMS Pro Debug Information</h1>";

// Check if autoloader exists
if (file_exists('../vendor/autoload.php')) {
    echo "<p style='color: green;'>✅ Composer autoloader: EXISTS</p>";
    require_once '../vendor/autoload.php';
} else {
    echo "<p style='color: red;'>❌ Composer autoloader: NOT FOUND</p>";
    exit;
}

// Check if bootstrap exists
if (file_exists('../bootstrap/app.php')) {
    echo "<p style='color: green;'>✅ Bootstrap file: EXISTS</p>";
} else {
    echo "<p style='color: red;'>❌ Bootstrap file: NOT FOUND</p>";
}

// Check .env file
if (file_exists('../.env')) {
    echo "<p style='color: green;'>✅ Environment file: EXISTS</p>";
} else {
    echo "<p style='color: red;'>❌ Environment file: NOT FOUND</p>";
}

// Test basic routing
echo "<h2>🛣️ Testing Basic Routing</h2>";

try {
    // Simulate a basic request
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['REQUEST_URI'] = '/';
    $_SERVER['HTTP_HOST'] = 'localhost';
    
    // Try to load the application
    if (file_exists('../src/Core/Application.php')) {
        echo "<p style='color: green;'>✅ Application class: EXISTS</p>";
        
        // Test database connection
        $config = [
            'driver' => 'mysql',
            'host' => 'localhost',
            'database' => 'cms_pro',
            'username' => 'root',
            'password' => '',
            'charset' => 'utf8mb4'
        ];
        
        $dsn = "mysql:host={$config['host']};dbname={$config['database']};charset={$config['charset']}";
        $pdo = new PDO($dsn, $config['username'], $config['password']);
        echo "<p style='color: green;'>✅ Database connection: SUCCESS</p>";
        
        // Check tables
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        echo "<p>📊 Database tables (" . count($tables) . "): " . implode(', ', $tables) . "</p>";
        
    } else {
        echo "<p style='color: red;'>❌ Application class: NOT FOUND</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>📁 Directory Structure</h2>";
$dirs = ['src', 'vendor', 'public', 'storage', 'views', 'config'];
foreach ($dirs as $dir) {
    $path = "../{$dir}";
    if (is_dir($path)) {
        echo "<p style='color: green;'>✅ {$dir}/: EXISTS</p>";
    } else {
        echo "<p style='color: red;'>❌ {$dir}/: NOT FOUND</p>";
    }
}

echo "<h2>🔗 Quick Links</h2>";
echo "<ul>";
echo "<li><a href='test.php'>Database Test</a></li>";
echo "<li><a href='index.php'>Frontend</a></li>";
echo "<li><a href='admin/'>Admin Panel</a></li>";
echo "</ul>";

echo "<h2>📋 Login Information</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<strong>Admin Panel:</strong> <a href='admin/'>http://localhost/cms-pro/public/admin</a><br>";
echo "<strong>Email:</strong> <EMAIL><br>";
echo "<strong>Password:</strong> admin123";
echo "</div>";
?>
