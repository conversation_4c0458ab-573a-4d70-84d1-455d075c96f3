<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ __('Reset Your Password') }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .email-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .email-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .email-body {
            padding: 40px 30px;
        }
        .email-body h2 {
            color: #333;
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 20px;
        }
        .email-body p {
            margin-bottom: 20px;
            color: #666;
        }
        .reset-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 5px;
            font-weight: 600;
            margin: 20px 0;
            text-align: center;
        }
        .reset-button:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .security-notice {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .security-notice h3 {
            color: #856404;
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .security-notice p {
            color: #856404;
            margin-bottom: 0;
            font-size: 14px;
        }
        .email-footer {
            background-color: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }
        .email-footer p {
            margin: 0;
            color: #6c757d;
            font-size: 14px;
        }
        .link-fallback {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
            color: #495057;
        }
        @media only screen and (max-width: 600px) {
            body {
                padding: 10px;
            }
            .email-header, .email-body, .email-footer {
                padding: 20px;
            }
            .reset-button {
                display: block;
                width: 100%;
                box-sizing: border-box;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <h1>{{ config('app.name') }}</h1>
        </div>
        
        <div class="email-body">
            <h2>{{ __('Reset Your Password') }}</h2>
            
            <p>{{ __('Hello :name,', {name: user.firstName}) }}</p>
            
            <p>{{ __('We received a request to reset the password for your account. If you made this request, click the button below to reset your password:') }}</p>
            
            <div style="text-align: center;">
                <a href="{{ reset_url }}" class="reset-button">{{ __('Reset Password') }}</a>
            </div>
            
            <p>{{ __('This password reset link will expire in :expires.', {expires: expires_in}) }}</p>
            
            <div class="security-notice">
                <h3>{{ __('Security Notice') }}</h3>
                <p>{{ __('If you did not request a password reset, please ignore this email. Your password will remain unchanged.') }}</p>
            </div>
            
            <p>{{ __('If the button above doesn\'t work, you can copy and paste the following link into your browser:') }}</p>
            
            <div class="link-fallback">
                {{ reset_url }}
            </div>
            
            <p>{{ __('For security reasons, this link will only work once and will expire automatically.') }}</p>
            
            <p>{{ __('If you continue to have problems, please contact our support team.') }}</p>
            
            <p>{{ __('Best regards,') }}<br>{{ __('The :app Team', {app: config('app.name')}) }}</p>
        </div>
        
        <div class="email-footer">
            <p>{{ __('This is an automated message, please do not reply to this email.') }}</p>
            <p>&copy; {{ "now"|date("Y") }} {{ config('app.name') }}. {{ __('All rights reserved.') }}</p>
        </div>
    </div>
</body>
</html>
