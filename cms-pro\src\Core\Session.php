<?php

namespace CmsPro\Core;

use Symfony\Component\HttpFoundation\Session\Session as SymfonySession;
use Symfony\Component\HttpFoundation\Session\Storage\NativeSessionStorage;
use Symfony\Component\HttpFoundation\Session\Storage\Handler\FileSessionHandler;

/**
 * Session Manager
 * 
 * @package CmsPro\Core
 */
class Session
{
    private $session;

    public function __construct()
    {
        $this->initializeSession();
    }

    /**
     * Initialize session
     */
    private function initializeSession()
    {
        $sessionPath = storage_path('sessions');
        
        if (!is_dir($sessionPath)) {
            mkdir($sessionPath, 0755, true);
        }

        $handler = new FileSessionHandler($sessionPath);
        $storage = new NativeSessionStorage([
            'cookie_lifetime' => config('session.lifetime', 120) * 60,
            'cookie_secure' => config('session.secure', false),
            'cookie_httponly' => true,
            'cookie_samesite' => 'Lax',
            'use_strict_mode' => true,
            'name' => config('session.cookie', 'cms_pro_session'),
        ], $handler);

        $this->session = new SymfonySession($storage);
        $this->session->start();

        // Generate CSRF token if not exists
        if (!$this->has('_token')) {
            $this->put('_token', $this->generateToken());
        }
    }

    /**
     * Get session value
     */
    public function get($key, $default = null)
    {
        return $this->session->get($key, $default);
    }

    /**
     * Set session value
     */
    public function put($key, $value)
    {
        $this->session->set($key, $value);
    }

    /**
     * Check if session has key
     */
    public function has($key)
    {
        return $this->session->has($key);
    }

    /**
     * Remove session key
     */
    public function forget($key)
    {
        $this->session->remove($key);
    }

    /**
     * Clear all session data
     */
    public function flush()
    {
        $this->session->clear();
    }

    /**
     * Regenerate session ID
     */
    public function regenerate($destroy = false)
    {
        $this->session->migrate($destroy);
    }

    /**
     * Get session ID
     */
    public function getId()
    {
        return $this->session->getId();
    }

    /**
     * Set session ID
     */
    public function setId($id)
    {
        $this->session->setId($id);
    }

    /**
     * Flash data for next request
     */
    public function flash($key, $value)
    {
        $this->session->getFlashBag()->add($key, $value);
    }

    /**
     * Get flash data
     */
    public function getFlash($key, $default = [])
    {
        return $this->session->getFlashBag()->get($key, $default);
    }

    /**
     * Check if has flash data
     */
    public function hasFlash($key)
    {
        return $this->session->getFlashBag()->has($key);
    }

    /**
     * Get all flash data
     */
    public function getAllFlash()
    {
        return $this->session->getFlashBag()->all();
    }

    /**
     * Flash input data
     */
    public function flashInput($input)
    {
        $this->flash('old_input', $input);
    }

    /**
     * Get old input
     */
    public function getOldInput($key = null, $default = null)
    {
        $oldInput = $this->getFlash('old_input', []);
        
        if ($key === null) {
            return $oldInput;
        }
        
        return $oldInput[0][$key] ?? $default;
    }

    /**
     * Generate secure token
     */
    private function generateToken($length = 32)
    {
        return bin2hex(random_bytes($length));
    }

    /**
     * Get CSRF token
     */
    public function getToken()
    {
        return $this->get('_token');
    }

    /**
     * Verify CSRF token
     */
    public function verifyToken($token)
    {
        return hash_equals($this->getToken(), $token);
    }

    /**
     * Get all session data
     */
    public function all()
    {
        return $this->session->all();
    }

    /**
     * Save session
     */
    public function save()
    {
        $this->session->save();
    }

    /**
     * Get underlying session instance
     */
    public function getSession()
    {
        return $this->session;
    }
}
