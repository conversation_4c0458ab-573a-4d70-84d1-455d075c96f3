<!DOCTYPE html>
<html lang="{{ app.locale }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    
    <title>{{ title }} - {{ app.name }}</title>
    
    <meta name="description" content="{{ meta_description ?? 'Admin Panel Login' }}">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ asset('css/admin.css') }}" rel="stylesheet">
</head>
<body class="admin-login-page">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h2>{{ app.name }}</h2>
                <p>{{ __('Admin Panel') }}</p>
            </div>
            
            <div class="login-body">
                <!-- Flash Messages -->
                {% for type, messages in session.getAllFlash() %}
                    {% for message in messages %}
                    <div class="alert alert-{{ type == 'error' ? 'danger' : type }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    {% endfor %}
                {% endfor %}
                
                <form method="POST" action="{{ url('/admin/login') }}" class="needs-validation" novalidate>
                    {{ csrf_field() | raw }}
                    
                    <div class="form-floating mb-3">
                        <input 
                            type="text" 
                            class="form-control" 
                            id="login" 
                            name="login" 
                            value="{{ old('login') }}"
                            placeholder="{{ __('Email or Username') }}"
                            required 
                            autofocus
                        >
                        <label for="login">
                            <i class="fas fa-user me-2"></i>
                            {{ __('Email or Username') }}
                        </label>
                        <div class="invalid-feedback">
                            {{ __('Please enter your email or username.') }}
                        </div>
                    </div>

                    <div class="form-floating mb-3">
                        <input 
                            type="password" 
                            class="form-control" 
                            id="password" 
                            name="password" 
                            placeholder="{{ __('Password') }}"
                            required
                        >
                        <label for="password">
                            <i class="fas fa-lock me-2"></i>
                            {{ __('Password') }}
                        </label>
                        <div class="password-toggle" onclick="togglePassword('password')">
                            <i class="fas fa-eye" id="password-toggle-icon"></i>
                        </div>
                        <div class="invalid-feedback">
                            {{ __('Please enter your password.') }}
                        </div>
                    </div>

                    <div class="form-check mb-4">
                        <input 
                            type="checkbox" 
                            class="form-check-input" 
                            id="remember" 
                            name="remember" 
                            value="1"
                        >
                        <label class="form-check-label" for="remember">
                            {{ __('Keep me signed in') }}
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary btn-lg w-100 mb-3">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        {{ __('Sign In') }}
                    </button>
                </form>
                
                <div class="text-center">
                    <a href="{{ url('/password/reset') }}" class="forgot-password-link">
                        <i class="fas fa-key me-1"></i>
                        {{ __('Forgot your password?') }}
                    </a>
                </div>
            </div>
            
            <!-- Demo Credentials (only in development) -->
            {% if app.debug %}
            <div class="demo-credentials">
                <div class="demo-header">
                    <i class="fas fa-info-circle me-2"></i>
                    {{ __('Demo Credentials') }}
                </div>
                <div class="demo-body">
                    <div class="credential-item">
                        <strong>{{ __('Super Admin:') }}</strong>
                        <span><EMAIL></span>
                        <span>password</span>
                    </div>
                    <div class="credential-item">
                        <strong>{{ __('Editor:') }}</strong>
                        <span><EMAIL></span>
                        <span>password</span>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
        
        <div class="login-footer">
            <p>&copy; {{ "now"|date("Y") }} {{ app.name }}. {{ __('All rights reserved.') }}</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    function togglePassword(fieldId) {
        const field = document.getElementById(fieldId);
        const icon = document.getElementById(fieldId + '-toggle-icon');
        
        if (field.type === 'password') {
            field.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            field.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    // Form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Auto-focus on first empty field
    document.addEventListener('DOMContentLoaded', function() {
        const loginField = document.getElementById('login');
        const passwordField = document.getElementById('password');
        
        if (loginField.value === '') {
            loginField.focus();
        } else {
            passwordField.focus();
        }
    });
    </script>
    
    <style>
    .admin-login-page {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .login-container {
        width: 100%;
        max-width: 400px;
        padding: 20px;
    }

    .login-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .login-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-align: center;
        padding: 40px 30px;
    }

    .login-header .logo {
        font-size: 3rem;
        margin-bottom: 15px;
    }

    .login-header h2 {
        margin: 0 0 5px 0;
        font-weight: 700;
    }

    .login-header p {
        margin: 0;
        opacity: 0.9;
        font-size: 0.9rem;
    }

    .login-body {
        padding: 40px 30px;
    }

    .form-floating {
        position: relative;
    }

    .password-toggle {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        color: #6c757d;
        z-index: 10;
    }

    .password-toggle:hover {
        color: #495057;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        font-weight: 600;
        padding: 12px;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-1px);
    }

    .forgot-password-link {
        color: #667eea;
        text-decoration: none;
        font-size: 0.9rem;
    }

    .forgot-password-link:hover {
        color: #5a6fd8;
        text-decoration: underline;
    }

    .demo-credentials {
        background: #fff3cd;
        border-top: 1px solid #ffeaa7;
        padding: 20px;
    }

    .demo-header {
        font-weight: 600;
        color: #856404;
        margin-bottom: 10px;
        font-size: 0.9rem;
    }

    .credential-item {
        display: flex;
        justify-content: space-between;
        font-size: 0.8rem;
        margin-bottom: 5px;
        color: #856404;
    }

    .credential-item strong {
        flex: 0 0 auto;
        margin-right: 10px;
    }

    .credential-item span:last-child {
        font-family: monospace;
        background: rgba(0, 0, 0, 0.1);
        padding: 2px 6px;
        border-radius: 3px;
    }

    .login-footer {
        text-align: center;
        margin-top: 20px;
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.8rem;
    }

    @media (max-width: 576px) {
        .login-container {
            padding: 10px;
        }
        
        .login-header {
            padding: 30px 20px;
        }
        
        .login-body {
            padding: 30px 20px;
        }
    }
    </style>
</body>
</html>
