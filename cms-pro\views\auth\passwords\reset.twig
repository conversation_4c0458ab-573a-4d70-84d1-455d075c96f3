{% extends "layouts/base.twig" %}

{% block title %}{{ __('Reset Password') }} - {{ app.name }}{% endblock %}

{% block body_class %}auth-page{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-lg border-0 mt-5">
                <div class="card-header bg-success text-white text-center py-4">
                    <h3 class="mb-0">
                        <i class="fas fa-lock me-2"></i>
                        {{ __('Reset Password') }}
                    </h3>
                </div>
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <p class="text-muted">
                            {{ __('Enter your new password below.') }}
                        </p>
                    </div>

                    <form method="POST" action="{{ url('/password/reset') }}" class="needs-validation" novalidate>
                        {{ csrf_field() | raw }}
                        <input type="hidden" name="token" value="{{ token }}">
                        <input type="hidden" name="email" value="{{ email }}">
                        
                        <div class="mb-3">
                            <label for="email_display" class="form-label">{{ __('Email Address') }}</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input 
                                    type="email" 
                                    class="form-control" 
                                    id="email_display" 
                                    value="{{ email }}"
                                    readonly
                                    disabled
                                >
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">{{ __('New Password') }}</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input 
                                    type="password" 
                                    class="form-control" 
                                    id="password" 
                                    name="password" 
                                    required
                                    autofocus
                                    minlength="8"
                                    placeholder="{{ __('Enter new password') }}"
                                >
                                <button 
                                    type="button" 
                                    class="btn btn-outline-secondary" 
                                    onclick="togglePassword('password')"
                                >
                                    <i class="fas fa-eye" id="password-toggle-icon"></i>
                                </button>
                                <div class="invalid-feedback">
                                    {{ __('Password must be at least 8 characters long.') }}
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="password_confirmation" class="form-label">{{ __('Confirm New Password') }}</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input 
                                    type="password" 
                                    class="form-control" 
                                    id="password_confirmation" 
                                    name="password_confirmation" 
                                    required
                                    minlength="8"
                                    placeholder="{{ __('Confirm new password') }}"
                                >
                                <button 
                                    type="button" 
                                    class="btn btn-outline-secondary" 
                                    onclick="togglePassword('password_confirmation')"
                                >
                                    <i class="fas fa-eye" id="password_confirmation-toggle-icon"></i>
                                </button>
                                <div class="invalid-feedback">
                                    {{ __('Please confirm your password.') }}
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-check me-2"></i>
                                {{ __('Reset Password') }}
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center py-3">
                    <a href="{{ url('/login') }}" class="text-decoration-none">
                        <i class="fas fa-arrow-left me-1"></i>
                        {{ __('Back to Login') }}
                    </a>
                </div>
            </div>
            
            <!-- Password Requirements -->
            <div class="card mt-3 border-warning">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        {{ __('Password Requirements') }}
                    </h6>
                </div>
                <div class="card-body py-3">
                    <small class="text-muted">
                        <ul class="mb-0 ps-3">
                            <li>{{ __('At least 8 characters long') }}</li>
                            <li>{{ __('Mix of uppercase and lowercase letters (recommended)') }}</li>
                            <li>{{ __('Include numbers and special characters (recommended)') }}</li>
                            <li>{{ __('Avoid common passwords and personal information') }}</li>
                        </ul>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '-toggle-icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Password confirmation validation
document.getElementById('password_confirmation').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmation = this.value;
    
    if (confirmation && password !== confirmation) {
        this.setCustomValidity('{{ __("Passwords do not match") }}');
    } else {
        this.setCustomValidity('');
    }
});

document.getElementById('password').addEventListener('input', function() {
    const confirmation = document.getElementById('password_confirmation');
    if (confirmation.value) {
        confirmation.dispatchEvent(new Event('input'));
    }
});

// Auto-focus on password field when page loads
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('password').focus();
});
</script>
{% endblock %}

{% block styles %}
{{ parent() }}
<style>
.auth-page .card-header.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.auth-page .btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    font-weight: 600;
}

.auth-page .btn-success:hover {
    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    transform: translateY(-1px);
}

.auth-page .input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
}

.auth-page .form-control:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.auth-page .form-control:disabled {
    background-color: #e9ecef;
    opacity: 1;
}

.auth-page .card-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

.auth-page .card-footer a {
    color: #28a745;
    font-weight: 500;
}

.auth-page .card-footer a:hover {
    color: #218838;
    text-decoration: underline !important;
}

.auth-page ul {
    font-size: 0.85rem;
}

.auth-page ul li {
    margin-bottom: 0.25rem;
}
</style>
{% endblock %}
