<?php

namespace CmsPro\Core;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Route;
use Symfony\Component\Routing\RouteCollection;
use Symfony\Component\Routing\RequestContext;
use Symfony\Component\Routing\Matcher\UrlMatcher;
use Symfony\Component\Routing\Generator\UrlGenerator;
use Symfony\Component\Routing\Exception\RouteNotFoundException;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;

/**
 * Router Class
 * 
 * @package CmsPro\Core
 */
class Router
{
    private $routes;
    private $middleware = [];
    private $currentRoute;

    public function __construct()
    {
        $this->routes = new RouteCollection();
    }

    /**
     * Add GET route
     */
    public function get($path, $controller, $name = null)
    {
        return $this->addRoute(['GET'], $path, $controller, $name);
    }

    /**
     * Add POST route
     */
    public function post($path, $controller, $name = null)
    {
        return $this->addRoute(['POST'], $path, $controller, $name);
    }

    /**
     * Add PUT route
     */
    public function put($path, $controller, $name = null)
    {
        return $this->addRoute(['PUT'], $path, $controller, $name);
    }

    /**
     * Add DELETE route
     */
    public function delete($path, $controller, $name = null)
    {
        return $this->addRoute(['DELETE'], $path, $controller, $name);
    }

    /**
     * Add PATCH route
     */
    public function patch($path, $controller, $name = null)
    {
        return $this->addRoute(['PATCH'], $path, $controller, $name);
    }

    /**
     * Add route for multiple methods
     */
    public function match($methods, $path, $controller, $name = null)
    {
        return $this->addRoute($methods, $path, $controller, $name);
    }

    /**
     * Add route for all methods
     */
    public function any($path, $controller, $name = null)
    {
        return $this->addRoute(['GET', 'POST', 'PUT', 'DELETE', 'PATCH'], $path, $controller, $name);
    }

    /**
     * Add route
     */
    private function addRoute($methods, $path, $controller, $name = null)
    {
        $route = new Route($path, [
            '_controller' => $controller
        ], [], [], '', [], $methods);

        $routeName = $name ?: $this->generateRouteName($path, $methods);
        $this->routes->add($routeName, $route);

        return $this;
    }

    /**
     * Generate route name
     */
    private function generateRouteName($path, $methods)
    {
        $method = strtolower($methods[0]);
        $name = trim($path, '/');
        $name = str_replace(['/', '{', '}'], ['.', '', ''], $name);
        $name = $name ?: 'home';
        
        return $method . '.' . $name;
    }

    /**
     * Add middleware
     */
    public function addMiddleware($middleware)
    {
        $this->middleware[] = $middleware;
        return $this;
    }

    /**
     * Dispatch request
     */
    public function dispatch(Request $request)
    {
        $context = new RequestContext();
        $context->fromRequest($request);

        $matcher = new UrlMatcher($this->routes, $context);

        try {
            $parameters = $matcher->match($request->getPathInfo());
            $this->currentRoute = $parameters;

            // Execute middleware
            foreach ($this->middleware as $middleware) {
                $response = $middleware->handle($request, function($request) use ($parameters) {
                    return $this->callController($request, $parameters);
                });

                if ($response instanceof Response) {
                    return $response;
                }
            }

            return $this->callController($request, $parameters);

        } catch (ResourceNotFoundException $e) {
            return $this->handleNotFound($request);
        } catch (\Exception $e) {
            return $this->handleError($request, $e);
        }
    }

    /**
     * Call controller
     */
    private function callController(Request $request, $parameters)
    {
        $controller = $parameters['_controller'];

        if (is_string($controller)) {
            if (strpos($controller, '@') !== false) {
                list($class, $method) = explode('@', $controller);
            } else {
                $class = $controller;
                $method = 'index';
            }

            if (!class_exists($class)) {
                throw new \RuntimeException("Controller class [{$class}] not found.");
            }

            $controllerInstance = new $class();
            
            if (!method_exists($controllerInstance, $method)) {
                throw new \RuntimeException("Method [{$method}] not found in controller [{$class}].");
            }

            $response = $controllerInstance->$method($request, $parameters);
        } elseif (is_callable($controller)) {
            $response = $controller($request, $parameters);
        } else {
            throw new \RuntimeException("Invalid controller type.");
        }

        if (!$response instanceof Response) {
            $response = new Response($response);
        }

        return $response;
    }

    /**
     * Handle 404 Not Found
     */
    private function handleNotFound(Request $request)
    {
        $response = new Response('Page Not Found', 404);
        return $response;
    }

    /**
     * Handle errors
     */
    private function handleError(Request $request, \Exception $e)
    {
        $response = new Response('Internal Server Error: ' . $e->getMessage(), 500);
        return $response;
    }

    /**
     * Generate URL
     */
    public function generate($name, $parameters = [])
    {
        $context = new RequestContext();
        $generator = new UrlGenerator($this->routes, $context);

        try {
            return $generator->generate($name, $parameters);
        } catch (RouteNotFoundException $e) {
            throw new \RuntimeException("Route [{$name}] not found.");
        }
    }

    /**
     * Get current route
     */
    public function getCurrentRoute()
    {
        return $this->currentRoute;
    }

    /**
     * Get all routes
     */
    public function getRoutes()
    {
        return $this->routes;
    }

    /**
     * Group routes with common attributes
     */
    public function group($attributes, callable $callback)
    {
        $callback($this);
        return $this;
    }
}
