{% extends "layouts/admin.twig" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="media-library-container">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ title }}</h1>
            <p class="text-muted mb-0">{{ __('Manage your media files and folders') }}</p>
        </div>
        <div class="btn-group">
            {% if auth().can('media.upload') %}
            <button type="button" class="btn btn-primary" id="upload-btn">
                <i class="fas fa-upload me-2"></i>{{ __('Upload Files') }}
            </button>
            <button type="button" class="btn btn-outline-primary" id="create-folder-btn">
                <i class="fas fa-folder-plus me-2"></i>{{ __('New Folder') }}
            </button>
            {% endif %}
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon bg-primary">
                                <i class="fas fa-file"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="stat-value">{{ stats.total_files }}</div>
                            <div class="stat-label">{{ __('Total Files') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon bg-success">
                                <i class="fas fa-images"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="stat-value">{{ stats.images }}</div>
                            <div class="stat-label">{{ __('Images') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon bg-info">
                                <i class="fas fa-file-alt"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="stat-value">{{ stats.documents }}</div>
                            <div class="stat-label">{{ __('Documents') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon bg-warning">
                                <i class="fas fa-hdd"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="stat-value">{{ (stats.total_size / 1024 / 1024)|number_format(1) }}MB</div>
                            <div class="stat-label">{{ __('Storage Used') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <!-- Folders -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-folder me-2"></i>{{ __('Folders') }}
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div class="folder-tree">
                        <div class="folder-item {{ current_filters.folder == '' ? 'active' : '' }}" data-folder="">
                            <a href="{{ url('/admin/media') }}" class="folder-link">
                                <i class="fas fa-home me-2"></i>{{ __('All Files') }}
                            </a>
                        </div>
                        {% for folder in folders %}
                        <div class="folder-item {{ current_filters.folder == folder.path ? 'active' : '' }}" data-folder="{{ folder.path }}">
                            <a href="{{ url('/admin/media') }}?folder={{ folder.path }}" class="folder-link">
                                <i class="fas fa-folder me-2"></i>{{ folder.name }}
                                {% if folder.file_count > 0 %}
                                <span class="badge bg-light text-dark ms-auto">{{ folder.file_count }}</span>
                                {% endif %}
                            </a>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-filter me-2"></i>{{ __('Filters') }}
                    </h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ url('/admin/media') }}" id="filters-form">
                        <input type="hidden" name="folder" value="{{ current_filters.folder }}">
                        
                        <!-- Search -->
                        <div class="mb-3">
                            <label for="search" class="form-label">{{ __('Search') }}</label>
                            <input type="text" class="form-control form-control-sm" id="search" name="search" 
                                   value="{{ current_filters.search }}" 
                                   placeholder="{{ __('Search files...') }}">
                        </div>
                        
                        <!-- File Type -->
                        <div class="mb-3">
                            <label for="type" class="form-label">{{ __('File Type') }}</label>
                            <select class="form-select form-select-sm" id="type" name="type">
                                <option value="all" {{ current_filters.type == 'all' ? 'selected' : '' }}>
                                    {{ __('All Types') }}
                                </option>
                                <option value="image" {{ current_filters.type == 'image' ? 'selected' : '' }}>
                                    {{ __('Images') }}
                                </option>
                                <option value="document" {{ current_filters.type == 'document' ? 'selected' : '' }}>
                                    {{ __('Documents') }}
                                </option>
                                <option value="video" {{ current_filters.type == 'video' ? 'selected' : '' }}>
                                    {{ __('Videos') }}
                                </option>
                                <option value="audio" {{ current_filters.type == 'audio' ? 'selected' : '' }}>
                                    {{ __('Audio') }}
                                </option>
                            </select>
                        </div>
                        
                        <!-- View Mode -->
                        <div class="mb-3">
                            <label for="view" class="form-label">{{ __('View Mode') }}</label>
                            <select class="form-select form-select-sm" id="view" name="view">
                                <option value="grid" {{ current_filters.view == 'grid' ? 'selected' : '' }}>
                                    {{ __('Grid View') }}
                                </option>
                                <option value="list" {{ current_filters.view == 'list' ? 'selected' : '' }}>
                                    {{ __('List View') }}
                                </option>
                            </select>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-search me-1"></i>{{ __('Apply Filters') }}
                            </button>
                            <a href="{{ url('/admin/media') }}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times me-1"></i>{{ __('Clear') }}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Toolbar -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body py-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <!-- Breadcrumb -->
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb mb-0">
                                    <li class="breadcrumb-item">
                                        <a href="{{ url('/admin/media') }}">{{ __('Media') }}</a>
                                    </li>
                                    {% if current_filters.folder %}
                                    {% set folder_parts = current_filters.folder|split('/') %}
                                    {% for part in folder_parts %}
                                    <li class="breadcrumb-item {{ loop.last ? 'active' : '' }}">
                                        {% if not loop.last %}
                                        <a href="{{ url('/admin/media') }}?folder={{ folder_parts|slice(0, loop.index)|join('/') }}">{{ part }}</a>
                                        {% else %}
                                        {{ part }}
                                        {% endif %}
                                    </li>
                                    {% endfor %}
                                    {% endif %}
                                </ol>
                            </nav>
                        </div>
                        
                        <div class="d-flex align-items-center gap-2">
                            <!-- Selection Actions -->
                            <div class="selection-actions" style="display: none;">
                                <span class="selected-count me-2">0 {{ __('selected') }}</span>
                                {% if auth().can('media.delete') %}
                                <button type="button" class="btn btn-outline-danger btn-sm" id="bulk-delete-btn">
                                    <i class="fas fa-trash me-1"></i>{{ __('Delete') }}
                                </button>
                                {% endif %}
                            </div>
                            
                            <!-- View Toggle -->
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-secondary {{ current_filters.view == 'grid' ? 'active' : '' }}" 
                                        data-view="grid">
                                    <i class="fas fa-th"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary {{ current_filters.view == 'list' ? 'active' : '' }}" 
                                        data-view="list">
                                    <i class="fas fa-list"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Media Grid/List -->
            <div class="media-container">
                {% if media_files and media_files|length > 0 %}
                <div class="media-grid {{ current_filters.view == 'list' ? 'media-list' : '' }}">
                    {% for media in media_files %}
                    <div class="media-item" data-media-id="{{ media.id }}" data-media-type="{{ media.file_type }}">
                        <div class="media-item-inner">
                            <!-- Selection Checkbox -->
                            <div class="media-selection">
                                <input type="checkbox" class="form-check-input media-checkbox" value="{{ media.id }}">
                            </div>
                            
                            <!-- Media Preview -->
                            <div class="media-preview">
                                {% if media.file_type == 'image' %}
                                <img src="{{ media.thumbnail_url }}" alt="{{ media.alt_text }}" class="media-thumbnail">
                                {% else %}
                                <div class="media-icon">
                                    <i class="fas fa-{{ media.file_type == 'document' ? 'file-alt' : (media.file_type == 'video' ? 'play-circle' : 'file') }}"></i>
                                </div>
                                {% endif %}
                            </div>
                            
                            <!-- Media Info -->
                            <div class="media-info">
                                <div class="media-title">{{ media.title ?: media.original_name }}</div>
                                <div class="media-meta">
                                    <span class="media-size">{{ (media.file_size / 1024)|number_format(0) }}KB</span>
                                    {% if media.file_type == 'image' and media.width and media.height %}
                                    <span class="media-dimensions">{{ media.width }}×{{ media.height }}</span>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- Media Actions -->
                            <div class="media-actions">
                                <button type="button" class="btn btn-sm btn-outline-primary media-edit-btn" 
                                        data-media-id="{{ media.id }}">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-info media-copy-url-btn" 
                                        data-url="{{ media.file_url }}">
                                    <i class="fas fa-link"></i>
                                </button>
                                {% if auth().can('media.delete') and (auth().can('media.manage_all') or media.user_id == auth().id()) %}
                                <button type="button" class="btn btn-sm btn-outline-danger media-delete-btn" 
                                        data-media-id="{{ media.id }}" data-media-name="{{ media.original_name }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Pagination -->
                {% if pagination.pages > 1 %}
                <div class="d-flex justify-content-center mt-4">
                    <nav aria-label="Media pagination">
                        <ul class="pagination">
                            {% if pagination.current_page > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url('/admin/media') }}?{{ http_build_query(current_filters|merge({page: pagination.current_page - 1})) }}">
                                    {{ __('Previous') }}
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for page in range(max(1, pagination.current_page - 2), min(pagination.pages, pagination.current_page + 2)) %}
                            <li class="page-item {{ page == pagination.current_page ? 'active' : '' }}">
                                <a class="page-link" href="{{ url('/admin/media') }}?{{ http_build_query(current_filters|merge({page: page})) }}">
                                    {{ page }}
                                </a>
                            </li>
                            {% endfor %}
                            
                            {% if pagination.current_page < pagination.pages %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url('/admin/media') }}?{{ http_build_query(current_filters|merge({page: pagination.current_page + 1})) }}">
                                    {{ __('Next') }}
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-images fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{{ __('No media files found') }}</h5>
                    <p class="text-muted">
                        {% if current_filters.search or current_filters.type != 'all' or current_filters.folder %}
                            {{ __('Try adjusting your filters or') }}
                            <a href="{{ url('/admin/media') }}">{{ __('view all files') }}</a>
                        {% else %}
                            {{ __('Get started by uploading your first media file.') }}
                        {% endif %}
                    </p>
                    {% if auth().can('media.upload') %}
                    <button type="button" class="btn btn-primary" id="upload-btn-empty">
                        <i class="fas fa-upload me-2"></i>{{ __('Upload First File') }}
                    </button>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Upload Files') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="upload-area" id="upload-area">
                    <div class="upload-placeholder">
                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                        <h5>{{ __('Drag & drop files here') }}</h5>
                        <p class="text-muted">{{ __('or click to browse') }}</p>
                        <button type="button" class="btn btn-primary" id="browse-files-btn">
                            {{ __('Browse Files') }}
                        </button>
                    </div>
                    <input type="file" id="file-input" multiple style="display: none;">
                </div>
                
                <div class="upload-progress" id="upload-progress" style="display: none;">
                    <div class="progress mb-3">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <div class="upload-status"></div>
                </div>
                
                <div class="upload-limits mt-3">
                    <small class="text-muted">
                        {{ __('Max file size') }}: {{ upload_limits.max_file_size }}MB | 
                        {{ __('Allowed types') }}: {{ allowed_types|join(', ') }}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Folder Modal -->
<div class="modal fade" id="createFolderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Create New Folder') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="create-folder-form">
                    <input type="hidden" name="parent" value="{{ current_filters.folder }}">
                    <div class="mb-3">
                        <label for="folder-name" class="form-label">{{ __('Folder Name') }}</label>
                        <input type="text" class="form-control" id="folder-name" name="name" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ __('Cancel') }}
                </button>
                <button type="button" class="btn btn-primary" id="create-folder-submit">
                    {{ __('Create Folder') }}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
/**
 * Media Library Management
 * Enhanced with drag-drop upload and bulk operations
 */
class MediaLibrary {
    constructor() {
        this.uploadModal = new bootstrap.Modal(document.getElementById('uploadModal'));
        this.createFolderModal = new bootstrap.Modal(document.getElementById('createFolderModal'));
        this.selectedFiles = new Set();
        this.currentFolder = '{{ current_filters.folder }}';

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupDragDrop();
        this.setupFileSelection();
        this.setupViewToggle();
        this.setupAutoSubmit();
    }

    setupEventListeners() {
        // Upload buttons
        document.getElementById('upload-btn').addEventListener('click', () => {
            this.uploadModal.show();
        });

        const uploadBtnEmpty = document.getElementById('upload-btn-empty');
        if (uploadBtnEmpty) {
            uploadBtnEmpty.addEventListener('click', () => {
                this.uploadModal.show();
            });
        }

        // Create folder button
        document.getElementById('create-folder-btn').addEventListener('click', () => {
            this.createFolderModal.show();
        });

        // Browse files button
        document.getElementById('browse-files-btn').addEventListener('click', () => {
            document.getElementById('file-input').click();
        });

        // File input change
        document.getElementById('file-input').addEventListener('change', (e) => {
            this.handleFileSelect(e.target.files);
        });

        // Create folder form
        document.getElementById('create-folder-submit').addEventListener('click', () => {
            this.createFolder();
        });

        // Bulk delete
        const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
        if (bulkDeleteBtn) {
            bulkDeleteBtn.addEventListener('click', () => {
                this.bulkDelete();
            });
        }

        // Media actions
        document.addEventListener('click', (e) => {
            if (e.target.matches('.media-copy-url-btn') || e.target.closest('.media-copy-url-btn')) {
                const button = e.target.matches('.media-copy-url-btn') ? e.target : e.target.closest('.media-copy-url-btn');
                this.copyUrl(button.dataset.url);
            }

            if (e.target.matches('.media-delete-btn') || e.target.closest('.media-delete-btn')) {
                const button = e.target.matches('.media-delete-btn') ? e.target : e.target.closest('.media-delete-btn');
                this.deleteMedia(button.dataset.mediaId, button.dataset.mediaName);
            }
        });
    }

    setupDragDrop() {
        const uploadArea = document.getElementById('upload-area');

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, this.preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => {
                uploadArea.classList.add('drag-over');
            }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => {
                uploadArea.classList.remove('drag-over');
            }, false);
        });

        uploadArea.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            this.handleFileSelect(files);
        }, false);
    }

    setupFileSelection() {
        // Select all checkbox functionality
        document.addEventListener('change', (e) => {
            if (e.target.matches('.media-checkbox')) {
                const mediaId = e.target.value;
                if (e.target.checked) {
                    this.selectedFiles.add(mediaId);
                } else {
                    this.selectedFiles.delete(mediaId);
                }
                this.updateSelectionUI();
            }
        });
    }

    setupViewToggle() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-view]')) {
                const view = e.target.dataset.view;
                const url = new URL(window.location);
                url.searchParams.set('view', view);
                window.location.href = url.toString();
            }
        });
    }

    setupAutoSubmit() {
        // Auto-submit filters on change (with debounce)
        let timeout;
        const searchInput = document.getElementById('search');

        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    document.getElementById('filters-form').submit();
                }, 500);
            });
        }

        // Auto-submit on select changes
        const selects = document.querySelectorAll('#type');
        selects.forEach(select => {
            select.addEventListener('change', () => {
                document.getElementById('filters-form').submit();
            });
        });
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    async handleFileSelect(files) {
        if (files.length === 0) return;

        const formData = new FormData();

        for (let file of files) {
            formData.append('files[]', file);
        }

        formData.append('folder', this.currentFolder);
        formData.append('_token', this.getCsrfToken());

        this.showUploadProgress();

        try {
            const response = await fetch('/admin/media/upload', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');
                this.uploadModal.hide();

                // Reload page to show new files
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                this.showNotification(result.message, 'error');
                this.hideUploadProgress();
            }

        } catch (error) {
            console.error('Upload error:', error);
            this.showNotification('Upload failed', 'error');
            this.hideUploadProgress();
        }
    }

    async createFolder() {
        const form = document.getElementById('create-folder-form');
        const formData = new FormData(form);
        formData.append('_token', this.getCsrfToken());

        try {
            const response = await fetch('/admin/media/create-folder', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');
                this.createFolderModal.hide();

                // Reload page to show new folder
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                this.showNotification(result.message, 'error');
            }

        } catch (error) {
            console.error('Create folder error:', error);
            this.showNotification('Failed to create folder', 'error');
        }
    }

    async bulkDelete() {
        if (this.selectedFiles.size === 0) {
            this.showNotification('No files selected', 'warning');
            return;
        }

        if (!confirm(`Are you sure you want to delete ${this.selectedFiles.size} file(s)?`)) {
            return;
        }

        const formData = new FormData();
        this.selectedFiles.forEach(id => {
            formData.append('media_ids[]', id);
        });
        formData.append('_token', this.getCsrfToken());

        try {
            const response = await fetch('/admin/media/bulk-delete', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');

                // Remove deleted items from UI
                this.selectedFiles.forEach(id => {
                    const item = document.querySelector(`[data-media-id="${id}"]`);
                    if (item) item.remove();
                });

                this.selectedFiles.clear();
                this.updateSelectionUI();
            } else {
                this.showNotification(result.message, 'error');
            }

        } catch (error) {
            console.error('Bulk delete error:', error);
            this.showNotification('Delete failed', 'error');
        }
    }

    async deleteMedia(mediaId, mediaName) {
        if (!confirm(`Are you sure you want to delete "${mediaName}"?`)) {
            return;
        }

        try {
            const response = await fetch(`/admin/media/${mediaId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': this.getCsrfToken()
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');

                // Remove item from UI
                const item = document.querySelector(`[data-media-id="${mediaId}"]`);
                if (item) item.remove();
            } else {
                this.showNotification(result.message, 'error');
            }

        } catch (error) {
            console.error('Delete error:', error);
            this.showNotification('Delete failed', 'error');
        }
    }

    copyUrl(url) {
        navigator.clipboard.writeText(url).then(() => {
            this.showNotification('URL copied to clipboard', 'success');
        }).catch(() => {
            this.showNotification('Failed to copy URL', 'error');
        });
    }

    updateSelectionUI() {
        const selectionActions = document.querySelector('.selection-actions');
        const selectedCount = document.querySelector('.selected-count');

        if (this.selectedFiles.size > 0) {
            selectionActions.style.display = 'block';
            selectedCount.textContent = `${this.selectedFiles.size} {{ __('selected') }}`;
        } else {
            selectionActions.style.display = 'none';
        }
    }

    showUploadProgress() {
        document.querySelector('.upload-placeholder').style.display = 'none';
        document.getElementById('upload-progress').style.display = 'block';
    }

    hideUploadProgress() {
        document.querySelector('.upload-placeholder').style.display = 'block';
        document.getElementById('upload-progress').style.display = 'none';
    }

    getCsrfToken() {
        return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
        notification.innerHTML = `
            ${this.escapeHtml(message)}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    new MediaLibrary();
});
</script>

<style>
/* Media Library Styles */
.media-library-container .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: #718096;
    margin-top: 0.25rem;
}

/* Folder Tree */
.folder-tree {
    max-height: 300px;
    overflow-y: auto;
}

.folder-item {
    border-bottom: 1px solid #f1f3f4;
}

.folder-item:last-child {
    border-bottom: none;
}

.folder-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #495057;
    text-decoration: none;
    transition: all 0.2s ease;
}

.folder-link:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
}

.folder-item.active .folder-link {
    background-color: #e3f2fd;
    color: #0d6efd;
    font-weight: 500;
}

/* Media Grid */
.media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.media-list {
    grid-template-columns: 1fr;
}

.media-item {
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    overflow: hidden;
    transition: all 0.2s ease;
    position: relative;
}

.media-item:hover {
    border-color: #0d6efd;
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.15);
}

.media-item-inner {
    position: relative;
}

.media-selection {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    z-index: 2;
}

.media-preview {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    overflow: hidden;
}

.media-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.media-icon {
    font-size: 3rem;
    color: #6c757d;
}

.media-info {
    padding: 0.75rem;
}

.media-title {
    font-weight: 500;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.media-meta {
    font-size: 0.75rem;
    color: #6c757d;
}

.media-actions {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    display: flex;
    gap: 0.25rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.media-item:hover .media-actions {
    opacity: 1;
}

/* List View */
.media-list .media-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
}

.media-list .media-preview {
    width: 60px;
    height: 60px;
    aspect-ratio: unset;
    flex-shrink: 0;
    margin-right: 1rem;
    border-radius: 0.375rem;
}

.media-list .media-info {
    flex-grow: 1;
    padding: 0;
}

.media-list .media-actions {
    position: static;
    opacity: 1;
}

/* Upload Area */
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.5rem;
    padding: 3rem 2rem;
    text-align: center;
    transition: all 0.2s ease;
}

.upload-area.drag-over {
    border-color: #0d6efd;
    background-color: #f0f8ff;
}

.upload-placeholder {
    color: #6c757d;
}

/* Responsive */
@media (max-width: 768px) {
    .media-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 0.75rem;
    }

    .stat-value {
        font-size: 1.25rem;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .media-actions {
        opacity: 1;
    }
}

/* Dark mode support */
[data-bs-theme="dark"] .stat-value {
    color: #e2e8f0;
}

[data-bs-theme="dark"] .stat-label {
    color: #a0aec0;
}

[data-bs-theme="dark"] .folder-link {
    color: #e2e8f0;
}

[data-bs-theme="dark"] .folder-link:hover {
    background-color: #374151;
    color: #60a5fa;
}

[data-bs-theme="dark"] .folder-item.active .folder-link {
    background-color: #1e3a8a;
    color: #93c5fd;
}

[data-bs-theme="dark"] .media-item {
    border-color: #4a5568;
    background-color: #2d3748;
}

[data-bs-theme="dark"] .media-preview {
    background-color: #374151;
}

[data-bs-theme="dark"] .media-icon {
    color: #9ca3af;
}

[data-bs-theme="dark"] .upload-area {
    border-color: #4a5568;
    background-color: #2d3748;
}

[data-bs-theme="dark"] .upload-area.drag-over {
    border-color: #60a5fa;
    background-color: #1e3a8a;
}
</style>
{% endblock %}
