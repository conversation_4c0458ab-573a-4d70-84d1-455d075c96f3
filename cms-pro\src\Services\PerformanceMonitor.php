<?php

namespace CmsPro\Services;

/**
 * Performance Monitor Service
 * 
 * Monitors application performance and provides optimization insights
 */
class PerformanceMonitor
{
    private $startTime;
    private $startMemory;
    private $queries = [];
    private $cacheHits = 0;
    private $cacheMisses = 0;
    private $config;

    public function __construct()
    {
        $this->config = require __DIR__ . '/../../config/performance.php';
        $this->startTime = microtime(true);
        $this->startMemory = memory_get_usage(true);
    }

    /**
     * Start monitoring a request
     */
    public function startRequest()
    {
        $this->startTime = microtime(true);
        $this->startMemory = memory_get_usage(true);
        $this->queries = [];
        $this->cacheHits = 0;
        $this->cacheMisses = 0;
    }

    /**
     * End monitoring and log results
     */
    public function endRequest()
    {
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        
        $metrics = [
            'response_time' => $endTime - $this->startTime,
            'memory_used' => $endMemory - $this->startMemory,
            'memory_peak' => memory_get_peak_usage(true),
            'query_count' => count($this->queries),
            'cache_hits' => $this->cacheHits,
            'cache_misses' => $this->cacheMisses,
            'cache_hit_ratio' => $this->getCacheHitRatio(),
            'url' => $_SERVER['REQUEST_URI'] ?? '',
            'method' => $_SERVER['REQUEST_METHOD'] ?? '',
            'timestamp' => date('Y-m-d H:i:s')
        ];

        $this->logMetrics($metrics);
        $this->checkThresholds($metrics);
        
        return $metrics;
    }

    /**
     * Log a database query
     */
    public function logQuery($sql, $time, $bindings = [])
    {
        $this->queries[] = [
            'sql' => $sql,
            'time' => $time,
            'bindings' => $bindings,
            'timestamp' => microtime(true)
        ];

        // Log slow queries
        if ($this->config['database']['slow_query_log'] && 
            $time > $this->config['database']['slow_query_threshold']) {
            $this->logSlowQuery($sql, $time, $bindings);
        }
    }

    /**
     * Log cache hit
     */
    public function logCacheHit($key)
    {
        $this->cacheHits++;
    }

    /**
     * Log cache miss
     */
    public function logCacheMiss($key)
    {
        $this->cacheMisses++;
    }

    /**
     * Get current performance metrics
     */
    public function getCurrentMetrics()
    {
        $currentTime = microtime(true);
        $currentMemory = memory_get_usage(true);
        
        return [
            'response_time' => $currentTime - $this->startTime,
            'memory_used' => $currentMemory - $this->startMemory,
            'memory_peak' => memory_get_peak_usage(true),
            'query_count' => count($this->queries),
            'cache_hits' => $this->cacheHits,
            'cache_misses' => $this->cacheMisses,
            'cache_hit_ratio' => $this->getCacheHitRatio()
        ];
    }

    /**
     * Get performance report
     */
    public function getPerformanceReport($period = '24h')
    {
        $logFile = $this->getLogFile();
        
        if (!file_exists($logFile)) {
            return [
                'avg_response_time' => 0,
                'avg_memory_usage' => 0,
                'total_requests' => 0,
                'slow_requests' => 0,
                'cache_hit_ratio' => 0,
                'top_slow_queries' => []
            ];
        }

        $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $metrics = [];
        $cutoff = $this->getPeriodCutoff($period);

        foreach ($lines as $line) {
            $data = json_decode($line, true);
            if ($data && strtotime($data['timestamp']) > $cutoff) {
                $metrics[] = $data;
            }
        }

        return $this->analyzeMetrics($metrics);
    }

    /**
     * Get optimization suggestions
     */
    public function getOptimizationSuggestions()
    {
        $report = $this->getPerformanceReport();
        $suggestions = [];

        // Response time suggestions
        if ($report['avg_response_time'] > 2.0) {
            $suggestions[] = [
                'type' => 'response_time',
                'priority' => 'high',
                'message' => 'Average response time is high. Consider enabling caching or optimizing database queries.',
                'value' => $report['avg_response_time']
            ];
        }

        // Memory usage suggestions
        if ($report['avg_memory_usage'] > 100 * 1024 * 1024) { // 100MB
            $suggestions[] = [
                'type' => 'memory_usage',
                'priority' => 'medium',
                'message' => 'High memory usage detected. Consider optimizing data structures or enabling object caching.',
                'value' => $report['avg_memory_usage']
            ];
        }

        // Cache hit ratio suggestions
        if ($report['cache_hit_ratio'] < 0.7) {
            $suggestions[] = [
                'type' => 'cache_hit_ratio',
                'priority' => 'medium',
                'message' => 'Low cache hit ratio. Review caching strategy and TTL settings.',
                'value' => $report['cache_hit_ratio']
            ];
        }

        // Slow queries suggestions
        if (!empty($report['top_slow_queries'])) {
            $suggestions[] = [
                'type' => 'slow_queries',
                'priority' => 'high',
                'message' => 'Slow database queries detected. Consider adding indexes or optimizing queries.',
                'queries' => array_slice($report['top_slow_queries'], 0, 5)
            ];
        }

        return $suggestions;
    }

    /**
     * Clear performance logs
     */
    public function clearLogs()
    {
        $logFile = $this->getLogFile();
        $slowQueryLog = $this->getSlowQueryLogFile();
        
        if (file_exists($logFile)) {
            unlink($logFile);
        }
        
        if (file_exists($slowQueryLog)) {
            unlink($slowQueryLog);
        }
        
        return true;
    }

    /**
     * Get cache hit ratio
     */
    private function getCacheHitRatio()
    {
        $total = $this->cacheHits + $this->cacheMisses;
        return $total > 0 ? $this->cacheHits / $total : 0;
    }

    /**
     * Log performance metrics
     */
    private function logMetrics($metrics)
    {
        if (!$this->config['monitoring']['enabled']) {
            return;
        }

        $logFile = $this->getLogFile();
        $logEntry = json_encode($metrics) . "\n";
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }

    /**
     * Log slow query
     */
    private function logSlowQuery($sql, $time, $bindings)
    {
        $logFile = $this->getSlowQueryLogFile();
        $logEntry = json_encode([
            'sql' => $sql,
            'time' => $time,
            'bindings' => $bindings,
            'timestamp' => date('Y-m-d H:i:s'),
            'url' => $_SERVER['REQUEST_URI'] ?? '',
            'user_id' => auth()->id() ?? null
        ]) . "\n";
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }

    /**
     * Check performance thresholds
     */
    private function checkThresholds($metrics)
    {
        $config = $this->config['monitoring'];
        
        // Check slow request threshold
        if ($config['log_slow_requests'] && 
            $metrics['response_time'] > $config['slow_request_threshold']) {
            $this->logSlowRequest($metrics);
        }

        // Check memory usage threshold
        $memoryLimit = $this->parseMemoryLimit($this->config['memory']['limit']);
        if ($metrics['memory_peak'] > $memoryLimit * $config['memory_usage_threshold']) {
            $this->logHighMemoryUsage($metrics);
        }
    }

    /**
     * Log slow request
     */
    private function logSlowRequest($metrics)
    {
        error_log("Slow request detected: {$metrics['response_time']}s - {$metrics['url']}");
    }

    /**
     * Log high memory usage
     */
    private function logHighMemoryUsage($metrics)
    {
        error_log("High memory usage: " . $this->formatBytes($metrics['memory_peak']) . " - {$metrics['url']}");
    }

    /**
     * Analyze metrics array
     */
    private function analyzeMetrics($metrics)
    {
        if (empty($metrics)) {
            return [
                'avg_response_time' => 0,
                'avg_memory_usage' => 0,
                'total_requests' => 0,
                'slow_requests' => 0,
                'cache_hit_ratio' => 0,
                'top_slow_queries' => []
            ];
        }

        $totalRequests = count($metrics);
        $totalResponseTime = array_sum(array_column($metrics, 'response_time'));
        $totalMemoryUsage = array_sum(array_column($metrics, 'memory_used'));
        $totalCacheHits = array_sum(array_column($metrics, 'cache_hits'));
        $totalCacheMisses = array_sum(array_column($metrics, 'cache_misses'));
        
        $slowRequests = array_filter($metrics, function($m) {
            return $m['response_time'] > $this->config['monitoring']['slow_request_threshold'];
        });

        return [
            'avg_response_time' => $totalResponseTime / $totalRequests,
            'avg_memory_usage' => $totalMemoryUsage / $totalRequests,
            'total_requests' => $totalRequests,
            'slow_requests' => count($slowRequests),
            'cache_hit_ratio' => ($totalCacheHits + $totalCacheMisses) > 0 ? 
                $totalCacheHits / ($totalCacheHits + $totalCacheMisses) : 0,
            'top_slow_queries' => $this->getTopSlowQueries()
        ];
    }

    /**
     * Get top slow queries
     */
    private function getTopSlowQueries()
    {
        $logFile = $this->getSlowQueryLogFile();
        
        if (!file_exists($logFile)) {
            return [];
        }

        $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $queries = [];

        foreach ($lines as $line) {
            $data = json_decode($line, true);
            if ($data) {
                $queries[] = $data;
            }
        }

        // Sort by time descending
        usort($queries, function($a, $b) {
            return $b['time'] <=> $a['time'];
        });

        return array_slice($queries, 0, 10);
    }

    /**
     * Get period cutoff timestamp
     */
    private function getPeriodCutoff($period)
    {
        switch ($period) {
            case '1h':
                return time() - 3600;
            case '24h':
                return time() - 86400;
            case '7d':
                return time() - 604800;
            case '30d':
                return time() - 2592000;
            default:
                return time() - 86400;
        }
    }

    /**
     * Parse memory limit string
     */
    private function parseMemoryLimit($limit)
    {
        $unit = strtolower(substr($limit, -1));
        $value = (int) substr($limit, 0, -1);
        
        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return (int) $limit;
        }
    }

    /**
     * Format bytes to human readable
     */
    private function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor((strlen($bytes) - 1) / 3);
        
        return sprintf("%.2f", $bytes / pow(1024, $factor)) . ' ' . $units[$factor];
    }

    /**
     * Get log file path
     */
    private function getLogFile()
    {
        $logDir = __DIR__ . '/../../storage/logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        return $logDir . '/performance.log';
    }

    /**
     * Get slow query log file path
     */
    private function getSlowQueryLogFile()
    {
        $logDir = __DIR__ . '/../../storage/logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        return $logDir . '/slow-queries.log';
    }
}
