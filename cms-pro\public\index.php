<?php

/**
 * CMS Pro - Working Entry Point
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple routing
$request_uri = $_SERVER['REQUEST_URI'] ?? '/';
$script_name = $_SERVER['SCRIPT_NAME'] ?? '';
$base_path = dirname($script_name);

// Remove base path from request URI
if ($base_path !== '/' && $base_path !== '') {
    $request_uri = substr($request_uri, strlen($base_path));
}

// Remove query string
$request_uri = strtok($request_uri, '?');

// Normalize URI
$request_uri = '/' . trim($request_uri, '/');
if ($request_uri === '/') {
    $request_uri = '/index.php';
}

// Basic routing
switch ($request_uri) {
    case '/':
    case '/index.php':
    case '/index_new.php':
        echo "<h1>🎉 CMS Pro - Hoş Geldiniz!</h1>";
        echo "<p>Sistem başarıyla kuruldu ve çalışıyor!</p>";
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>📋 Giriş Bilgileri:</h3>";
        echo "<strong>Admin Panel:</strong> <a href='index_new.php/admin'>Admin Panel</a><br>";
        echo "<strong>Email:</strong> <EMAIL><br>";
        echo "<strong>Password:</strong> admin123";
        echo "</div>";
        echo "<p><a href='debug.php'>🔧 Debug Bilgileri</a> | <a href='test.php'>🧪 Test Sayfası</a></p>";
        break;
        
    case '/admin':
    case '/admin/':
        // Simple admin login form
        if ($_POST['email'] ?? false) {
            // Handle login
            try {
                $pdo = new PDO('mysql:host=localhost;dbname=cms_pro', 'root', '');
                $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
                $stmt->execute([$_POST['email']]);
                $user = $stmt->fetch();
                
                if ($user && password_verify($_POST['password'], $user['password'])) {
                    session_start();
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
                    
                    echo "<h1>✅ Giriş Başarılı!</h1>";
                    echo "<p>Hoş geldiniz, " . htmlspecialchars($_SESSION['user_name']) . "!</p>";
                    echo "<p><a href='index_new.php/dashboard'>Dashboard'a Git</a></p>";
                } else {
                    echo "<h1>❌ Giriş Başarısız!</h1>";
                    echo "<p>Email veya şifre hatalı.</p>";
                    echo "<p><a href='index_new.php/admin'>Tekrar Dene</a></p>";
                }
            } catch (Exception $e) {
                echo "<h1>❌ Hata!</h1>";
                echo "<p>" . $e->getMessage() . "</p>";
            }
        } else {
            // Show login form
            echo "<h1>🔐 CMS Pro - Admin Girişi</h1>";
            echo "<form method='POST' style='max-width: 400px; margin: 20px 0;'>";
            echo "<div style='margin-bottom: 15px;'>";
            echo "<label>Email:</label><br>";
            echo "<input type='email' name='email' value='<EMAIL>' required style='width: 100%; padding: 8px; margin-top: 5px;'>";
            echo "</div>";
            echo "<div style='margin-bottom: 15px;'>";
            echo "<label>Şifre:</label><br>";
            echo "<input type='password' name='password' value='admin123' required style='width: 100%; padding: 8px; margin-top: 5px;'>";
            echo "</div>";
            echo "<button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;'>Giriş Yap</button>";
            echo "</form>";
            echo "<p><small>Varsayılan: <EMAIL> / admin123</small></p>";
            echo "<p><a href='index_new.php'>← Ana Sayfa</a></p>";
        }
        break;
        
    case '/dashboard':
        session_start();
        if (!isset($_SESSION['user_id'])) {
            header('Location: index_new.php/admin');
            exit;
        }
        
        echo "<h1>📊 CMS Pro Dashboard</h1>";
        echo "<p>Hoş geldiniz, " . htmlspecialchars($_SESSION['user_name']) . "!</p>";
        
        try {
            $pdo = new PDO('mysql:host=localhost;dbname=cms_pro', 'root', '');
            
            // Get statistics
            $pages = $pdo->query("SELECT COUNT(*) FROM pages")->fetchColumn();
            $posts = $pdo->query("SELECT COUNT(*) FROM blog_posts")->fetchColumn();
            $users = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
            $categories = $pdo->query("SELECT COUNT(*) FROM categories")->fetchColumn();
            
            echo "<div style='display: flex; gap: 20px; margin: 20px 0; flex-wrap: wrap;'>";
            echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; min-width: 150px;'>";
            echo "<h3>📄 Sayfalar</h3><h2 style='color: #007bff;'>{$pages}</h2>";
            echo "</div>";
            echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; min-width: 150px;'>";
            echo "<h3>📝 Blog Yazıları</h3><h2 style='color: #28a745;'>{$posts}</h2>";
            echo "</div>";
            echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; min-width: 150px;'>";
            echo "<h3>👥 Kullanıcılar</h3><h2 style='color: #ffc107;'>{$users}</h2>";
            echo "</div>";
            echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; min-width: 150px;'>";
            echo "<h3>📂 Kategoriler</h3><h2 style='color: #dc3545;'>{$categories}</h2>";
            echo "</div>";
            echo "</div>";
            
            // Recent content
            echo "<h3>📋 Son İçerikler</h3>";
            
            // Recent pages
            $recentPages = $pdo->query("SELECT title, created_at FROM pages ORDER BY created_at DESC LIMIT 5")->fetchAll();
            if ($recentPages) {
                echo "<h4>Son Sayfalar:</h4><ul>";
                foreach ($recentPages as $page) {
                    echo "<li>" . htmlspecialchars($page['title']) . " <small>(" . $page['created_at'] . ")</small></li>";
                }
                echo "</ul>";
            }
            
            // Recent posts
            $recentPosts = $pdo->query("SELECT title, created_at FROM blog_posts ORDER BY created_at DESC LIMIT 5")->fetchAll();
            if ($recentPosts) {
                echo "<h4>Son Blog Yazıları:</h4><ul>";
                foreach ($recentPosts as $post) {
                    echo "<li>" . htmlspecialchars($post['title']) . " <small>(" . $post['created_at'] . ")</small></li>";
                }
                echo "</ul>";
            }
            
            // Quick actions
            echo "<h3>⚡ Hızlı İşlemler</h3>";
            echo "<div style='display: flex; gap: 10px; flex-wrap: wrap;'>";
            echo "<a href='index_new.php/pages' style='background: #007bff; color: white; padding: 10px 15px; border-radius: 4px; text-decoration: none;'>📄 Sayfalar</a>";
            echo "<a href='index_new.php/posts' style='background: #28a745; color: white; padding: 10px 15px; border-radius: 4px; text-decoration: none;'>📝 Blog Yazıları</a>";
            echo "<a href='index_new.php/users' style='background: #ffc107; color: white; padding: 10px 15px; border-radius: 4px; text-decoration: none;'>👥 Kullanıcılar</a>";
            echo "<a href='index_new.php/settings' style='background: #6c757d; color: white; padding: 10px 15px; border-radius: 4px; text-decoration: none;'>⚙️ Ayarlar</a>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>Hata: " . $e->getMessage() . "</p>";
        }
        
        echo "<hr>";
        echo "<p><a href='index_new.php/logout'>Çıkış Yap</a> | <a href='index_new.php'>Ana Sayfa</a></p>";
        break;
        
    case '/pages':
        session_start();
        if (!isset($_SESSION['user_id'])) {
            header('Location: index_new.php/admin');
            exit;
        }
        
        echo "<h1>📄 Sayfa Yönetimi</h1>";
        
        try {
            $pdo = new PDO('mysql:host=localhost;dbname=cms_pro', 'root', '');
            $pages = $pdo->query("SELECT * FROM pages ORDER BY created_at DESC")->fetchAll();
            
            echo "<table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 10px; border: 1px solid #ddd; text-align: left;'>Başlık</th>";
            echo "<th style='padding: 10px; border: 1px solid #ddd; text-align: left;'>Durum</th>";
            echo "<th style='padding: 10px; border: 1px solid #ddd; text-align: left;'>Oluşturulma</th>";
            echo "<th style='padding: 10px; border: 1px solid #ddd; text-align: left;'>Görüntülenme</th>";
            echo "</tr>";
            
            foreach ($pages as $page) {
                echo "<tr>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . htmlspecialchars($page['title']) . "</td>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . ucfirst($page['status']) . "</td>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . $page['created_at'] . "</td>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . $page['views'] . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>Hata: " . $e->getMessage() . "</p>";
        }
        
        echo "<p><a href='index_new.php/dashboard'>← Dashboard</a></p>";
        break;
        
    case '/posts':
        session_start();
        if (!isset($_SESSION['user_id'])) {
            header('Location: index_new.php/admin');
            exit;
        }
        
        echo "<h1>📝 Blog Yazı Yönetimi</h1>";
        
        try {
            $pdo = new PDO('mysql:host=localhost;dbname=cms_pro', 'root', '');
            $posts = $pdo->query("
                SELECT bp.*, c.name as category_name 
                FROM blog_posts bp 
                LEFT JOIN categories c ON bp.category_id = c.id 
                ORDER BY bp.created_at DESC
            ")->fetchAll();
            
            echo "<table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 10px; border: 1px solid #ddd; text-align: left;'>Başlık</th>";
            echo "<th style='padding: 10px; border: 1px solid #ddd; text-align: left;'>Kategori</th>";
            echo "<th style='padding: 10px; border: 1px solid #ddd; text-align: left;'>Durum</th>";
            echo "<th style='padding: 10px; border: 1px solid #ddd; text-align: left;'>Görüntülenme</th>";
            echo "<th style='padding: 10px; border: 1px solid #ddd; text-align: left;'>Beğeni</th>";
            echo "</tr>";
            
            foreach ($posts as $post) {
                echo "<tr>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . htmlspecialchars($post['title']) . "</td>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . htmlspecialchars($post['category_name'] ?? 'Kategori Yok') . "</td>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . ucfirst($post['status']) . "</td>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . $post['views'] . "</td>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . $post['likes'] . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>Hata: " . $e->getMessage() . "</p>";
        }
        
        echo "<p><a href='index_new.php/dashboard'>← Dashboard</a></p>";
        break;
        
    case '/logout':
        session_start();
        session_destroy();
        echo "<h1>👋 Çıkış Yapıldı</h1>";
        echo "<p>Başarıyla çıkış yaptınız.</p>";
        echo "<p><a href='index_new.php/admin'>Tekrar Giriş Yap</a> | <a href='index_new.php'>Ana Sayfa</a></p>";
        break;
        
    default:
        http_response_code(404);
        echo "<h1>404 - Sayfa Bulunamadı</h1>";
        echo "<p>Aradığınız sayfa bulunamadı: <code>" . htmlspecialchars($request_uri) . "</code></p>";
        echo "<p><a href='index_new.php'>← Ana Sayfa</a></p>";
        break;
}

// Add some basic styling
echo "<style>
body { 
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
    margin: 40px; 
    line-height: 1.6; 
    background: #f8f9fa;
}
h1 { color: #333; margin-bottom: 20px; }
h2 { color: #495057; }
h3 { color: #6c757d; margin-top: 30px; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
code { background: #e9ecef; padding: 2px 4px; border-radius: 3px; }
table { background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
.container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
</style>";

echo "<script>
// Add some interactivity
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects to buttons
    const buttons = document.querySelectorAll('button, a[style*=\"background\"]');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.opacity = '0.9';
            this.style.transform = 'translateY(-1px)';
        });
        button.addEventListener('mouseleave', function() {
            this.style.opacity = '1';
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>";
?>
