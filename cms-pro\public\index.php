<?php
/**
 * CMS Pro - Professional Content Management System
 * Entry Point
 * 
 * @package CmsPro
 * @version 1.0.0
 */

// Define application constants
define('APP_START_TIME', microtime(true));
define('ROOT_PATH', dirname(__DIR__));
define('APP_PATH', ROOT_PATH . '/src');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('STORAGE_PATH', ROOT_PATH . '/storage');
define('PUBLIC_PATH', __DIR__);

// Load Composer autoloader
require_once ROOT_PATH . '/vendor/autoload.php';

// Load environment variables
if (file_exists(ROOT_PATH . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(ROOT_PATH);
    $dotenv->load();
}

// Bootstrap the application
try {
    // Use the bootstrap file for proper initialization
    $app = require_once ROOT_PATH . '/bootstrap/app.php';

    // Make app globally available
    $GLOBALS['app'] = $app;

    // Get router and load routes
    $router = $app->resolve('router');
    require_once ROOT_PATH . '/routes/web.php';
    require_once ROOT_PATH . '/routes/admin.php';

    // Handle the request
    $request = \Symfony\Component\HttpFoundation\Request::createFromGlobals();

    // Add CSRF token to session if not exists
    $session = $app->resolve('session');
    if (!$session->get('_token')) {
        $session->set('_token', bin2hex(random_bytes(32)));
    }

    // Dispatch request
    $response = $router->dispatch($request);

    if ($response instanceof \Symfony\Component\HttpFoundation\Response) {
        $response->send();
    } else {
        // Handle 404
        http_response_code(404);
        echo '<h1>Page Not Found</h1><p>The requested page could not be found.</p>';
    }

} catch (Exception $e) {
    // Handle fatal errors
    http_response_code(500);

    // Log error if possible
    if (isset($app)) {
        try {
            $activityLogger = $app->resolve('activity_logger');
            $activityLogger->log('application_error', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
        } catch (Exception $logError) {
            // Ignore logging errors
        }
    }

    if (getenv('APP_DEBUG') === 'true') {
        echo '<h1>Application Error</h1>';
        echo '<p><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>';
        echo '<p><strong>Line:</strong> ' . $e->getLine() . '</p>';
        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    } else {
        echo '<h1>Internal Server Error</h1>';
        echo '<p>Something went wrong. Please try again later.</p>';
    }
}
