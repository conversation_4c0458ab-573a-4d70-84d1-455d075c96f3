<?php
/**
 * CMS Pro - Professional Content Management System
 * Entry Point
 * 
 * @package CmsPro
 * @version 1.0.0
 */

// Define application constants
define('APP_START_TIME', microtime(true));
define('ROOT_PATH', dirname(__DIR__));
define('APP_PATH', ROOT_PATH . '/src');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('STORAGE_PATH', ROOT_PATH . '/storage');
define('PUBLIC_PATH', __DIR__);

// Load Composer autoloader
require_once ROOT_PATH . '/vendor/autoload.php';

// Load environment variables
if (file_exists(ROOT_PATH . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(ROOT_PATH);
    $dotenv->load();
}

// Bootstrap the application
try {
    $app = new CmsPro\Core\Application();
    $app->run();
} catch (Exception $e) {
    // Handle fatal errors
    http_response_code(500);
    if (getenv('APP_DEBUG') === 'true') {
        echo '<h1>Application Error</h1>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    } else {
        echo '<h1>Internal Server Error</h1>';
        echo '<p>Something went wrong. Please try again later.</p>';
    }
}
