/**
 * WYSIWYG Editor Integration for CMS Pro
 * TinyMCE 6 Configuration and Management
 */

class WysiwygEditor {
    constructor() {
        this.editors = new Map();
        this.csrfToken = this.getCsrfToken();
        this.defaultConfig = {
            height: 400,
            menubar: false,
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'media', 'table', 'help', 'wordcount', 'emoticons',
                'template', 'codesample', 'hr', 'pagebreak', 'nonbreaking',
                'toc', 'imagetools', 'textpattern', 'noneditable', 'quickbars'
            ],
            toolbar: [
                'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table mergetags | addcomment showcomments | spellcheckdialog a11ycheck typography | align lineheight | checklist numlist bullist indent outdent | emoticons charmap | removeformat',
                'forecolor backcolor | code codesample | fullscreen preview | help'
            ],
            quickbars_selection_toolbar: 'bold italic | quicklink h2 h3 blockquote quickimage quicktable',
            quickbars_insert_toolbar: 'quickimage quicktable',
            contextmenu: 'link image table',
            skin: 'oxide',
            content_css: '/css/editor-content.css',
            body_class: 'editor-content',
            branding: false,
            promotion: false,
            license_key: 'gpl',
            
            // Content filtering - SECURITY ENHANCED
            valid_elements: 'p,br,strong,em,u,s,a[href|title|target],img[src|alt|width|height|class],h1,h2,h3,h4,h5,h6,ul,ol,li,blockquote,table,thead,tbody,tr,td,th,div[class],span[class],code,pre',
            invalid_elements: 'script,object,embed,iframe,form,input,button,textarea,select,option,meta,link,style,base,head,html,body',
            extended_valid_elements: '',
            
            // Image handling - SECURITY ENHANCED
            images_upload_url: '/admin/media/upload',
            images_upload_base_path: '/uploads/',
            images_upload_credentials: true,
            automatic_uploads: true,
            images_upload_handler: this.secureImageUpload.bind(this),
            images_file_types: 'jpg,jpeg,png,gif,webp',
            images_upload_max_size: 5242880, // 5MB limit
            
            // File picker
            file_picker_callback: this.filePickerCallback.bind(this),
            file_picker_types: 'image media file',
            
            // Templates
            templates: '/admin/editor/templates',
            
            // Custom styles
            style_formats: [
                {
                    title: 'Headings',
                    items: [
                        { title: 'Heading 1', format: 'h1' },
                        { title: 'Heading 2', format: 'h2' },
                        { title: 'Heading 3', format: 'h3' },
                        { title: 'Heading 4', format: 'h4' },
                        { title: 'Heading 5', format: 'h5' },
                        { title: 'Heading 6', format: 'h6' }
                    ]
                },
                {
                    title: 'Inline',
                    items: [
                        { title: 'Bold', format: 'bold' },
                        { title: 'Italic', format: 'italic' },
                        { title: 'Underline', format: 'underline' },
                        { title: 'Strikethrough', format: 'strikethrough' },
                        { title: 'Superscript', format: 'superscript' },
                        { title: 'Subscript', format: 'subscript' },
                        { title: 'Code', format: 'code' }
                    ]
                },
                {
                    title: 'Blocks',
                    items: [
                        { title: 'Paragraph', format: 'p' },
                        { title: 'Blockquote', format: 'blockquote' },
                        { title: 'Div', format: 'div' },
                        { title: 'Pre', format: 'pre' }
                    ]
                },
                {
                    title: 'Alignment',
                    items: [
                        { title: 'Left', format: 'alignleft' },
                        { title: 'Center', format: 'aligncenter' },
                        { title: 'Right', format: 'alignright' },
                        { title: 'Justify', format: 'alignjustify' }
                    ]
                }
            ],
            
            // Custom formats
            formats: {
                alignleft: { selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'text-start' },
                aligncenter: { selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'text-center' },
                alignright: { selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'text-end' },
                alignjustify: { selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'text-justify' }
            },
            
            // Setup callback
            setup: this.setupEditor.bind(this),
            
            // Event callbacks
            init_instance_callback: this.onEditorInit.bind(this),
            save_onsavecallback: this.onSave.bind(this)
        };
        
        this.init();
    }

    /**
     * Initialize the editor system
     */
    init() {
        // Load TinyMCE if not already loaded
        if (typeof tinymce === 'undefined') {
            this.loadTinyMCE().then(() => {
                this.initializeEditors();
            });
        } else {
            this.initializeEditors();
        }
    }

    /**
     * Load TinyMCE library
     */
    async loadTinyMCE() {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    /**
     * Initialize all editors on the page
     */
    initializeEditors() {
        // Find all textarea elements with wysiwyg class
        const textareas = document.querySelectorAll('textarea.wysiwyg, textarea[data-editor="wysiwyg"]');
        
        textareas.forEach(textarea => {
            this.createEditor(textarea);
        });
    }

    /**
     * Create editor for specific textarea
     */
    createEditor(textarea, customConfig = {}) {
        const editorId = textarea.id || 'editor_' + Date.now();
        textarea.id = editorId;

        // Merge custom config with default
        const config = {
            ...this.defaultConfig,
            ...customConfig,
            selector: '#' + editorId,
            target: textarea
        };

        // Apply field-specific configuration
        this.applyFieldConfig(textarea, config);

        // Initialize TinyMCE
        tinymce.init(config).then(editors => {
            if (editors.length > 0) {
                this.editors.set(editorId, editors[0]);
                this.bindEvents(editors[0]);
            }
        });

        return editorId;
    }

    /**
     * Apply field-specific configuration
     */
    applyFieldConfig(textarea, config) {
        // Get configuration from data attributes
        const height = textarea.dataset.height;
        const toolbar = textarea.dataset.toolbar;
        const plugins = textarea.dataset.plugins;
        const readonly = textarea.hasAttribute('readonly');

        if (height) {
            config.height = parseInt(height);
        }

        if (toolbar) {
            config.toolbar = toolbar;
        }

        if (plugins) {
            config.plugins = plugins.split(',').map(p => p.trim());
        }

        if (readonly) {
            config.readonly = true;
            config.toolbar = false;
            config.menubar = false;
        }

        // Field type specific configurations
        const fieldType = textarea.dataset.fieldType;
        
        switch (fieldType) {
            case 'simple':
                config.toolbar = 'bold italic underline | link | removeformat';
                config.plugins = ['link', 'autolink'];
                config.menubar = false;
                break;
                
            case 'basic':
                config.toolbar = 'bold italic underline strikethrough | link image | bullist numlist | removeformat';
                config.plugins = ['link', 'image', 'lists', 'autolink'];
                break;
                
            case 'advanced':
                // Use full configuration
                break;
                
            case 'code':
                config.toolbar = 'code codesample | fullscreen';
                config.plugins = ['code', 'codesample', 'fullscreen'];
                config.content_style = 'body { font-family: Monaco, Consolas, "Courier New", monospace; }';
                break;
        }
    }

    /**
     * Setup editor instance
     */
    setupEditor(editor) {
        // Store editor reference
        this.editors.set(editor.id, editor);

        // Add custom buttons
        this.addCustomButtons(editor);

        // Add custom shortcuts
        this.addCustomShortcuts(editor);

        // Setup auto-save
        this.setupAutoSave(editor);

        // Add security event handlers
        this.setupSecurityHandlers(editor);
    }

    /**
     * Setup security event handlers
     */
    setupSecurityHandlers(editor) {
        // Sanitize content before setting
        editor.on('BeforeSetContent', (e) => {
            if (e.content) {
                e.content = this.sanitizeContent(e.content);
            }
        });

        // Sanitize content when getting
        editor.on('GetContent', (e) => {
            if (e.content) {
                e.content = this.sanitizeContent(e.content);
            }
        });

        // Validate pasted content
        editor.on('PastePreProcess', (e) => {
            if (e.content) {
                e.content = this.sanitizeContent(e.content);
            }
        });

        // Monitor for suspicious activity
        editor.on('NodeChange', (e) => {
            this.validateEditorContent(editor);
        });

        // Prevent dangerous operations
        editor.on('BeforeExecCommand', (e) => {
            const dangerousCommands = ['mceInsertRawHTML', 'mceInsertContent'];
            if (dangerousCommands.includes(e.command)) {
                // Allow but sanitize the content
                if (e.value) {
                    e.value = this.sanitizeContent(e.value);
                }
            }
        });
    }

    /**
     * Add custom buttons to editor
     */
    addCustomButtons(editor) {
        // Media Library button
        editor.ui.registry.addButton('medialibrary', {
            text: 'Media',
            icon: 'gallery',
            onAction: () => {
                this.openMediaLibrary(editor);
            }
        });

        // Custom field button
        editor.ui.registry.addButton('customfield', {
            text: 'Field',
            icon: 'template',
            onAction: () => {
                this.insertCustomField(editor);
            }
        });

        // Save button
        editor.ui.registry.addButton('save', {
            text: 'Save',
            icon: 'save',
            onAction: () => {
                this.saveContent(editor);
            }
        });
    }

    /**
     * Add custom keyboard shortcuts
     */
    addCustomShortcuts(editor) {
        // Ctrl+S for save
        editor.addShortcut('ctrl+s', 'Save content', () => {
            this.saveContent(editor);
        });

        // Ctrl+M for media library
        editor.addShortcut('ctrl+m', 'Open media library', () => {
            this.openMediaLibrary(editor);
        });
    }

    /**
     * Setup auto-save functionality
     */
    setupAutoSave(editor) {
        let saveTimeout;
        
        editor.on('input change', () => {
            clearTimeout(saveTimeout);
            saveTimeout = setTimeout(() => {
                this.autoSave(editor);
            }, 30000); // Auto-save every 30 seconds
        });
    }

    /**
     * File picker callback
     */
    filePickerCallback(callback, value, meta) {
        // Open media library modal
        this.openMediaLibraryModal((url, alt) => {
            callback(url, { alt: alt });
        }, meta.filetype);
    }

    /**
     * Open media library modal
     */
    openMediaLibraryModal(callback, filetype = 'image') {
        // Create modal for media library
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Media Library</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <iframe src="/admin/media/library?type=${filetype}" 
                                style="width: 100%; height: 500px; border: none;"></iframe>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        
        // Listen for media selection
        window.addEventListener('message', (event) => {
            if (event.data.type === 'media-selected') {
                callback(event.data.url, event.data.alt);
                bsModal.hide();
                document.body.removeChild(modal);
            }
        });
    }

    /**
     * Open media library
     */
    openMediaLibrary(editor) {
        this.openMediaLibraryModal((url, alt) => {
            editor.insertContent(`<img src="${url}" alt="${alt}" />`);
        });
    }

    /**
     * Insert custom field
     */
    insertCustomField(editor) {
        editor.windowManager.open({
            title: 'Insert Custom Field',
            body: {
                type: 'panel',
                items: [
                    {
                        type: 'input',
                        name: 'fieldname',
                        label: 'Field Name',
                        placeholder: 'Enter field name'
                    }
                ]
            },
            buttons: [
                {
                    type: 'cancel',
                    text: 'Cancel'
                },
                {
                    type: 'submit',
                    text: 'Insert',
                    primary: true
                }
            ],
            onSubmit: (api) => {
                const data = api.getData();
                if (data.fieldname) {
                    editor.insertContent(`{{ field('${data.fieldname}') }}`);
                }
                api.close();
            }
        });
    }

    /**
     * Save content
     */
    saveContent(editor) {
        const content = editor.getContent();
        const textarea = editor.getElement();
        textarea.value = content;
        
        // Trigger save event
        const event = new CustomEvent('editor-save', {
            detail: { editor, content, textarea }
        });
        document.dispatchEvent(event);
        
        // Show save notification
        this.showNotification('Content saved successfully', 'success');
    }

    /**
     * Auto-save content
     */
    autoSave(editor) {
        const content = editor.getContent();
        const textarea = editor.getElement();
        const form = textarea.closest('form');
        
        if (form && form.dataset.autoSave) {
            const formData = new FormData(form);
            formData.set(textarea.name, content);
            
            fetch(form.dataset.autoSave, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.showNotification('Auto-saved', 'info', 2000);
                }
            })
            .catch(error => {
                console.error('Auto-save failed:', error);
            });
        }
    }

    /**
     * Editor initialization callback
     */
    onEditorInit(editor) {
        // Set initial content
        const textarea = editor.getElement();
        if (textarea.value) {
            editor.setContent(textarea.value);
        }
        
        // Mark as initialized
        textarea.dataset.editorInitialized = 'true';
        
        // Trigger custom event
        const event = new CustomEvent('editor-initialized', {
            detail: { editor, textarea }
        });
        document.dispatchEvent(event);
    }

    /**
     * Save callback
     */
    onSave(editor) {
        this.saveContent(editor);
    }

    /**
     * Bind editor events
     */
    bindEvents(editor) {
        // Content change
        editor.on('change', () => {
            const textarea = editor.getElement();
            textarea.value = editor.getContent();
            
            // Trigger change event on textarea
            const event = new Event('change', { bubbles: true });
            textarea.dispatchEvent(event);
        });
        
        // Focus events
        editor.on('focus', () => {
            editor.getContainer().classList.add('editor-focused');
        });
        
        editor.on('blur', () => {
            editor.getContainer().classList.remove('editor-focused');
        });
    }

    /**
     * Get editor instance
     */
    getEditor(id) {
        return this.editors.get(id);
    }

    /**
     * Destroy editor
     */
    destroyEditor(id) {
        const editor = this.editors.get(id);
        if (editor) {
            editor.destroy();
            this.editors.delete(id);
        }
    }

    /**
     * Destroy all editors
     */
    destroyAllEditors() {
        this.editors.forEach((editor, id) => {
            this.destroyEditor(id);
        });
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info', duration = 3000) {
        if (typeof showNotification === 'function') {
            showNotification(message, type, duration);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    /**
     * Reinitialize editors (useful for dynamic content)
     */
    reinitialize() {
        this.destroyAllEditors();
        this.initializeEditors();
    }

    /**
     * Get CSRF token from meta tag or cookie
     */
    getCsrfToken() {
        // Try to get from meta tag first
        const metaToken = document.querySelector('meta[name="csrf-token"]');
        if (metaToken) {
            return metaToken.getAttribute('content');
        }

        // Try to get from cookie
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'cms_csrf_token') {
                return decodeURIComponent(value);
            }
        }

        return null;
    }

    /**
     * Secure image upload handler
     */
    async secureImageUpload(blobInfo, progress) {
        return new Promise((resolve, reject) => {
            const formData = new FormData();
            formData.append('file', blobInfo.blob(), blobInfo.filename());

            // Add CSRF token
            if (this.csrfToken) {
                formData.append('_token', this.csrfToken);
            }

            // Validate file type
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
            if (!allowedTypes.includes(blobInfo.blob().type)) {
                reject('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.');
                return;
            }

            // Validate file size (5MB limit)
            if (blobInfo.blob().size > 5242880) {
                reject('File size too large. Maximum size is 5MB.');
                return;
            }

            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/admin/media/upload');

            // Add security headers
            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
            if (this.csrfToken) {
                xhr.setRequestHeader('X-CSRF-TOKEN', this.csrfToken);
            }

            xhr.upload.onprogress = (e) => {
                if (e.lengthComputable) {
                    progress(e.loaded / e.total * 100);
                }
            };

            xhr.onload = () => {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success && response.url) {
                            // Sanitize the URL
                            const sanitizedUrl = this.sanitizeUrl(response.url);
                            resolve(sanitizedUrl);
                        } else {
                            reject(response.message || 'Upload failed');
                        }
                    } catch (e) {
                        reject('Invalid server response');
                    }
                } else {
                    reject('Upload failed with status: ' + xhr.status);
                }
            };

            xhr.onerror = () => {
                reject('Upload failed due to network error');
            };

            xhr.send(formData);
        });
    }

    /**
     * Sanitize URL to prevent XSS
     */
    sanitizeUrl(url) {
        // Remove any javascript: or data: protocols
        if (url.match(/^(javascript|data|vbscript):/i)) {
            return '';
        }

        // Ensure URL starts with / or http(s)://
        if (!url.match(/^(\/|https?:\/\/)/i)) {
            return '/' + url;
        }

        return url;
    }

    /**
     * Sanitize HTML content
     */
    sanitizeContent(content) {
        // Create a temporary div to parse HTML
        const temp = document.createElement('div');
        temp.innerHTML = content;

        // Remove dangerous elements
        const dangerousElements = temp.querySelectorAll('script, object, embed, iframe, form, input, button, textarea, select, option, meta, link, style, base');
        dangerousElements.forEach(el => el.remove());

        // Remove dangerous attributes
        const allElements = temp.querySelectorAll('*');
        allElements.forEach(el => {
            // Remove event handlers
            const attributes = [...el.attributes];
            attributes.forEach(attr => {
                if (attr.name.startsWith('on') || attr.name === 'style') {
                    el.removeAttribute(attr.name);
                }
            });

            // Sanitize href attributes
            if (el.tagName === 'A' && el.hasAttribute('href')) {
                const href = el.getAttribute('href');
                el.setAttribute('href', this.sanitizeUrl(href));
            }

            // Sanitize src attributes
            if ((el.tagName === 'IMG' || el.tagName === 'SOURCE') && el.hasAttribute('src')) {
                const src = el.getAttribute('src');
                el.setAttribute('src', this.sanitizeUrl(src));
            }
        });

        return temp.innerHTML;
    }

    /**
     * Validate editor content for security issues
     */
    validateEditorContent(editor) {
        const content = editor.getContent();

        // Check for suspicious patterns
        const suspiciousPatterns = [
            /<script[^>]*>/i,
            /javascript:/i,
            /vbscript:/i,
            /data:text\/html/i,
            /on\w+\s*=/i,
            /<iframe[^>]*>/i,
            /<object[^>]*>/i,
            /<embed[^>]*>/i
        ];

        let hasSuspiciousContent = false;
        for (const pattern of suspiciousPatterns) {
            if (pattern.test(content)) {
                hasSuspiciousContent = true;
                break;
            }
        }

        if (hasSuspiciousContent) {
            console.warn('Suspicious content detected in editor, content will be sanitized');
            // Auto-sanitize the content
            const sanitizedContent = this.sanitizeContent(content);
            editor.setContent(sanitizedContent);

            // Show warning to user
            this.showSecurityWarning('Potentially unsafe content was detected and has been cleaned.');
        }
    }

    /**
     * Show security warning to user
     */
    showSecurityWarning(message) {
        // Create a simple notification
        const notification = document.createElement('div');
        notification.className = 'editor-security-warning';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #f8d7da;
            color: #721c24;
            padding: 12px 16px;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            z-index: 10000;
            max-width: 300px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        `;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }

    /**
     * Get editor content safely
     */
    getEditorContent(editorId) {
        const editor = this.editors.get(editorId);
        if (editor) {
            const content = editor.getContent();
            return this.sanitizeContent(content);
        }
        return '';
    }

    /**
     * Set editor content safely
     */
    setEditorContent(editorId, content) {
        const editor = this.editors.get(editorId);
        if (editor) {
            const sanitizedContent = this.sanitizeContent(content);
            editor.setContent(sanitizedContent);
        }
    }

    /**
     * Validate file before upload
     */
    validateFile(file) {
        const errors = [];

        // Check file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            errors.push('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.');
        }

        // Check file size (5MB limit)
        if (file.size > 5242880) {
            errors.push('File size too large. Maximum size is 5MB.');
        }

        // Check filename for suspicious patterns
        const suspiciousPatterns = [
            /\.php$/i,
            /\.asp$/i,
            /\.jsp$/i,
            /\.exe$/i,
            /\.bat$/i,
            /\.cmd$/i,
            /\.scr$/i,
            /\.com$/i,
            /\.pif$/i
        ];

        for (const pattern of suspiciousPatterns) {
            if (pattern.test(file.name)) {
                errors.push('Suspicious file extension detected.');
                break;
            }
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.wysiwygEditor = new WysiwygEditor();
});

// Reinitialize on dynamic content load
document.addEventListener('content-loaded', () => {
    if (window.wysiwygEditor) {
        window.wysiwygEditor.reinitialize();
    }
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WysiwygEditor;
}
