{% extends "layouts/frontend.twig" %}

{% block title %}İletişim - {{ site_settings.site_name }}{% endblock %}

{% block content %}
<!-- Page Header -->
<section class="page-header bg-primary text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="display-4 fw-bold mb-3"><PERSON>letişim</h1>
                <p class="lead">Biz<PERSON>le iletişime geçin, size yardımcı olmaktan mutluluk duyarız</p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Contact Form -->
            <div class="col-lg-8 mb-5">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-5">
                        <h3 class="mb-4">Bize Mesaj Gönderin</h3>
                        <form id="contactForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="firstName" class="form-label">Ad *</label>
                                    <input type="text" class="form-control" id="firstName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="lastName" class="form-label">Soyad *</label>
                                    <input type="text" class="form-control" id="lastName" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">E-posta *</label>
                                    <input type="email" class="form-control" id="email" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Telefon</label>
                                    <input type="tel" class="form-control" id="phone">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="company" class="form-label">Şirket</label>
                                <input type="text" class="form-control" id="company">
                            </div>
                            
                            <div class="mb-3">
                                <label for="service" class="form-label">İlgilendiğiniz Hizmet</label>
                                <select class="form-select" id="service">
                                    <option value="">Seçiniz</option>
                                    <option value="web-design">Web Tasarım</option>
                                    <option value="ecommerce">E-Ticaret</option>
                                    <option value="mobile-app">Mobil Uygulama</option>
                                    <option value="seo">SEO Optimizasyon</option>
                                    <option value="other">Diğer</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="budget" class="form-label">Bütçe Aralığı</label>
                                <select class="form-select" id="budget">
                                    <option value="">Seçiniz</option>
                                    <option value="0-5000">₺0 - ₺5.000</option>
                                    <option value="5000-10000">₺5.000 - ₺10.000</option>
                                    <option value="10000-25000">₺10.000 - ₺25.000</option>
                                    <option value="25000+">₺25.000+</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="message" class="form-label">Mesajınız *</label>
                                <textarea class="form-control" id="message" rows="5" required placeholder="Projeniz hakkında detayları paylaşın..."></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="privacy" required>
                                    <label class="form-check-label" for="privacy">
                                        <a href="#" class="text-decoration-none">Gizlilik Politikası</a>'nı okudum ve kabul ediyorum. *
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="newsletter">
                                    <label class="form-check-label" for="newsletter">
                                        E-posta bültenine abone olmak istiyorum.
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane me-2"></i>Mesaj Gönder
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Contact Info -->
            <div class="col-lg-4">
                <!-- Contact Details -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body p-4">
                        <h5 class="card-title mb-4">İletişim Bilgileri</h5>
                        
                        <div class="contact-item mb-3">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon me-3">
                                    <i class="fas fa-map-marker-alt fa-lg text-primary"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">Adres</h6>
                                    <p class="text-muted mb-0">{{ site_settings.address ?? 'İstanbul, Türkiye' }}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="contact-item mb-3">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon me-3">
                                    <i class="fas fa-phone fa-lg text-primary"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">Telefon</h6>
                                    <p class="text-muted mb-0">
                                        <a href="tel:{{ site_settings.phone }}" class="text-decoration-none">
                                            {{ site_settings.phone ?? '+90 ************' }}
                                        </a>
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="contact-item mb-3">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon me-3">
                                    <i class="fas fa-envelope fa-lg text-primary"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">E-posta</h6>
                                    <p class="text-muted mb-0">
                                        <a href="mailto:{{ site_settings.admin_email }}" class="text-decoration-none">
                                            {{ site_settings.admin_email ?? '<EMAIL>' }}
                                        </a>
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="contact-item">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon me-3">
                                    <i class="fas fa-clock fa-lg text-primary"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">Çalışma Saatleri</h6>
                                    <p class="text-muted mb-1">Pazartesi - Cuma: 09:00 - 18:00</p>
                                    <p class="text-muted mb-0">Cumartesi: 09:00 - 14:00</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Social Media -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body p-4">
                        <h5 class="card-title mb-4">Sosyal Medya</h5>
                        <div class="d-flex gap-3">
                            {% if site_settings.facebook_url %}
                            <a href="{{ site_settings.facebook_url }}" class="btn btn-outline-primary btn-sm" target="_blank">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            {% endif %}
                            {% if site_settings.twitter_url %}
                            <a href="{{ site_settings.twitter_url }}" class="btn btn-outline-info btn-sm" target="_blank">
                                <i class="fab fa-twitter"></i>
                            </a>
                            {% endif %}
                            {% if site_settings.instagram_url %}
                            <a href="{{ site_settings.instagram_url }}" class="btn btn-outline-danger btn-sm" target="_blank">
                                <i class="fab fa-instagram"></i>
                            </a>
                            {% endif %}
                            {% if site_settings.linkedin_url %}
                            <a href="{{ site_settings.linkedin_url }}" class="btn btn-outline-primary btn-sm" target="_blank">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Quick Contact -->
                <div class="card border-0 shadow-sm bg-primary text-white">
                    <div class="card-body p-4 text-center">
                        <h5 class="card-title mb-3">Hızlı İletişim</h5>
                        <p class="mb-4">Acil durumlar için bizi arayabilirsiniz</p>
                        <a href="tel:{{ site_settings.phone }}" class="btn btn-light btn-lg">
                            <i class="fas fa-phone me-2"></i>Hemen Ara
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-5 fw-bold">Ofisimizi Ziyaret Edin</h2>
                <p class="lead text-muted">İstanbul merkezindeki ofisimizde sizleri ağırlamaktan mutluluk duyarız</p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="map-container rounded shadow-sm overflow-hidden" style="height: 400px;">
                    <!-- Google Maps Embed -->
                    <div class="bg-secondary d-flex align-items-center justify-content-center h-100">
                        <div class="text-center text-white">
                            <i class="fas fa-map-marked-alt fa-3x mb-3"></i>
                            <h5>Harita Yükleniyor...</h5>
                            <p>Google Maps entegrasyonu burada olacak</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-5 fw-bold">Sık Sorulan Sorular</h2>
                <p class="lead text-muted">Merak ettiğiniz soruların cevapları</p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                Proje süresi ne kadar?
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Proje süresi, projenin kapsamına göre değişiklik gösterir. Basit bir web sitesi 2-4 hafta, 
                                e-ticaret sitesi 4-8 hafta, mobil uygulama ise 8-12 hafta sürebilir.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                Ödeme koşulları nasıl?
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Genellikle %50 peşin, %50 teslimde ödeme sistemi uyguluyoruz. 
                                Büyük projeler için taksitli ödeme seçenekleri de mevcuttur.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                Destek hizmeti var mı?
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Evet, tüm projelerimiz için 1 yıl ücretsiz teknik destek sunuyoruz. 
                                Bu süre sonrasında uygun fiyatlarla destek hizmeti devam eder.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                Revizyon hakkım var mı?
                            </button>
                        </h2>
                        <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Tasarım aşamasında 3 revizyon hakkınız bulunmaktadır. 
                                Ek revizyonlar için makul ücretler talep edilir.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block scripts %}
<script>
// Contact form submission
document.getElementById('contactForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Get form data
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Gönderiliyor...';
    submitBtn.disabled = true;
    
    // Simulate form submission
    setTimeout(() => {
        // Show success message
        alert('Mesajınız başarıyla gönderildi! En kısa sürede size dönüş yapacağız.');
        
        // Reset form
        this.reset();
        
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        
        console.log('Form data:', data);
    }, 2000);
});

// Form validation
document.querySelectorAll('input[required], textarea[required]').forEach(field => {
    field.addEventListener('blur', function() {
        if (!this.value.trim()) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        }
    });
});

// Email validation
document.getElementById('email').addEventListener('blur', function() {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(this.value)) {
        this.classList.add('is-invalid');
    } else {
        this.classList.remove('is-invalid');
        this.classList.add('is-valid');
    }
});

// Phone formatting
document.getElementById('phone').addEventListener('input', function() {
    let value = this.value.replace(/\D/g, '');
    if (value.length > 0) {
        if (value.length <= 3) {
            value = `(${value}`;
        } else if (value.length <= 6) {
            value = `(${value.slice(0, 3)}) ${value.slice(3)}`;
        } else {
            value = `(${value.slice(0, 3)}) ${value.slice(3, 6)}-${value.slice(6, 10)}`;
        }
    }
    this.value = value;
});

// Smooth animations
const animateElements = document.querySelectorAll('.card, .contact-item');
animateElements.forEach((el, index) => {
    el.style.opacity = '0';
    el.style.transform = 'translateY(20px)';
    el.style.transition = 'all 0.6s ease';
    
    setTimeout(() => {
        el.style.opacity = '1';
        el.style.transform = 'translateY(0)';
    }, index * 100);
});
</script>
{% endblock %}
