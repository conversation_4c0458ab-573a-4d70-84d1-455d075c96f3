<?php
echo "<h1>CMS Pro Test</h1>";
echo "<p>PHP Version: " . PHP_VERSION . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";

// Test database connection
try {
    $pdo = new PDO('mysql:host=localhost;dbname=cms_pro', 'root', '');
    echo "<p style='color: green;'>✅ Database connection: SUCCESS</p>";
    
    // Test if tables exist
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "<p>📊 Database tables: " . count($tables) . " tables found</p>";
    
    // Test admin user
    $admin = $pdo->query("SELECT * FROM users WHERE email = '<EMAIL>'")->fetch();
    if ($admin) {
        echo "<p style='color: green;'>👤 Admin user: EXISTS</p>";
    } else {
        echo "<p style='color: red;'>❌ Admin user: NOT FOUND</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>Go to CMS Pro</a></p>";
echo "<p><a href='admin/'>Go to Admin Panel</a></p>";
?>
