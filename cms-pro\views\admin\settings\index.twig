{% extends "admin/layout.twig" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="settings-container">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ title }}</h1>
            <p class="text-muted mb-0">{{ __('Configure your system settings and preferences') }}</p>
        </div>
        <div class="btn-group">
            {% if auth().can('settings.system') %}
            <button type="button" class="btn btn-outline-warning" id="clear-cache-btn">
                <i class="fas fa-broom me-2"></i>{{ __('Clear Cache') }}
            </button>
            {% endif %}
        </div>
    </div>

    <div class="row">
        <!-- Settings Navigation -->
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>{{ __('Categories') }}
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div class="nav nav-pills flex-column settings-nav">
                        <a class="nav-link {{ current_tab == 'general' ? 'active' : '' }}"
                           href="{{ url('/admin/settings') }}?tab=general">
                            <i class="fas fa-cog me-2"></i>{{ __('General') }}
                        </a>
                        <a class="nav-link {{ current_tab == 'seo' ? 'active' : '' }}"
                           href="{{ url('/admin/settings') }}?tab=seo">
                            <i class="fas fa-search me-2"></i>{{ __('SEO') }}
                        </a>
                        <a class="nav-link {{ current_tab == 'media' ? 'active' : '' }}"
                           href="{{ url('/admin/settings') }}?tab=media">
                            <i class="fas fa-images me-2"></i>{{ __('Media') }}
                        </a>
                        <a class="nav-link {{ current_tab == 'email' ? 'active' : '' }}"
                           href="{{ url('/admin/settings') }}?tab=email">
                            <i class="fas fa-envelope me-2"></i>{{ __('Email') }}
                        </a>
                        <a class="nav-link {{ current_tab == 'security' ? 'active' : '' }}"
                           href="{{ url('/admin/settings') }}?tab=security">
                            <i class="fas fa-shield-alt me-2"></i>{{ __('Security') }}
                        </a>
                        <a class="nav-link {{ current_tab == 'advanced' ? 'active' : '' }}"
                           href="{{ url('/admin/settings') }}?tab=advanced">
                            <i class="fas fa-code me-2"></i>{{ __('Advanced') }}
                        </a>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-0">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle me-2"></i>{{ __('System Info') }}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="system-info">
                        <div class="info-item">
                            <strong>{{ __('PHP Version') }}:</strong>
                            <span>{{ system_info.php_version }}</span>
                        </div>
                        <div class="info-item">
                            <strong>{{ __('Database') }}:</strong>
                            <span>{{ system_info.database_version }}</span>
                        </div>
                        <div class="info-item">
                            <strong>{{ __('Memory Limit') }}:</strong>
                            <span>{{ system_info.memory_limit }}</span>
                        </div>
                        <div class="info-item">
                            <strong>{{ __('Upload Limit') }}:</strong>
                            <span>{{ system_info.upload_max_filesize }}</span>
                        </div>
                        <div class="info-item">
                            <strong>{{ __('Free Space') }}:</strong>
                            <span>{{ system_info.disk_free_space }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Content -->
        <div class="col-md-9">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <!-- General Settings -->
                    {% if current_tab == 'general' %}
                    <form method="POST" action="{{ url('/admin/settings') }}" enctype="multipart/form-data" id="settings-form">
                        {{ csrf_field() | raw }}
                        <input type="hidden" name="category" value="general">

                        <div class="settings-section">
                            <h5 class="section-title">{{ __('Site Information') }}</h5>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="site_name" class="form-label">{{ __('Site Name') }} <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="site_name" name="settings[site_name]"
                                           value="{{ settings.general.site_name }}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="site_url" class="form-label">{{ __('Site URL') }} <span class="text-danger">*</span></label>
                                    <input type="url" class="form-control" id="site_url" name="settings[site_url]"
                                           value="{{ settings.general.site_url }}" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="site_description" class="form-label">{{ __('Site Description') }}</label>
                                <textarea class="form-control" id="site_description" name="settings[site_description]"
                                          rows="3">{{ settings.general.site_description }}</textarea>
                            </div>

                            <div class="mb-3">
                                <label for="admin_email" class="form-label">{{ __('Admin Email') }} <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="admin_email" name="settings[admin_email]"
                                       value="{{ settings.general.admin_email }}" required>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h5 class="section-title">{{ __('Localization') }}</h5>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="timezone" class="form-label">{{ __('Timezone') }}</label>
                                    <select class="form-select" id="timezone" name="settings[timezone]">
                                        <option value="UTC" {{ settings.general.timezone == 'UTC' ? 'selected' : '' }}>UTC</option>
                                        <option value="America/New_York" {{ settings.general.timezone == 'America/New_York' ? 'selected' : '' }}>Eastern Time</option>
                                        <option value="America/Chicago" {{ settings.general.timezone == 'America/Chicago' ? 'selected' : '' }}>Central Time</option>
                                        <option value="America/Denver" {{ settings.general.timezone == 'America/Denver' ? 'selected' : '' }}>Mountain Time</option>
                                        <option value="America/Los_Angeles" {{ settings.general.timezone == 'America/Los_Angeles' ? 'selected' : '' }}>Pacific Time</option>
                                        <option value="Europe/London" {{ settings.general.timezone == 'Europe/London' ? 'selected' : '' }}>London</option>
                                        <option value="Europe/Istanbul" {{ settings.general.timezone == 'Europe/Istanbul' ? 'selected' : '' }}>Istanbul</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="date_format" class="form-label">{{ __('Date Format') }}</label>
                                    <select class="form-select" id="date_format" name="settings[date_format]">
                                        <option value="Y-m-d" {{ settings.general.date_format == 'Y-m-d' ? 'selected' : '' }}>2023-12-25</option>
                                        <option value="m/d/Y" {{ settings.general.date_format == 'm/d/Y' ? 'selected' : '' }}>12/25/2023</option>
                                        <option value="d/m/Y" {{ settings.general.date_format == 'd/m/Y' ? 'selected' : '' }}>25/12/2023</option>
                                        <option value="F j, Y" {{ settings.general.date_format == 'F j, Y' ? 'selected' : '' }}>December 25, 2023</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="time_format" class="form-label">{{ __('Time Format') }}</label>
                                    <select class="form-select" id="time_format" name="settings[time_format]">
                                        <option value="H:i:s" {{ settings.general.time_format == 'H:i:s' ? 'selected' : '' }}>24-hour (23:59:59)</option>
                                        <option value="H:i" {{ settings.general.time_format == 'H:i' ? 'selected' : '' }}>24-hour (23:59)</option>
                                        <option value="g:i:s A" {{ settings.general.time_format == 'g:i:s A' ? 'selected' : '' }}>12-hour (11:59:59 PM)</option>
                                        <option value="g:i A" {{ settings.general.time_format == 'g:i A' ? 'selected' : '' }}>12-hour (11:59 PM)</option>
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="language" class="form-label">{{ __('Language') }}</label>
                                <select class="form-select" id="language" name="settings[language]">
                                    <option value="en" {{ settings.general.language == 'en' ? 'selected' : '' }}>English</option>
                                    <option value="tr" {{ settings.general.language == 'tr' ? 'selected' : '' }}>Türkçe</option>
                                    <option value="es" {{ settings.general.language == 'es' ? 'selected' : '' }}>Español</option>
                                    <option value="fr" {{ settings.general.language == 'fr' ? 'selected' : '' }}>Français</option>
                                </select>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h5 class="section-title">{{ __('Site Assets') }}</h5>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="logo" class="form-label">{{ __('Site Logo') }}</label>
                                    {% if settings.general.site_logo %}
                                    <div class="current-logo mb-2">
                                        <img src="{{ settings.general.site_logo }}" alt="Current Logo" class="img-thumbnail" style="max-height: 60px;">
                                    </div>
                                    {% endif %}
                                    <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                                    <div class="form-text">{{ __('Recommended size: 200x60px') }}</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="favicon" class="form-label">{{ __('Favicon') }}</label>
                                    {% if settings.general.site_favicon %}
                                    <div class="current-favicon mb-2">
                                        <img src="{{ settings.general.site_favicon }}" alt="Current Favicon" class="img-thumbnail" style="max-height: 32px;">
                                    </div>
                                    {% endif %}
                                    <input type="file" class="form-control" id="favicon" name="favicon" accept="image/*">
                                    <div class="form-text">{{ __('Recommended size: 32x32px (.ico or .png)') }}</div>
                                </div>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h5 class="section-title">{{ __('Maintenance') }}</h5>

                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="maintenance_mode"
                                       name="settings[maintenance_mode]" value="1"
                                       {{ settings.general.maintenance_mode ? 'checked' : '' }}>
                                <label class="form-check-label" for="maintenance_mode">
                                    {{ __('Enable Maintenance Mode') }}
                                </label>
                                <div class="form-text">{{ __('When enabled, only administrators can access the site') }}</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ __('Save Settings') }}
                            </button>
                        </div>
                    </form>
                    {% endif %}

                    <!-- SEO Settings -->
                    {% if current_tab == 'seo' %}
                    <form method="POST" action="{{ url('/admin/settings') }}" id="settings-form">
                        {{ csrf_field() | raw }}
                        <input type="hidden" name="category" value="seo">

                        <div class="settings-section">
                            <h5 class="section-title">{{ __('Meta Tags') }}</h5>

                            <div class="mb-3">
                                <label for="seo_title" class="form-label">{{ __('Default SEO Title') }}</label>
                                <input type="text" class="form-control" id="seo_title" name="settings[seo_title]"
                                       value="{{ settings.seo.seo_title }}" maxlength="60">
                                <div class="form-text">{{ __('Recommended length: 50-60 characters') }}</div>
                            </div>

                            <div class="mb-3">
                                <label for="seo_description" class="form-label">{{ __('Default Meta Description') }}</label>
                                <textarea class="form-control" id="seo_description" name="settings[seo_description]"
                                          rows="3" maxlength="160">{{ settings.seo.seo_description }}</textarea>
                                <div class="form-text">{{ __('Recommended length: 150-160 characters') }}</div>
                            </div>

                            <div class="mb-3">
                                <label for="seo_keywords" class="form-label">{{ __('Default Keywords') }}</label>
                                <input type="text" class="form-control" id="seo_keywords" name="settings[seo_keywords]"
                                       value="{{ settings.seo.seo_keywords }}">
                                <div class="form-text">{{ __('Separate keywords with commas') }}</div>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h5 class="section-title">{{ __('Search Engines') }}</h5>

                            <div class="mb-3">
                                <label for="robots_txt" class="form-label">{{ __('Robots.txt Content') }}</label>
                                <textarea class="form-control" id="robots_txt" name="settings[robots_txt]"
                                          rows="5">{{ settings.seo.robots_txt }}</textarea>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h5 class="section-title">{{ __('Analytics & Tracking') }}</h5>

                            <div class="mb-3">
                                <label for="google_analytics" class="form-label">{{ __('Google Analytics Tracking ID') }}</label>
                                <input type="text" class="form-control" id="google_analytics" name="settings[google_analytics]"
                                       value="{{ settings.seo.google_analytics }}" placeholder="G-XXXXXXXXXX">
                            </div>

                            <div class="mb-3">
                                <label for="google_search_console" class="form-label">{{ __('Google Search Console Verification') }}</label>
                                <input type="text" class="form-control" id="google_search_console" name="settings[google_search_console]"
                                       value="{{ settings.seo.google_search_console }}" placeholder="meta tag content">
                            </div>

                            <div class="mb-3">
                                <label for="facebook_pixel" class="form-label">{{ __('Facebook Pixel ID') }}</label>
                                <input type="text" class="form-control" id="facebook_pixel" name="settings[facebook_pixel]"
                                       value="{{ settings.seo.facebook_pixel }}" placeholder="123456789012345">
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ __('Save Settings') }}
                            </button>
                        </div>
                    </form>
                    {% endif %}

                    <!-- Media Settings -->
                    {% if current_tab == 'media' %}
                    <form method="POST" action="{{ url('/admin/settings') }}" id="settings-form">
                        {{ csrf_field() | raw }}
                        <input type="hidden" name="category" value="media">

                        <div class="settings-section">
                            <h5 class="section-title">{{ __('Upload Settings') }}</h5>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="max_upload_size" class="form-label">{{ __('Max Upload Size (MB)') }}</label>
                                    <input type="number" class="form-control" id="max_upload_size" name="settings[max_upload_size]"
                                           value="{{ settings.media.max_upload_size }}" min="1" max="100">
                                    <div class="form-text">{{ __('Current server limit: :limit', {limit: upload_limits.max_file_size ~ 'MB'}) }}</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="image_quality" class="form-label">{{ __('Image Quality (%)') }}</label>
                                    <input type="number" class="form-control" id="image_quality" name="settings[image_quality]"
                                           value="{{ settings.media.image_quality }}" min="1" max="100">
                                    <div class="form-text">{{ __('Higher values = better quality, larger file size') }}</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="allowed_file_types" class="form-label">{{ __('Allowed File Types') }}</label>
                                <input type="text" class="form-control" id="allowed_file_types" name="settings[allowed_file_types]"
                                       value="{{ settings.media.allowed_file_types }}">
                                <div class="form-text">{{ __('Separate extensions with commas (e.g., jpg,png,pdf)') }}</div>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h5 class="section-title">{{ __('Image Processing') }}</h5>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="thumbnail_width" class="form-label">{{ __('Thumbnail Width (px)') }}</label>
                                    <input type="number" class="form-control" id="thumbnail_width" name="settings[thumbnail_width]"
                                           value="{{ settings.media.thumbnail_width }}" min="50" max="1000">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="thumbnail_height" class="form-label">{{ __('Thumbnail Height (px)') }}</label>
                                    <input type="number" class="form-control" id="thumbnail_height" name="settings[thumbnail_height]"
                                           value="{{ settings.media.thumbnail_height }}" min="50" max="1000">
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ __('Save Settings') }}
                            </button>
                        </div>
                    </form>
                    {% endif %}

                    <!-- Email Settings -->
                    {% if current_tab == 'email' %}
                    <form method="POST" action="{{ url('/admin/settings') }}" id="settings-form">
                        {{ csrf_field() | raw }}
                        <input type="hidden" name="category" value="email">

                        <div class="settings-section">
                            <h5 class="section-title">{{ __('SMTP Configuration') }}</h5>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="mail_driver" class="form-label">{{ __('Mail Driver') }}</label>
                                    <select class="form-select" id="mail_driver" name="settings[mail_driver]">
                                        <option value="smtp" {{ settings.email.mail_driver == 'smtp' ? 'selected' : '' }}>SMTP</option>
                                        <option value="sendmail" {{ settings.email.mail_driver == 'sendmail' ? 'selected' : '' }}>Sendmail</option>
                                        <option value="mail" {{ settings.email.mail_driver == 'mail' ? 'selected' : '' }}>PHP Mail</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="mail_host" class="form-label">{{ __('SMTP Host') }}</label>
                                    <input type="text" class="form-control" id="mail_host" name="settings[mail_host]"
                                           value="{{ settings.email.mail_host }}">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="mail_port" class="form-label">{{ __('SMTP Port') }}</label>
                                    <input type="number" class="form-control" id="mail_port" name="settings[mail_port]"
                                           value="{{ settings.email.mail_port }}" min="1" max="65535">
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="mail_username" class="form-label">{{ __('SMTP Username') }}</label>
                                    <input type="text" class="form-control" id="mail_username" name="settings[mail_username]"
                                           value="{{ settings.email.mail_username }}">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="mail_password" class="form-label">{{ __('SMTP Password') }}</label>
                                    <input type="password" class="form-control" id="mail_password" name="settings[mail_password]"
                                           value="{{ settings.email.mail_password }}">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="mail_encryption" class="form-label">{{ __('Encryption') }}</label>
                                <select class="form-select" id="mail_encryption" name="settings[mail_encryption]">
                                    <option value="" {{ settings.email.mail_encryption == '' ? 'selected' : '' }}>None</option>
                                    <option value="tls" {{ settings.email.mail_encryption == 'tls' ? 'selected' : '' }}>TLS</option>
                                    <option value="ssl" {{ settings.email.mail_encryption == 'ssl' ? 'selected' : '' }}>SSL</option>
                                </select>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h5 class="section-title">{{ __('Email Identity') }}</h5>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="mail_from_address" class="form-label">{{ __('From Email') }}</label>
                                    <input type="email" class="form-control" id="mail_from_address" name="settings[mail_from_address]"
                                           value="{{ settings.email.mail_from_address }}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="mail_from_name" class="form-label">{{ __('From Name') }}</label>
                                    <input type="text" class="form-control" id="mail_from_name" name="settings[mail_from_name]"
                                           value="{{ settings.email.mail_from_name }}" required>
                                </div>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h5 class="section-title">{{ __('Test Email') }}</h5>

                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="test_email" class="form-label">{{ __('Test Email Address') }}</label>
                                    <input type="email" class="form-control" id="test_email" placeholder="{{ __('Enter email to test configuration') }}">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-outline-info w-100" id="test-email-btn">
                                        <i class="fas fa-paper-plane me-2"></i>{{ __('Send Test Email') }}
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ __('Save Settings') }}
                            </button>
                        </div>
                    </form>
                    {% endif %}

                    <!-- Security Settings -->
                    {% if current_tab == 'security' %}
                    <form method="POST" action="{{ url('/admin/settings') }}" id="settings-form">
                        {{ csrf_field() | raw }}
                        <input type="hidden" name="category" value="security">

                        <div class="settings-section">
                            <h5 class="section-title">{{ __('Password Policy') }}</h5>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="password_min_length" class="form-label">{{ __('Minimum Password Length') }}</label>
                                    <input type="number" class="form-control" id="password_min_length" name="settings[password_min_length]"
                                           value="{{ settings.security.password_min_length }}" min="6" max="50">
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="password_require_uppercase"
                                               name="settings[password_require_uppercase]" value="1"
                                               {{ settings.security.password_require_uppercase ? 'checked' : '' }}>
                                        <label class="form-check-label" for="password_require_uppercase">
                                            {{ __('Require Uppercase Letters') }}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="password_require_lowercase"
                                               name="settings[password_require_lowercase]" value="1"
                                               {{ settings.security.password_require_lowercase ? 'checked' : '' }}>
                                        <label class="form-check-label" for="password_require_lowercase">
                                            {{ __('Require Lowercase Letters') }}
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="password_require_numbers"
                                               name="settings[password_require_numbers]" value="1"
                                               {{ settings.security.password_require_numbers ? 'checked' : '' }}>
                                        <label class="form-check-label" for="password_require_numbers">
                                            {{ __('Require Numbers') }}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="password_require_symbols"
                                               name="settings[password_require_symbols]" value="1"
                                               {{ settings.security.password_require_symbols ? 'checked' : '' }}>
                                        <label class="form-check-label" for="password_require_symbols">
                                            {{ __('Require Special Characters') }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h5 class="section-title">{{ __('Session & Login') }}</h5>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="session_lifetime" class="form-label">{{ __('Session Lifetime (minutes)') }}</label>
                                    <input type="number" class="form-control" id="session_lifetime" name="settings[session_lifetime]"
                                           value="{{ settings.security.session_lifetime }}" min="1" max="10080">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="max_login_attempts" class="form-label">{{ __('Max Login Attempts') }}</label>
                                    <input type="number" class="form-control" id="max_login_attempts" name="settings[max_login_attempts]"
                                           value="{{ settings.security.max_login_attempts }}" min="1" max="20">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="lockout_duration" class="form-label">{{ __('Lockout Duration (minutes)') }}</label>
                                    <input type="number" class="form-control" id="lockout_duration" name="settings[lockout_duration]"
                                           value="{{ settings.security.lockout_duration }}" min="1" max="1440">
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ __('Save Settings') }}
                            </button>
                        </div>
                    </form>
                    {% endif %}

                    <!-- Advanced Settings -->
                    {% if current_tab == 'advanced' %}
                    <form method="POST" action="{{ url('/admin/settings') }}" id="settings-form">
                        {{ csrf_field() | raw }}
                        <input type="hidden" name="category" value="advanced">

                        <div class="settings-section">
                            <h5 class="section-title">{{ __('System Configuration') }}</h5>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="debug_mode"
                                               name="settings[debug_mode]" value="1"
                                               {{ settings.advanced.debug_mode ? 'checked' : '' }}>
                                        <label class="form-check-label" for="debug_mode">
                                            {{ __('Enable Debug Mode') }}
                                        </label>
                                        <div class="form-text text-warning">{{ __('Only enable in development environment') }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="log_level" class="form-label">{{ __('Log Level') }}</label>
                                    <select class="form-select" id="log_level" name="settings[log_level]">
                                        <option value="emergency" {{ settings.advanced.log_level == 'emergency' ? 'selected' : '' }}>Emergency</option>
                                        <option value="alert" {{ settings.advanced.log_level == 'alert' ? 'selected' : '' }}>Alert</option>
                                        <option value="critical" {{ settings.advanced.log_level == 'critical' ? 'selected' : '' }}>Critical</option>
                                        <option value="error" {{ settings.advanced.log_level == 'error' ? 'selected' : '' }}>Error</option>
                                        <option value="warning" {{ settings.advanced.log_level == 'warning' ? 'selected' : '' }}>Warning</option>
                                        <option value="notice" {{ settings.advanced.log_level == 'notice' ? 'selected' : '' }}>Notice</option>
                                        <option value="info" {{ settings.advanced.log_level == 'info' ? 'selected' : '' }}>Info</option>
                                        <option value="debug" {{ settings.advanced.log_level == 'debug' ? 'selected' : '' }}>Debug</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h5 class="section-title">{{ __('Performance') }}</h5>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="cache_enabled"
                                               name="settings[cache_enabled]" value="1"
                                               {{ settings.advanced.cache_enabled ? 'checked' : '' }}>
                                        <label class="form-check-label" for="cache_enabled">
                                            {{ __('Enable Caching') }}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="cache_lifetime" class="form-label">{{ __('Cache Lifetime (minutes)') }}</label>
                                    <input type="number" class="form-control" id="cache_lifetime" name="settings[cache_lifetime]"
                                           value="{{ settings.advanced.cache_lifetime }}" min="1" max="10080">
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="compression_enabled"
                                           name="settings[compression_enabled]" value="1"
                                           {{ settings.advanced.compression_enabled ? 'checked' : '' }}>
                                    <label class="form-check-label" for="compression_enabled">
                                        {{ __('Enable GZIP Compression') }}
                                    </label>
                                    <div class="form-text">{{ __('Reduces bandwidth usage and improves page load times') }}</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ __('Save Settings') }}
                            </button>
                        </div>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
/**
 * Settings Management
 * Enhanced with form validation and AJAX functionality
 */
class SettingsManager {
    constructor() {
        this.currentTab = '{{ current_tab }}';

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupFormValidation();
        this.setupEmailTest();
        this.setupCacheClear();
    }

    setupEventListeners() {
        // Form submission
        const settingsForm = document.getElementById('settings-form');
        if (settingsForm) {
            settingsForm.addEventListener('submit', (e) => {
                this.handleFormSubmit(e);
            });
        }

        // Tab navigation
        document.addEventListener('click', (e) => {
            if (e.target.matches('.settings-nav .nav-link')) {
                // Let the browser handle navigation, but add loading state
                e.target.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>' + e.target.textContent.trim();
            }
        });
    }

    setupFormValidation() {
        // Real-time validation for specific fields
        const emailFields = document.querySelectorAll('input[type="email"]');
        emailFields.forEach(field => {
            field.addEventListener('blur', () => {
                this.validateEmail(field);
            });
        });

        const urlFields = document.querySelectorAll('input[type="url"]');
        urlFields.forEach(field => {
            field.addEventListener('blur', () => {
                this.validateUrl(field);
            });
        });

        const numberFields = document.querySelectorAll('input[type="number"]');
        numberFields.forEach(field => {
            field.addEventListener('input', () => {
                this.validateNumber(field);
            });
        });
    }

    setupEmailTest() {
        const testEmailBtn = document.getElementById('test-email-btn');
        if (testEmailBtn) {
            testEmailBtn.addEventListener('click', () => {
                this.testEmail();
            });
        }
    }

    setupCacheClear() {
        const clearCacheBtn = document.getElementById('clear-cache-btn');
        if (clearCacheBtn) {
            clearCacheBtn.addEventListener('click', () => {
                this.clearCache();
            });
        }
    }

    async handleFormSubmit(e) {
        e.preventDefault();

        const form = e.target;

        // Validate form
        if (!form.checkValidity()) {
            form.classList.add('was-validated');

            // Focus first invalid field
            const firstInvalid = form.querySelector(':invalid');
            if (firstInvalid) {
                firstInvalid.focus();
                firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }

            return;
        }

        // Show loading state
        const submitButton = form.querySelector('button[type="submit"]');
        const originalText = submitButton.innerHTML;
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';

        try {
            const formData = new FormData(form);

            const response = await fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');

                // Reload page to show updated settings
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                this.showNotification(result.message || 'Save failed', 'error');
            }

        } catch (error) {
            console.error('Form submission error:', error);
            this.showNotification('An error occurred while saving settings', 'error');
        } finally {
            // Restore button state
            submitButton.disabled = false;
            submitButton.innerHTML = originalText;
        }
    }

    async testEmail() {
        const testEmailInput = document.getElementById('test_email');
        const testEmailBtn = document.getElementById('test-email-btn');

        if (!testEmailInput.value) {
            this.showNotification('Please enter an email address to test', 'warning');
            testEmailInput.focus();
            return;
        }

        if (!this.validateEmail(testEmailInput)) {
            this.showNotification('Please enter a valid email address', 'error');
            testEmailInput.focus();
            return;
        }

        // Show loading state
        const originalText = testEmailBtn.innerHTML;
        testEmailBtn.disabled = true;
        testEmailBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';

        try {
            const formData = new FormData();
            formData.append('test_email', testEmailInput.value);
            formData.append('_token', this.getCsrfToken());

            const response = await fetch('/admin/settings/test-email', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');
            } else {
                this.showNotification(result.message, 'error');
            }

        } catch (error) {
            console.error('Email test error:', error);
            this.showNotification('Failed to send test email', 'error');
        } finally {
            // Restore button state
            testEmailBtn.disabled = false;
            testEmailBtn.innerHTML = originalText;
        }
    }

    async clearCache() {
        const clearCacheBtn = document.getElementById('clear-cache-btn');

        if (!confirm('Are you sure you want to clear the cache?')) {
            return;
        }

        // Show loading state
        const originalText = clearCacheBtn.innerHTML;
        clearCacheBtn.disabled = true;
        clearCacheBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Clearing...';

        try {
            const formData = new FormData();
            formData.append('type', 'all');
            formData.append('_token', this.getCsrfToken());

            const response = await fetch('/admin/settings/clear-cache', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');
            } else {
                this.showNotification(result.message, 'error');
            }

        } catch (error) {
            console.error('Cache clear error:', error);
            this.showNotification('Failed to clear cache', 'error');
        } finally {
            // Restore button state
            clearCacheBtn.disabled = false;
            clearCacheBtn.innerHTML = originalText;
        }
    }

    validateEmail(field) {
        const email = field.value.trim();
        const isValid = email === '' || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

        if (isValid) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        } else {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
        }

        return isValid;
    }

    validateUrl(field) {
        const url = field.value.trim();
        const isValid = url === '' || /^https?:\/\/.+/.test(url);

        if (isValid) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        } else {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
        }

        return isValid;
    }

    validateNumber(field) {
        const value = parseInt(field.value);
        const min = parseInt(field.getAttribute('min')) || 0;
        const max = parseInt(field.getAttribute('max')) || Infinity;
        const isValid = !isNaN(value) && value >= min && value <= max;

        if (isValid) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        } else {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
        }

        return isValid;
    }

    getCsrfToken() {
        return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
        notification.innerHTML = `
            ${this.escapeHtml(message)}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    new SettingsManager();
});
</script>

<style>
/* Settings Styles */
.settings-container .settings-nav .nav-link {
    color: #495057;
    border: none;
    border-radius: 0;
    border-left: 3px solid transparent;
    padding: 0.75rem 1rem;
    margin-bottom: 0.25rem;
    transition: all 0.2s ease;
}

.settings-container .settings-nav .nav-link:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
    border-left-color: #e3f2fd;
}

.settings-container .settings-nav .nav-link.active {
    background-color: #e3f2fd;
    color: #0d6efd;
    border-left-color: #0d6efd;
    font-weight: 500;
}

.settings-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.settings-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section-title {
    color: #495057;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.system-info {
    font-size: 0.875rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item strong {
    color: #495057;
}

.info-item span {
    color: #6c757d;
    font-family: 'Courier New', monospace;
}

.current-logo,
.current-favicon {
    padding: 0.5rem;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    display: inline-block;
}

/* Form validation styles */
.was-validated .form-control:valid,
.form-control.is-valid {
    border-color: #28a745;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.38 1.38 3.72-3.72.94.94-4.66 4.66z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:invalid,
.form-control.is-invalid {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 7.4 5.8 6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .settings-container .col-md-3,
    .settings-container .col-md-9 {
        margin-bottom: 1rem;
    }

    .settings-nav {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .settings-nav .nav-link {
        flex: 1;
        text-align: center;
        border-left: none;
        border-bottom: 3px solid transparent;
        margin-bottom: 0;
    }

    .settings-nav .nav-link.active {
        border-left: none;
        border-bottom-color: #0d6efd;
    }

    .system-info {
        display: grid;
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}

/* Dark mode support */
[data-bs-theme="dark"] .settings-nav .nav-link {
    color: #e2e8f0;
}

[data-bs-theme="dark"] .settings-nav .nav-link:hover {
    background-color: #374151;
    color: #60a5fa;
    border-left-color: #1e3a8a;
}

[data-bs-theme="dark"] .settings-nav .nav-link.active {
    background-color: #1e3a8a;
    color: #93c5fd;
    border-left-color: #60a5fa;
}

[data-bs-theme="dark"] .section-title {
    color: #e2e8f0;
    border-bottom-color: #4a5568;
}

[data-bs-theme="dark"] .settings-section {
    border-bottom-color: #4a5568;
}

[data-bs-theme="dark"] .info-item {
    border-bottom-color: #4a5568;
}

[data-bs-theme="dark"] .info-item strong {
    color: #e2e8f0;
}

[data-bs-theme="dark"] .info-item span {
    color: #a0aec0;
}

[data-bs-theme="dark"] .current-logo,
[data-bs-theme="dark"] .current-favicon {
    background-color: #374151;
    border-color: #4a5568;
}
</style>
{% endblock %}