<?php

require_once __DIR__ . '/../vendor/autoload.php';

use CmsPro\Core\Application;
use CmsPro\Core\Database;
use CmsPro\Core\Router;
use CmsPro\Services\AuthService;
use CmsPro\Services\SessionService;
use CmsPro\Services\ConfigService;
use CmsPro\Services\CacheService;
use CmsPro\Services\SettingsService;
use CmsPro\Services\ActivityLogger;
use CmsPro\Services\MediaService;
use CmsPro\Services\SeoService;
use CmsPro\Services\SearchService;
use CmsPro\Services\AnalyticsService;

// Create application instance
$app = new Application(__DIR__ . '/..');

// Load configuration
$config = new ConfigService();
$app->bind('config', $config);

// Initialize database
$database = new Database($config->get('database'));
$app->bind('database', $database);

// Initialize session
$session = new SessionService();
$session->start();
$app->bind('session', $session);

// Initialize cache service
$cache = new CacheService();
$app->bind('cache', $cache);

// Initialize settings service
$settings = new SettingsService();
$app->bind('settings', $settings);

// Initialize activity logger
$activityLogger = new ActivityLogger();
$app->bind('activity_logger', $activityLogger);

// Initialize auth service
$auth = new AuthService($database, $session);
$app->bind('auth', $auth);

// Initialize media service
$media = new MediaService();
$app->bind('media', $media);

// Initialize SEO service
$seo = new SeoService();
$app->bind('seo', $seo);

// Initialize search service
$search = new SearchService();
$app->bind('search', $search);

// Initialize analytics service
$analytics = new AnalyticsService();
$app->bind('analytics', $analytics);

// Initialize router
$router = new Router();
$app->bind('router', $router);

// Set error reporting based on environment
if ($settings->get('debug_mode', false)) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Set timezone
$timezone = $settings->get('timezone', 'UTC');
date_default_timezone_set($timezone);

// Set memory limit for large operations
ini_set('memory_limit', '256M');

// Set upload limits
ini_set('upload_max_filesize', '10M');
ini_set('post_max_size', '10M');

// Register error handler
set_error_handler(function($severity, $message, $file, $line) use ($activityLogger) {
    if (!(error_reporting() & $severity)) {
        return false;
    }
    
    $activityLogger->log('php_error', [
        'severity' => $severity,
        'message' => $message,
        'file' => $file,
        'line' => $line,
        'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5)
    ]);
    
    return false;
});

// Register exception handler
set_exception_handler(function($exception) use ($activityLogger) {
    $activityLogger->log('php_exception', [
        'message' => $exception->getMessage(),
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString()
    ]);
    
    // Show user-friendly error page in production
    if (!$GLOBALS['app']->resolve('settings')->get('debug_mode', false)) {
        http_response_code(500);
        include __DIR__ . '/../views/errors/500.html';
        exit;
    }
});

// Register shutdown handler
register_shutdown_function(function() use ($activityLogger) {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        $activityLogger->log('php_fatal_error', [
            'message' => $error['message'],
            'file' => $error['file'],
            'line' => $error['line']
        ]);
    }
});

return $app;
