<?php

namespace CmsPro\Controllers\Admin;

use CmsPro\Controllers\BaseController;
use CmsPro\Services\SettingsService;
use CmsPro\Services\ValidationService;
use CmsPro\Services\SanitizationService;
use CmsPro\Services\ActivityLogger;
use CmsPro\Services\MediaService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Settings Controller for Admin Panel
 * 
 * @package CmsPro\Controllers\Admin
 */
class SettingsController extends BaseController
{
    private $settingsService;
    private $validationService;
    private $sanitizationService;
    private $activityLogger;
    private $mediaService;

    public function __construct()
    {
        parent::__construct();
        
        $this->settingsService = new SettingsService();
        $this->validationService = new ValidationService();
        $this->sanitizationService = new SanitizationService();
        $this->activityLogger = new ActivityLogger();
        $this->mediaService = new MediaService();
    }

    /**
     * Display settings page
     */
    public function index(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('settings.view')) {
                return $this->forbidden('You do not have permission to view settings.');
            }

            // Get current tab
            $tab = $request->query->get('tab', 'general');
            
            // Validate tab
            $validTabs = ['general', 'seo', 'media', 'email', 'security', 'advanced'];
            if (!in_array($tab, $validTabs)) {
                $tab = 'general';
            }

            // Get all settings grouped by category
            $settings = $this->settingsService->getAllSettings();

            // Get system information
            $systemInfo = $this->getSystemInfo();

            // Log activity
            $this->activityLogger->log('settings_viewed', [
                'user_id' => auth()->id(),
                'tab' => $tab
            ]);

            $data = [
                'title' => __('Settings'),
                'current_tab' => $tab,
                'settings' => $settings,
                'system_info' => $systemInfo,
                'upload_limits' => $this->mediaService->getUploadLimits(),
                'allowed_types' => $this->mediaService->getAllowedTypes()
            ];

            return $this->view('admin/settings/index.twig', $data);

        } catch (\Exception $e) {
            $this->logError('settings_index_error', $e, $request);
            $this->flashError(__('An error occurred while loading settings.'));
            return $this->redirectToRoute('admin.dashboard');
        }
    }

    /**
     * Update settings
     */
    public function update(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('settings.edit')) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'You do not have permission to edit settings.'
                    ], 403);
                }
                
                return $this->forbidden('You do not have permission to edit settings.');
            }

            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Invalid CSRF token'
                    ], 403);
                }
                
                $this->flashError(__('Security token mismatch. Please try again.'));
                return $this->back();
            }

            $category = $request->request->get('category', 'general');
            $settings = $request->request->get('settings', []);

            // Validate settings based on category
            $validationRules = $this->getValidationRules($category);
            $validatedSettings = $this->validationService->validate($settings, $validationRules);

            // Sanitize settings
            $sanitizedSettings = $this->sanitizeSettings($validatedSettings, $category);

            // Handle file uploads (logo, favicon, etc.)
            $uploadedFiles = $this->handleFileUploads($request, $category);
            if ($uploadedFiles) {
                $sanitizedSettings = array_merge($sanitizedSettings, $uploadedFiles);
            }

            // Update settings
            $updateResults = [];
            foreach ($sanitizedSettings as $key => $value) {
                $result = $this->settingsService->updateSetting($key, $value);
                $updateResults[$key] = $result;
            }

            // Check if all updates were successful
            $successCount = count(array_filter($updateResults));
            $totalCount = count($updateResults);

            // Log activity
            $this->activityLogger->log('settings_updated', [
                'user_id' => auth()->id(),
                'category' => $category,
                'settings_updated' => array_keys($sanitizedSettings),
                'success_count' => $successCount,
                'total_count' => $totalCount
            ]);

            $message = sprintf(
                '%d of %d settings updated successfully.',
                $successCount,
                $totalCount
            );

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => $successCount > 0,
                    'message' => $message,
                    'results' => $updateResults
                ]);
            }

            if ($successCount > 0) {
                $this->flashSuccess($message);
            } else {
                $this->flashError(__('No settings were updated.'));
            }

            return $this->redirectToRoute('admin.settings.index', ['tab' => $category]);

        } catch (\Exception $e) {
            $this->logError('settings_update_error', $e, $request);
            
            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => __('An error occurred while updating settings.')
                ], 500);
            }
            
            $this->flashError(__('An error occurred while updating settings.'));
            return $this->back();
        }
    }

    /**
     * Test email configuration
     */
    public function testEmail(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('settings.edit')) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'You do not have permission to test email.'
                ], 403);
            }

            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Invalid CSRF token'
                ], 403);
            }

            $testEmail = $request->request->get('test_email');
            
            if (!$testEmail || !filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Please provide a valid email address.'
                ]);
            }

            // Test email sending
            $result = $this->settingsService->testEmailConfiguration($testEmail);

            // Log activity
            $this->activityLogger->log('email_test', [
                'user_id' => auth()->id(),
                'test_email' => $testEmail,
                'success' => $result['success']
            ]);

            return new JsonResponse($result);

        } catch (\Exception $e) {
            $this->logError('email_test_error', $e, $request);
            
            return new JsonResponse([
                'success' => false,
                'message' => __('An error occurred while testing email.')
            ], 500);
        }
    }

    /**
     * Clear cache
     */
    public function clearCache(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('settings.system')) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'You do not have permission to clear cache.'
                ], 403);
            }

            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Invalid CSRF token'
                ], 403);
            }

            $cacheType = $request->request->get('type', 'all');
            
            // Clear cache
            $result = $this->settingsService->clearCache($cacheType);

            // Log activity
            $this->activityLogger->log('cache_cleared', [
                'user_id' => auth()->id(),
                'cache_type' => $cacheType,
                'success' => $result['success']
            ]);

            return new JsonResponse($result);

        } catch (\Exception $e) {
            $this->logError('cache_clear_error', $e, $request);
            
            return new JsonResponse([
                'success' => false,
                'message' => __('An error occurred while clearing cache.')
            ], 500);
        }
    }

    /**
     * Get validation rules for settings category
     */
    private function getValidationRules($category)
    {
        $rules = [];

        switch ($category) {
            case 'general':
                $rules = [
                    'site_name' => 'required|string|max:100',
                    'site_description' => 'nullable|string|max:500',
                    'site_url' => 'required|url|max:255',
                    'admin_email' => 'required|email|max:255',
                    'timezone' => 'required|string|max:50',
                    'date_format' => 'required|string|max:20',
                    'time_format' => 'required|string|max:20',
                    'language' => 'required|string|max:10',
                    'maintenance_mode' => 'boolean'
                ];
                break;

            case 'seo':
                $rules = [
                    'seo_title' => 'nullable|string|max:60',
                    'seo_description' => 'nullable|string|max:160',
                    'seo_keywords' => 'nullable|string|max:255',
                    'robots_txt' => 'nullable|string',
                    'google_analytics' => 'nullable|string',
                    'google_search_console' => 'nullable|string',
                    'facebook_pixel' => 'nullable|string'
                ];
                break;

            case 'media':
                $rules = [
                    'max_upload_size' => 'required|integer|min:1|max:100',
                    'allowed_file_types' => 'required|string',
                    'image_quality' => 'required|integer|min:1|max:100',
                    'thumbnail_width' => 'required|integer|min:50|max:1000',
                    'thumbnail_height' => 'required|integer|min:50|max:1000'
                ];
                break;

            case 'email':
                $rules = [
                    'mail_driver' => 'required|in:smtp,sendmail,mail',
                    'mail_host' => 'nullable|string|max:255',
                    'mail_port' => 'nullable|integer|min:1|max:65535',
                    'mail_username' => 'nullable|string|max:255',
                    'mail_password' => 'nullable|string|max:255',
                    'mail_encryption' => 'nullable|in:tls,ssl',
                    'mail_from_address' => 'required|email|max:255',
                    'mail_from_name' => 'required|string|max:255'
                ];
                break;

            case 'security':
                $rules = [
                    'password_min_length' => 'required|integer|min:6|max:50',
                    'password_require_uppercase' => 'boolean',
                    'password_require_lowercase' => 'boolean',
                    'password_require_numbers' => 'boolean',
                    'password_require_symbols' => 'boolean',
                    'session_lifetime' => 'required|integer|min:1|max:10080',
                    'max_login_attempts' => 'required|integer|min:1|max:20',
                    'lockout_duration' => 'required|integer|min:1|max:1440'
                ];
                break;

            case 'advanced':
                $rules = [
                    'debug_mode' => 'boolean',
                    'log_level' => 'required|in:emergency,alert,critical,error,warning,notice,info,debug',
                    'cache_enabled' => 'boolean',
                    'cache_lifetime' => 'required|integer|min:1|max:10080',
                    'compression_enabled' => 'boolean'
                ];
                break;
        }

        return $rules;
    }

    /**
     * Sanitize settings based on category
     */
    private function sanitizeSettings($settings, $category)
    {
        $sanitized = [];

        foreach ($settings as $key => $value) {
            switch ($category) {
                case 'general':
                    if (in_array($key, ['site_name', 'site_description', 'admin_email'])) {
                        $sanitized[$key] = $this->sanitizationService->sanitizeInput($value);
                    } elseif ($key === 'site_url') {
                        $sanitized[$key] = filter_var($value, FILTER_SANITIZE_URL);
                    } else {
                        $sanitized[$key] = $value;
                    }
                    break;

                case 'seo':
                    if (in_array($key, ['seo_title', 'seo_description', 'seo_keywords'])) {
                        $sanitized[$key] = $this->sanitizationService->sanitizeInput($value);
                    } elseif ($key === 'robots_txt') {
                        $sanitized[$key] = $this->sanitizationService->sanitizeText($value);
                    } else {
                        $sanitized[$key] = $value;
                    }
                    break;

                default:
                    $sanitized[$key] = $value;
                    break;
            }
        }

        return $sanitized;
    }

    /**
     * Handle file uploads for settings
     */
    private function handleFileUploads(Request $request, $category)
    {
        $uploadedFiles = [];

        if ($category === 'general') {
            // Handle logo upload
            $logoFile = $request->files->get('logo');
            if ($logoFile) {
                $result = $this->mediaService->uploadFile($logoFile, [
                    'folder' => 'system',
                    'user_id' => auth()->id()
                ]);
                
                if ($result['success']) {
                    $uploadedFiles['site_logo'] = $result['media']['file_url'];
                }
            }

            // Handle favicon upload
            $faviconFile = $request->files->get('favicon');
            if ($faviconFile) {
                $result = $this->mediaService->uploadFile($faviconFile, [
                    'folder' => 'system',
                    'user_id' => auth()->id()
                ]);
                
                if ($result['success']) {
                    $uploadedFiles['site_favicon'] = $result['media']['file_url'];
                }
            }
        }

        return $uploadedFiles;
    }

    /**
     * Get system information
     */
    private function getSystemInfo()
    {
        return [
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'database_version' => $this->getDatabaseVersion(),
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'disk_free_space' => $this->formatBytes(disk_free_space('.')),
            'disk_total_space' => $this->formatBytes(disk_total_space('.'))
        ];
    }

    /**
     * Get database version
     */
    private function getDatabaseVersion()
    {
        try {
            $db = app()->getDatabase();
            $result = $db->selectOne("SELECT VERSION() as version");
            return $result['version'] ?? 'Unknown';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Validate CSRF token
     */
    private function validateCsrfToken(Request $request)
    {
        $token = $request->request->get('_token') ?: $request->headers->get('X-CSRF-TOKEN');
        
        if (!$token) {
            return false;
        }

        $sessionToken = session()->get('_token');
        return $sessionToken && hash_equals($sessionToken, $token);
    }

    /**
     * Log errors
     */
    private function logError($event, \Exception $e, Request $request)
    {
        $logData = [
            'event' => $event,
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'user_id' => auth()->id(),
            'ip_address' => $request->getClientIp(),
            'user_agent' => $request->headers->get('User-Agent'),
            'url' => $request->getUri()
        ];

        error_log(json_encode($logData), 3, storage_path('logs/settings.log'));
        
        if ($this->activityLogger) {
            $this->activityLogger->log($event, $logData);
        }
    }
}
