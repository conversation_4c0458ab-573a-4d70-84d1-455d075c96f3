<?php

/**
 * PHPUnit Bootstrap File
 * 
 * Sets up the testing environment
 */

require_once __DIR__ . '/../vendor/autoload.php';

use CmsPro\Core\Application;
use CmsPro\Core\Database;
use CmsPro\Services\ConfigService;

// Set testing environment
putenv('APP_ENV=testing');
$_ENV['APP_ENV'] = 'testing';

// Create test application instance
$app = new Application(__DIR__ . '/..');

// Load test configuration
$config = new ConfigService();
$config->set('database', [
    'driver' => 'sqlite',
    'database' => ':memory:',
    'prefix' => ''
]);

$config->set('cache', [
    'driver' => 'array'
]);

$config->set('session', [
    'driver' => 'array'
]);

$app->bind('config', $config);

// Initialize in-memory database for testing
$database = new Database($config->get('database'));
$app->bind('database', $database);

// Make app globally available for tests
$GLOBALS['app'] = $app;

// Create database tables for testing
createTestTables($database);

/**
 * Create test database tables
 */
function createTestTables($database)
{
    $migrations = [
        __DIR__ . '/../database/migrations/001_create_users_table.sql',
        __DIR__ . '/../database/migrations/002_create_roles_table.sql',
        __DIR__ . '/../database/migrations/003_create_permissions_table.sql',
        __DIR__ . '/../database/migrations/004_create_pages_table.sql',
        __DIR__ . '/../database/migrations/005_create_media_table.sql',
        __DIR__ . '/../database/migrations/006_create_settings_table.sql',
        __DIR__ . '/../database/migrations/007_create_activity_logs_table.sql',
        __DIR__ . '/../database/migrations/008_create_categories_tags_table.sql',
        __DIR__ . '/../database/migrations/009_create_blog_tables.sql'
    ];
    
    foreach ($migrations as $migration) {
        if (file_exists($migration)) {
            $sql = file_get_contents($migration);
            
            // Split by semicolon and execute each statement
            $statements = array_filter(array_map('trim', explode(';', $sql)));
            
            foreach ($statements as $statement) {
                if (!empty($statement)) {
                    try {
                        $database->execute($statement);
                    } catch (Exception $e) {
                        // Ignore errors for testing (table might already exist)
                        continue;
                    }
                }
            }
        }
    }
    
    // Insert test data
    insertTestData($database);
}

/**
 * Insert test data
 */
function insertTestData($database)
{
    // Create test admin user
    $database->insert('users', [
        'first_name' => 'Test',
        'last_name' => 'Admin',
        'email' => '<EMAIL>',
        'password' => password_hash('password', PASSWORD_DEFAULT),
        'role_id' => 1,
        'status' => 'active',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    
    // Create test editor user
    $database->insert('users', [
        'first_name' => 'Test',
        'last_name' => 'Editor',
        'email' => '<EMAIL>',
        'password' => password_hash('password', PASSWORD_DEFAULT),
        'role_id' => 2,
        'status' => 'active',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    
    // Create test roles
    $database->insert('roles', [
        'name' => 'Administrator',
        'slug' => 'administrator',
        'description' => 'Full system access',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    
    $database->insert('roles', [
        'name' => 'Editor',
        'slug' => 'editor',
        'description' => 'Content management access',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    
    // Create test page
    $database->insert('pages', [
        'title' => 'Test Page',
        'slug' => 'test-page',
        'content' => '<p>This is a test page content.</p>',
        'excerpt' => 'Test page excerpt',
        'status' => 'published',
        'author_id' => 1,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    
    // Create test category
    $database->insert('categories', [
        'name' => 'Test Category',
        'slug' => 'test-category',
        'description' => 'Test category description',
        'sort_order' => 1,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    
    // Create test tag
    $database->insert('tags', [
        'name' => 'Test Tag',
        'slug' => 'test-tag',
        'description' => 'Test tag description',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    
    // Create test blog post
    $database->insert('blog_posts', [
        'title' => 'Test Blog Post',
        'slug' => 'test-blog-post',
        'content' => '<p>This is a test blog post content.</p>',
        'excerpt' => 'Test blog post excerpt',
        'status' => 'published',
        'author_id' => 1,
        'category_id' => 1,
        'views' => 10,
        'reading_time' => 2,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    
    // Create test settings
    $settings = [
        ['key' => 'site_name', 'value' => 'Test CMS'],
        ['key' => 'site_description', 'value' => 'Test CMS Description'],
        ['key' => 'site_url', 'value' => 'http://localhost'],
        ['key' => 'admin_email', 'value' => '<EMAIL>'],
        ['key' => 'timezone', 'value' => 'UTC'],
        ['key' => 'language', 'value' => 'en']
    ];
    
    foreach ($settings as $setting) {
        $database->insert('settings', array_merge($setting, [
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]));
    }
}

// Helper function for tests
function createTestUser($data = [])
{
    $defaults = [
        'first_name' => 'Test',
        'last_name' => 'User',
        'email' => 'test' . uniqid() . '@test.com',
        'password' => password_hash('password', PASSWORD_DEFAULT),
        'role_id' => 2,
        'status' => 'active',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    $userData = array_merge($defaults, $data);
    $id = app('database')->insert('users', $userData);
    
    return app('database')->selectOne('SELECT * FROM users WHERE id = ?', [$id]);
}

function createTestPage($data = [])
{
    $defaults = [
        'title' => 'Test Page ' . uniqid(),
        'slug' => 'test-page-' . uniqid(),
        'content' => '<p>Test page content</p>',
        'status' => 'published',
        'author_id' => 1,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    $pageData = array_merge($defaults, $data);
    $id = app('database')->insert('pages', $pageData);
    
    return app('database')->selectOne('SELECT * FROM pages WHERE id = ?', [$id]);
}

function createTestBlogPost($data = [])
{
    $defaults = [
        'title' => 'Test Blog Post ' . uniqid(),
        'slug' => 'test-blog-post-' . uniqid(),
        'content' => '<p>Test blog post content</p>',
        'status' => 'published',
        'author_id' => 1,
        'reading_time' => 2,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    $postData = array_merge($defaults, $data);
    $id = app('database')->insert('blog_posts', $postData);
    
    return app('database')->selectOne('SELECT * FROM blog_posts WHERE id = ?', [$id]);
}
