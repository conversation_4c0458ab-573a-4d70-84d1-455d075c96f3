<?php

namespace CmsPro\Core;

use CmsPro\Core\Router;
use CmsPro\Core\Database;
use CmsPro\Core\Config;
use CmsPro\Core\Session;
use CmsPro\Core\View;
use CmsPro\Core\Auth;
use CmsPro\Middleware\SecurityMiddleware;
use CmsPro\Middleware\AuthMiddleware;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * Main Application Class
 * 
 * @package CmsPro\Core
 */
class Application
{
    private $router;
    private $database;
    private $config;
    private $session;
    private $view;
    private $request;
    private $auth;

    public function __construct()
    {
        $this->initializeCore();
        $this->registerMiddleware();
        $this->registerRoutes();
    }

    /**
     * Initialize core components
     */
    private function initializeCore()
    {
        // Initialize configuration
        $this->config = new Config();
        
        // Initialize request
        $this->request = Request::createFromGlobals();
        
        // Initialize session
        $this->session = new Session();
        
        // Initialize database
        $this->database = new Database($this->config);
        
        // Initialize view engine
        $this->view = new View($this->config);

        // Initialize auth
        $this->auth = new Auth($this->session);

        // Initialize router
        $this->router = new Router();
    }

    /**
     * Register middleware
     */
    private function registerMiddleware()
    {
        $this->router->addMiddleware(new SecurityMiddleware());
        $this->router->addMiddleware(new AuthMiddleware());
    }

    /**
     * Register application routes
     */
    private function registerRoutes()
    {
        // Load route definitions
        require_once ROOT_PATH . '/routes/web.php';
        require_once ROOT_PATH . '/routes/admin.php';
        require_once ROOT_PATH . '/routes/api.php';
    }

    /**
     * Run the application
     */
    public function run()
    {
        try {
            $response = $this->router->dispatch($this->request);
            $response->send();
        } catch (\Exception $e) {
            $this->handleException($e);
        }
    }

    /**
     * Handle application exceptions
     */
    private function handleException(\Exception $e)
    {
        $response = new Response();
        
        if ($this->config->get('app.debug', false)) {
            $response->setContent($this->renderDebugError($e));
        } else {
            $response->setContent($this->renderProductionError($e));
        }
        
        $response->setStatusCode(500);
        $response->send();
    }

    /**
     * Render debug error page
     */
    private function renderDebugError(\Exception $e)
    {
        return sprintf(
            '<h1>Application Error</h1><p>%s</p><pre>%s</pre>',
            htmlspecialchars($e->getMessage()),
            htmlspecialchars($e->getTraceAsString())
        );
    }

    /**
     * Render production error page
     */
    private function renderProductionError(\Exception $e)
    {
        return '<h1>Internal Server Error</h1><p>Something went wrong. Please try again later.</p>';
    }

    /**
     * Get application instance
     */
    public static function getInstance()
    {
        static $instance = null;
        if ($instance === null) {
            $instance = new self();
        }
        return $instance;
    }

    // Getters for core components
    public function getRouter() { return $this->router; }
    public function getDatabase() { return $this->database; }
    public function getConfig() { return $this->config; }
    public function getSession() { return $this->session; }
    public function getView() { return $this->view; }
    public function getRequest() { return $this->request; }
    public function getAuth() { return $this->auth; }
}
