<?php

/**
 * CMS Pro - Professional Interface
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load Composer autoloader
require_once __DIR__ . '/../vendor/autoload.php';

// Initialize Twig
$loader = new \Twig\Loader\FilesystemLoader(__DIR__ . '/../views');
$twig = new \Twig\Environment($loader, [
    'cache' => false, // Disable cache for development
    'debug' => true
]);

// Add debug extension
$twig->addExtension(new \Twig\Extension\DebugExtension());

// Database connection
try {
    $pdo = new PDO('mysql:host=localhost;dbname=cms_pro', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die('Database connection failed: ' . $e->getMessage());
}

// Simple routing
$request_uri = $_SERVER['REQUEST_URI'] ?? '/';
$script_name = $_SERVER['SCRIPT_NAME'] ?? '';

// Remove query string
$request_uri = strtok($request_uri, '?');

// Handle PATH_INFO style routing
$path_info = $_SERVER['PATH_INFO'] ?? '';
if (!empty($path_info)) {
    $request_uri = $path_info;
} else {
    // Extract path after script name
    if (strpos($request_uri, '/professional.php') !== false) {
        $request_uri = substr($request_uri, strpos($request_uri, '/professional.php') + strlen('/professional.php'));
    }
}

// Normalize URI
$request_uri = '/' . trim($request_uri, '/');
if ($request_uri === '/') {
    $request_uri = '/home';
}

// Get site settings
$site_settings = [
    'site_name' => 'TechCorp Solutions',
    'site_description' => 'Modern web çözümleri ve dijital dönüşüm uzmanı',
    'site_url' => 'http://localhost/cms-pro',
    'admin_email' => '<EMAIL>',
    'phone' => '+90 ************',
    'address' => 'İstanbul, Türkiye',
    'facebook_url' => 'https://facebook.com/techcorp',
    'twitter_url' => 'https://twitter.com/techcorp',
    'instagram_url' => 'https://instagram.com/techcorp',
    'linkedin_url' => 'https://linkedin.com/company/techcorp'
];

// Helper functions
function url($path) {
    if ($path === '/') {
        return '/cms-pro/public/professional.php';
    }
    return '/cms-pro/public/professional.php' . $path;
}

function asset($path) {
    return '/cms-pro/public/assets/' . $path;
}

// Add Twig functions
$twig->addFunction(new \Twig\TwigFunction('url', 'url'));
$twig->addFunction(new \Twig\TwigFunction('asset', 'asset'));

// Session management
session_start();

// Check authentication
function isAuthenticated() {
    return isset($_SESSION['user_id']);
}

function requireAuth() {
    if (!isAuthenticated()) {
        header('Location: ' . url('/admin/login'));
        exit;
    }
}

// Get current user
function getCurrentUser($pdo) {
    if (!isAuthenticated()) {
        return null;
    }
    
    $stmt = $pdo->prepare("SELECT u.*, r.name as role_name FROM users u LEFT JOIN roles r ON u.role_id = r.id WHERE u.id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Routing
switch ($request_uri) {
    case '/home':
    case '/':
        // Get latest blog posts
        $stmt = $pdo->query("
            SELECT bp.*, c.name as category_name, u.first_name, u.last_name,
                   CONCAT(u.first_name, ' ', u.last_name) as author_name
            FROM blog_posts bp 
            LEFT JOIN categories c ON bp.category_id = c.id 
            LEFT JOIN users u ON bp.author_id = u.id 
            WHERE bp.status = 'published' 
            ORDER BY bp.created_at DESC 
            LIMIT 6
        ");
        $latest_posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Sample services data
        $services = [
            [
                'title' => 'Web Tasarım',
                'description' => 'Modern ve responsive web tasarımları ile markanızı dijital dünyada öne çıkarın.',
                'image' => asset('images/service-web-design.jpg'),
                'slug' => 'web-tasarim'
            ],
            [
                'title' => 'E-Ticaret',
                'description' => 'Güvenli ve kullanıcı dostu e-ticaret platformları ile online satışlarınızı artırın.',
                'image' => asset('images/service-ecommerce.jpg'),
                'slug' => 'e-ticaret'
            ],
            [
                'title' => 'Mobil Uygulama',
                'description' => 'iOS ve Android platformları için native ve hybrid mobil uygulamalar geliştiriyoruz.',
                'image' => asset('images/service-mobile.jpg'),
                'slug' => 'mobil-uygulama'
            ],
            [
                'title' => 'SEO Optimizasyon',
                'description' => 'Arama motorlarında üst sıralarda yer almanız için kapsamlı SEO hizmetleri.',
                'image' => asset('images/service-seo.jpg'),
                'slug' => 'seo-optimizasyon'
            ]
        ];
        
        // Sample testimonials
        $testimonials = [
            [
                'name' => 'Ahmet Yılmaz',
                'company' => 'ABC Şirketi',
                'content' => 'TechCorp ile çalışmak harika bir deneyimdi. Profesyonel yaklaşımları ve kaliteli işleri ile beklentilerimizi aştılar.',
                'avatar' => asset('images/testimonial-1.jpg')
            ],
            [
                'name' => 'Elif Kaya',
                'company' => 'XYZ Ltd.',
                'content' => 'Web sitemiz için aldığımız hizmet mükemmeldi. Hem tasarım hem de teknik açıdan çok başarılı bir çalışma.',
                'avatar' => asset('images/testimonial-2.jpg')
            ],
            [
                'name' => 'Mehmet Demir',
                'company' => 'DEF A.Ş.',
                'content' => 'E-ticaret projemizde bize destek oldular. Satışlarımız %200 arttı. Kesinlikle tavsiye ederim.',
                'avatar' => asset('images/testimonial-3.jpg')
            ]
        ];
        
        echo $twig->render('frontend/home.twig', [
            'site_settings' => $site_settings,
            'current_page' => 'home',
            'latest_posts' => $latest_posts,
            'services' => $services,
            'testimonials' => $testimonials,
            'stats' => [
                'happy_clients' => 150,
                'completed_projects' => 300,
                'years_experience' => 8,
                'awards' => 25
            ]
        ]);
        break;
        
    case '/admin':
    case '/admin/login':
        if ($_POST['email'] ?? false) {
            // Handle login
            try {
                $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
                $stmt->execute([$_POST['email']]);
                $user = $stmt->fetch();
                
                if ($user && password_verify($_POST['password'], $user['password'])) {
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
                    
                    header('Location: ' . url('/admin/dashboard'));
                    exit;
                } else {
                    $error = 'Email veya şifre hatalı.';
                }
            } catch (Exception $e) {
                $error = 'Giriş sırasında bir hata oluştu.';
            }
        }
        
        echo $twig->render('auth/professional-login.twig', [
            'site_settings' => $site_settings,
            'error' => $error ?? null
        ]);
        break;
        
    case '/admin/dashboard':
        requireAuth();
        
        $user = getCurrentUser($pdo);
        
        // Get statistics
        $stats = [
            'pages' => $pdo->query("SELECT COUNT(*) FROM pages")->fetchColumn(),
            'posts' => $pdo->query("SELECT COUNT(*) FROM blog_posts")->fetchColumn(),
            'users' => $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn(),
            'total_views' => $pdo->query("SELECT SUM(views) FROM pages") + $pdo->query("SELECT SUM(views) FROM blog_posts")->fetchColumn()
        ];
        
        // Recent activities (sample data)
        $recent_activities = [
            [
                'user_name' => 'Admin User',
                'description' => 'Yeni blog yazısı oluşturdu',
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours'))
            ],
            [
                'user_name' => 'Editor User',
                'description' => 'Ana sayfa içeriğini güncelledi',
                'created_at' => date('Y-m-d H:i:s', strtotime('-4 hours'))
            ]
        ];
        
        // Recent pages
        $recent_pages = $pdo->query("SELECT * FROM pages ORDER BY created_at DESC LIMIT 5")->fetchAll();
        
        // Recent posts
        $recent_posts = $pdo->query("
            SELECT bp.*, c.name as category_name 
            FROM blog_posts bp 
            LEFT JOIN categories c ON bp.category_id = c.id 
            ORDER BY bp.created_at DESC 
            LIMIT 5
        ")->fetchAll();
        
        // Chart data (sample)
        $visitor_chart = [
            'labels' => ['Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi', 'Pazar'],
            'data' => [120, 190, 300, 500, 200, 300, 450]
        ];
        
        $content_chart = [
            'labels' => ['Sayfalar', 'Blog', 'Medya', 'Yorumlar'],
            'data' => [45, 30, 15, 10]
        ];
        
        echo $twig->render('admin/dashboard.twig', [
            'site_settings' => $site_settings,
            'current_page' => 'dashboard',
            'user' => $user,
            'stats' => $stats,
            'recent_activities' => $recent_activities,
            'recent_pages' => $recent_pages,
            'recent_posts' => $recent_posts,
            'visitor_chart' => $visitor_chart,
            'content_chart' => $content_chart,
            'system_status' => [
                'disk_usage' => 65,
                'memory_usage' => 45,
                'cache_usage' => 30
            ]
        ]);
        break;
        
    case '/admin/logout':
        session_destroy();
        header('Location: ' . url('/admin/login'));
        exit;
        break;

    case '/admin/pages':
        requireAuth();
        $user = getCurrentUser($pdo);

        // Get all pages
        $pages = $pdo->query("SELECT * FROM pages ORDER BY created_at DESC")->fetchAll();

        echo $twig->render('admin/pages/index.twig', [
            'site_settings' => $site_settings,
            'current_page' => 'pages',
            'user' => $user,
            'pages' => $pages
        ]);
        break;

    case '/admin/posts':
        requireAuth();
        $user = getCurrentUser($pdo);

        // Get all posts with categories
        $posts = $pdo->query("
            SELECT bp.*, c.name as category_name, u.first_name, u.last_name,
                   CONCAT(u.first_name, ' ', u.last_name) as author_name
            FROM blog_posts bp
            LEFT JOIN categories c ON bp.category_id = c.id
            LEFT JOIN users u ON bp.author_id = u.id
            ORDER BY bp.created_at DESC
        ")->fetchAll();

        echo $twig->render('admin/posts/index.twig', [
            'site_settings' => $site_settings,
            'current_page' => 'posts',
            'user' => $user,
            'posts' => $posts
        ]);
        break;

    case '/admin/users':
        requireAuth();
        $user = getCurrentUser($pdo);

        // Get all users with roles
        $users = $pdo->query("
            SELECT u.*, r.name as role_name
            FROM users u
            LEFT JOIN roles r ON u.role_id = r.id
            ORDER BY u.created_at DESC
        ")->fetchAll();

        echo $twig->render('admin/users/index.twig', [
            'site_settings' => $site_settings,
            'current_page' => 'users',
            'user' => $user,
            'users' => $users
        ]);
        break;

    case '/admin/categories':
        requireAuth();
        $user = getCurrentUser($pdo);

        // Get all categories
        $categories = $pdo->query("SELECT * FROM categories ORDER BY name ASC")->fetchAll();

        echo $twig->render('admin/categories/index.twig', [
            'site_settings' => $site_settings,
            'current_page' => 'categories',
            'user' => $user,
            'categories' => $categories
        ]);
        break;

    case '/admin/media':
        requireAuth();
        $user = getCurrentUser($pdo);

        echo $twig->render('admin/media/index.twig', [
            'site_settings' => $site_settings,
            'current_page' => 'media',
            'user' => $user
        ]);
        break;

    case '/admin/comments':
        requireAuth();
        $user = getCurrentUser($pdo);

        echo $twig->render('admin/comments/index.twig', [
            'site_settings' => $site_settings,
            'current_page' => 'comments',
            'user' => $user
        ]);
        break;

    case '/admin/analytics':
        requireAuth();
        $user = getCurrentUser($pdo);

        echo $twig->render('admin/analytics/index.twig', [
            'site_settings' => $site_settings,
            'current_page' => 'analytics',
            'user' => $user
        ]);
        break;

    case '/admin/settings':
        requireAuth();
        $user = getCurrentUser($pdo);

        echo $twig->render('admin/settings/index.twig', [
            'site_settings' => $site_settings,
            'current_page' => 'settings',
            'user' => $user
        ]);
        break;

    case '/blog':
        // Get all published posts
        $posts = $pdo->query("
            SELECT bp.*, c.name as category_name, u.first_name, u.last_name,
                   CONCAT(u.first_name, ' ', u.last_name) as author_name
            FROM blog_posts bp
            LEFT JOIN categories c ON bp.category_id = c.id
            LEFT JOIN users u ON bp.author_id = u.id
            WHERE bp.status = 'published'
            ORDER BY bp.created_at DESC
        ")->fetchAll();

        echo $twig->render('frontend/blog.twig', [
            'site_settings' => $site_settings,
            'current_page' => 'blog',
            'posts' => $posts
        ]);
        break;

    case '/hakkimizda':
        echo $twig->render('frontend/about.twig', [
            'site_settings' => $site_settings,
            'current_page' => 'about'
        ]);
        break;

    case '/hizmetler':
        echo $twig->render('frontend/services.twig', [
            'site_settings' => $site_settings,
            'current_page' => 'services'
        ]);
        break;

    case '/iletisim':
        echo $twig->render('frontend/contact.twig', [
            'site_settings' => $site_settings,
            'current_page' => 'contact'
        ]);
        break;

    default:
        http_response_code(404);
        echo $twig->render('errors/404.twig', [
            'site_settings' => $site_settings
        ]);
        break;
}
?>
