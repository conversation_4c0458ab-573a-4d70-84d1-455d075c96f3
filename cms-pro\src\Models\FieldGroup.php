<?php

namespace CmsPro\Models;

use CmsPro\Core\Database;

/**
 * Field Group Model
 * 
 * @package CmsPro\Models
 */
class FieldGroup
{
    private $db;
    private $data = [];

    public function __construct(Database $db = null)
    {
        $this->db = $db ?: app()->getDatabase();
    }

    /**
     * Find field group by ID
     */
    public static function find($id)
    {
        $instance = new static();
        $fieldGroupData = $instance->db->selectOne(
            "SELECT * FROM field_groups WHERE id = ?",
            [$id]
        );

        if ($fieldGroupData) {
            $instance->data = $fieldGroupData;
            return $instance;
        }

        return null;
    }

    /**
     * Find field group by slug
     */
    public static function findBySlug($slug)
    {
        $instance = new static();
        $fieldGroupData = $instance->db->selectOne(
            "SELECT * FROM field_groups WHERE slug = ?",
            [$slug]
        );

        if ($fieldGroupData) {
            $instance->data = $fieldGroupData;
            return $instance;
        }

        return null;
    }

    /**
     * Get all active field groups
     */
    public static function getAllActive()
    {
        $instance = new static();
        $fieldGroupsData = $instance->db->select(
            "SELECT * FROM field_groups WHERE is_active = 1 ORDER BY sort_order, name"
        );
        
        $fieldGroups = [];
        foreach ($fieldGroupsData as $fieldGroupData) {
            $fieldGroup = new static();
            $fieldGroup->data = $fieldGroupData;
            $fieldGroups[] = $fieldGroup;
        }
        
        return $fieldGroups;
    }

    /**
     * Get field groups by location
     */
    public static function getByLocation($location)
    {
        $instance = new static();
        $fieldGroupsData = $instance->db->select(
            "SELECT * FROM field_groups WHERE location = ? AND is_active = 1 ORDER BY sort_order, name",
            [$location]
        );
        
        $fieldGroups = [];
        foreach ($fieldGroupsData as $fieldGroupData) {
            $fieldGroup = new static();
            $fieldGroup->data = $fieldGroupData;
            $fieldGroups[] = $fieldGroup;
        }
        
        return $fieldGroups;
    }

    /**
     * Get all field groups
     */
    public static function getAll()
    {
        $instance = new static();
        $fieldGroupsData = $instance->db->select(
            "SELECT * FROM field_groups ORDER BY sort_order, name"
        );
        
        $fieldGroups = [];
        foreach ($fieldGroupsData as $fieldGroupData) {
            $fieldGroup = new static();
            $fieldGroup->data = $fieldGroupData;
            $fieldGroups[] = $fieldGroup;
        }
        
        return $fieldGroups;
    }

    /**
     * Create new field group
     */
    public static function create($data)
    {
        $instance = new static();
        
        // Generate slug if not provided
        if (!isset($data['slug'])) {
            $data['slug'] = strtolower(str_replace(' ', '_', $data['name']));
        }

        // Encode JSON fields
        if (isset($data['rules']) && is_array($data['rules'])) {
            $data['rules'] = json_encode($data['rules']);
        }

        if (isset($data['settings']) && is_array($data['settings'])) {
            $data['settings'] = json_encode($data['settings']);
        }

        $instance->db->insert('field_groups', $data);
        $fieldGroupId = $instance->db->lastInsertId();

        return static::find($fieldGroupId);
    }

    /**
     * Update field group
     */
    public function update($data)
    {
        // Encode JSON fields
        if (isset($data['rules']) && is_array($data['rules'])) {
            $data['rules'] = json_encode($data['rules']);
        }

        if (isset($data['settings']) && is_array($data['settings'])) {
            $data['settings'] = json_encode($data['settings']);
        }

        $this->db->update('field_groups', $data, 'id = ?', [$this->getId()]);
        
        // Refresh data
        $this->data = array_merge($this->data, $data);
        
        return $this;
    }

    /**
     * Delete field group
     */
    public function delete()
    {
        // Delete all fields in this group first
        $this->db->delete('fields', 'field_group_id = ?', [$this->getId()]);
        
        // Delete the field group
        $this->db->delete('field_groups', 'id = ?', [$this->getId()]);
        
        return true;
    }

    /**
     * Get fields in this group
     */
    public function getFields()
    {
        $fieldsData = $this->db->select(
            "SELECT f.*, ft.name as field_type_name, ft.slug as field_type_slug, ft.component 
             FROM fields f 
             INNER JOIN field_types ft ON f.field_type_id = ft.id 
             WHERE f.field_group_id = ? AND f.is_active = 1 
             ORDER BY f.sort_order, f.name",
            [$this->getId()]
        );

        $fields = [];
        foreach ($fieldsData as $fieldData) {
            // Field model will be loaded when needed
            $fields[] = $fieldData;
        }

        return $fields;
    }

    /**
     * Get field count
     */
    public function getFieldCount()
    {
        $result = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM fields WHERE field_group_id = ? AND is_active = 1",
            [$this->getId()]
        );

        return $result['count'] ?? 0;
    }

    /**
     * Get rules as array
     */
    public function getRules()
    {
        $rules = $this->data['rules'] ?? '{}';
        return json_decode($rules, true) ?: [];
    }

    /**
     * Get settings as array
     */
    public function getSettings()
    {
        $settings = $this->data['settings'] ?? '{}';
        return json_decode($settings, true) ?: [];
    }

    /**
     * Check if field group should be displayed for given context
     */
    public function shouldDisplay($context = [])
    {
        $rules = $this->getRules();
        
        if (empty($rules)) {
            return true;
        }

        // Check content type rules
        if (isset($rules['content_types']) && !empty($rules['content_types'])) {
            $contentType = $context['content_type'] ?? null;
            if ($contentType && !in_array($contentType, $rules['content_types'])) {
                return false;
            }
        }

        // Check template rules
        if (isset($rules['templates']) && !empty($rules['templates'])) {
            $template = $context['template'] ?? null;
            if ($template && !in_array($template, $rules['templates'])) {
                return false;
            }
        }

        // Check user role rules
        if (isset($rules['user_roles']) && !empty($rules['user_roles'])) {
            $userRole = $context['user_role'] ?? auth()->user()->getRole()->getSlug();
            if ($userRole && !in_array($userRole, $rules['user_roles'])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get available locations
     */
    public static function getAvailableLocations()
    {
        return [
            'content' => 'Content',
            'page' => 'Page',
            'post' => 'Post',
            'user' => 'User',
            'category' => 'Category',
            'tag' => 'Tag',
            'media' => 'Media',
            'settings' => 'Settings'
        ];
    }

    // Getters
    public function getId() { return $this->data['id'] ?? null; }
    public function getName() { return $this->data['name'] ?? null; }
    public function getSlug() { return $this->data['slug'] ?? null; }
    public function getDescription() { return $this->data['description'] ?? null; }
    public function getLocation() { return $this->data['location'] ?? null; }
    public function isActive() { return (bool) ($this->data['is_active'] ?? false); }
    public function getSortOrder() { return $this->data['sort_order'] ?? 0; }
    public function getCreatedAt() { return $this->data['created_at'] ?? null; }
    public function getUpdatedAt() { return $this->data['updated_at'] ?? null; }

    /**
     * Convert to array
     */
    public function toArray()
    {
        $data = $this->data;
        $data['rules'] = $this->getRules();
        $data['settings'] = $this->getSettings();
        $data['field_count'] = $this->getFieldCount();
        return $data;
    }

    /**
     * Magic getter
     */
    public function __get($key)
    {
        return $this->data[$key] ?? null;
    }

    /**
     * Magic isset
     */
    public function __isset($key)
    {
        return isset($this->data[$key]);
    }
}
