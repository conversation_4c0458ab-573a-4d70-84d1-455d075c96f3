<?php

namespace CmsPro\Controllers;

use CmsPro\Services\SearchService;
use CmsPro\Services\SettingsService;
use CmsPro\Services\SeoService;
use CmsPro\Services\ActivityLogger;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Search Controller
 * 
 * Handles search functionality for frontend
 */
class SearchController extends BaseController
{
    private $searchService;
    private $settingsService;
    private $seoService;
    private $activityLogger;

    public function __construct()
    {
        parent::__construct();
        
        $this->searchService = new SearchService();
        $this->settingsService = new SettingsService();
        $this->seoService = new SeoService();
        $this->activityLogger = new ActivityLogger();
    }

    /**
     * Display search results
     */
    public function index(Request $request)
    {
        $this->request = $request;

        try {
            $query = trim($request->query->get('q', ''));
            $type = $request->query->get('type', 'all');
            $page = max(1, (int) $request->query->get('page', 1));

            // Prepare search options
            $options = [
                'page' => $page,
                'limit' => 10,
                'highlight' => true,
                'fuzzy' => true
            ];

            // Filter by content type
            if ($type !== 'all') {
                $typeMap = [
                    'pages' => ['pages'],
                    'blog' => ['blog_posts'],
                    'categories' => ['categories'],
                    'tags' => ['tags']
                ];
                
                if (isset($typeMap[$type])) {
                    $options['types'] = $typeMap[$type];
                }
            }

            // Perform search
            $searchResults = $this->searchService->search($query, $options);

            // Get site settings
            $siteSettings = $this->getSiteSettings();

            // Generate SEO data
            $seoData = $this->seoService->generateSeoData([
                'title' => $query ? "Search results for '{$query}'" : 'Search',
                'description' => $query ? "Search results for '{$query}' on {$siteSettings['site_name']}" : 'Search our content',
                'url' => $siteSettings['site_url'] . '/search' . ($query ? '?q=' . urlencode($query) : ''),
                'type' => 'website'
            ]);

            // Log search activity
            if ($query) {
                $this->activityLogger->log('search_performed', [
                    'query' => $query,
                    'type' => $type,
                    'results_count' => $searchResults['total'],
                    'ip_address' => $request->getClientIp(),
                    'user_agent' => $request->headers->get('User-Agent')
                ]);
            }

            $data = [
                'query' => $query,
                'type' => $type,
                'results' => $searchResults['results'],
                'total' => $searchResults['total'],
                'pagination' => [
                    'current_page' => $searchResults['page'],
                    'pages' => $searchResults['pages'],
                    'limit' => $searchResults['limit']
                ],
                'suggestions' => $searchResults['suggestions'],
                'site_settings' => $siteSettings,
                'seo' => $seoData,
                'search_stats' => $this->searchService->getSearchStatistics()
            ];

            return $this->view('frontend/search.twig', $data);

        } catch (\Exception $e) {
            $this->logError('search_error', $e, $request);
            
            // Return basic search page on error
            $siteSettings = $this->getSiteSettings();
            $seoData = $this->seoService->generateSeoData([
                'title' => 'Search',
                'description' => 'Search our content',
                'url' => $siteSettings['site_url'] . '/search',
                'type' => 'website'
            ]);

            $data = [
                'query' => $query ?? '',
                'type' => $type ?? 'all',
                'results' => [],
                'total' => 0,
                'pagination' => ['current_page' => 1, 'pages' => 0, 'limit' => 10],
                'suggestions' => [],
                'site_settings' => $siteSettings,
                'seo' => $seoData,
                'search_stats' => ['total_pages' => 0, 'total_posts' => 0, 'total_categories' => 0, 'total_tags' => 0],
                'error' => 'An error occurred while searching. Please try again.'
            ];

            return $this->view('frontend/search.twig', $data);
        }
    }

    /**
     * AJAX search endpoint
     */
    public function ajax(Request $request)
    {
        $this->request = $request;

        try {
            $query = trim($request->query->get('q', ''));
            $type = $request->query->get('type', 'all');
            $limit = min(20, max(1, (int) $request->query->get('limit', 5)));

            if (strlen($query) < 2) {
                return new JsonResponse([
                    'success' => true,
                    'results' => [],
                    'total' => 0,
                    'query' => $query
                ]);
            }

            // Prepare search options
            $options = [
                'page' => 1,
                'limit' => $limit,
                'highlight' => false,
                'fuzzy' => true
            ];

            // Filter by content type
            if ($type !== 'all') {
                $typeMap = [
                    'pages' => ['pages'],
                    'blog' => ['blog_posts'],
                    'categories' => ['categories'],
                    'tags' => ['tags']
                ];
                
                if (isset($typeMap[$type])) {
                    $options['types'] = $typeMap[$type];
                }
            }

            // Perform search
            $searchResults = $this->searchService->search($query, $options);

            // Format results for AJAX response
            $formattedResults = [];
            foreach ($searchResults['results'] as $result) {
                $formattedResults[] = [
                    'id' => $result['id'],
                    'type' => $result['type'],
                    'title' => $result['title'],
                    'excerpt' => $result['excerpt'],
                    'url' => $result['url'],
                    'date' => $result['date']
                ];
            }

            // Log search activity
            $this->activityLogger->log('ajax_search_performed', [
                'query' => $query,
                'type' => $type,
                'results_count' => $searchResults['total'],
                'ip_address' => $request->getClientIp()
            ]);

            return new JsonResponse([
                'success' => true,
                'results' => $formattedResults,
                'total' => $searchResults['total'],
                'query' => $query,
                'suggestions' => $searchResults['suggestions']
            ]);

        } catch (\Exception $e) {
            $this->logError('ajax_search_error', $e, $request);
            
            return new JsonResponse([
                'success' => false,
                'message' => 'An error occurred while searching.',
                'results' => [],
                'total' => 0
            ], 500);
        }
    }

    /**
     * Search suggestions endpoint
     */
    public function suggestions(Request $request)
    {
        $this->request = $request;

        try {
            $query = trim($request->query->get('q', ''));
            
            if (strlen($query) < 2) {
                return new JsonResponse([
                    'success' => true,
                    'suggestions' => []
                ]);
            }

            // Get quick suggestions (just titles)
            $options = [
                'page' => 1,
                'limit' => 8,
                'highlight' => false,
                'fuzzy' => true
            ];

            $searchResults = $this->searchService->search($query, $options);
            
            $suggestions = [];
            foreach ($searchResults['results'] as $result) {
                $suggestions[] = [
                    'title' => $result['title'],
                    'type' => $result['type'],
                    'url' => $result['url']
                ];
            }

            return new JsonResponse([
                'success' => true,
                'suggestions' => $suggestions
            ]);

        } catch (\Exception $e) {
            $this->logError('search_suggestions_error', $e, $request);
            
            return new JsonResponse([
                'success' => false,
                'suggestions' => []
            ], 500);
        }
    }

    /**
     * Get site settings for frontend
     */
    private function getSiteSettings()
    {
        return [
            'site_name' => $this->settingsService->get('site_name', 'CMS Pro'),
            'site_description' => $this->settingsService->get('site_description', ''),
            'site_url' => $this->settingsService->get('site_url', 'http://localhost'),
            'site_logo' => $this->settingsService->get('site_logo', ''),
            'language' => $this->settingsService->get('language', 'en')
        ];
    }

    /**
     * Log errors
     */
    private function logError($event, \Exception $e, Request $request)
    {
        $logData = [
            'event' => $event,
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'ip_address' => $request->getClientIp(),
            'user_agent' => $request->headers->get('User-Agent'),
            'url' => $request->getUri()
        ];

        error_log(json_encode($logData), 3, storage_path('logs/search.log'));
        
        if ($this->activityLogger) {
            $this->activityLogger->log($event, $logData);
        }
    }
}
