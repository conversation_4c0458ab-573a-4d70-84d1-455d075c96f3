<?php

namespace CmsPro\Services;

use CmsPro\Core\Database;

/**
 * Validation Rules Manager
 * 
 * @package CmsPro\Services
 */
class ValidationRulesManager
{
    private $db;
    private $customRules = [];
    private $ruleTemplates = [];

    public function __construct(Database $db = null)
    {
        $this->db = $db ?: app()->getDatabase();
        $this->initializeDefaultRules();
    }

    /**
     * Get validation rules for field type
     */
    public function getRulesForFieldType($fieldType)
    {
        $rules = [];

        switch ($fieldType) {
            case 'text':
                $rules = [
                    'min_length' => ['type' => 'number', 'label' => 'Minimum Length', 'default' => 0],
                    'max_length' => ['type' => 'number', 'label' => 'Maximum Length', 'default' => 255],
                    'pattern' => ['type' => 'text', 'label' => 'Regex Pattern', 'placeholder' => '/^[a-zA-Z]+$/'],
                    'pattern_message' => ['type' => 'text', 'label' => 'Pattern Error Message'],
                    'unique' => ['type' => 'boolean', 'label' => 'Must be unique'],
                    'trim' => ['type' => 'boolean', 'label' => 'Trim whitespace', 'default' => true]
                ];
                break;

            case 'textarea':
                $rules = [
                    'min_length' => ['type' => 'number', 'label' => 'Minimum Length', 'default' => 0],
                    'max_length' => ['type' => 'number', 'label' => 'Maximum Length', 'default' => 5000],
                    'max_words' => ['type' => 'number', 'label' => 'Maximum Words'],
                    'pattern' => ['type' => 'text', 'label' => 'Regex Pattern'],
                    'pattern_message' => ['type' => 'text', 'label' => 'Pattern Error Message'],
                    'trim' => ['type' => 'boolean', 'label' => 'Trim whitespace', 'default' => true]
                ];
                break;

            case 'wysiwyg':
                $rules = [
                    'min_length' => ['type' => 'number', 'label' => 'Minimum Length', 'default' => 0],
                    'max_length' => ['type' => 'number', 'label' => 'Maximum Length', 'default' => 50000],
                    'max_words' => ['type' => 'number', 'label' => 'Maximum Words'],
                    'allowed_tags' => ['type' => 'multiselect', 'label' => 'Allowed HTML Tags', 'options' => $this->getHtmlTags()],
                    'strip_scripts' => ['type' => 'boolean', 'label' => 'Strip JavaScript', 'default' => true],
                    'strip_styles' => ['type' => 'boolean', 'label' => 'Strip Inline Styles', 'default' => false]
                ];
                break;

            case 'email':
                $rules = [
                    'unique' => ['type' => 'boolean', 'label' => 'Must be unique'],
                    'domain_whitelist' => ['type' => 'textarea', 'label' => 'Allowed Domains (one per line)'],
                    'domain_blacklist' => ['type' => 'textarea', 'label' => 'Blocked Domains (one per line)']
                ];
                break;

            case 'url':
                $rules = [
                    'protocols' => ['type' => 'multiselect', 'label' => 'Allowed Protocols', 'options' => [
                        'http' => 'HTTP',
                        'https' => 'HTTPS',
                        'ftp' => 'FTP',
                        'mailto' => 'Mailto'
                    ], 'default' => ['http', 'https']],
                    'domain_whitelist' => ['type' => 'textarea', 'label' => 'Allowed Domains (one per line)'],
                    'domain_blacklist' => ['type' => 'textarea', 'label' => 'Blocked Domains (one per line)']
                ];
                break;

            case 'number':
            case 'range':
                $rules = [
                    'min' => ['type' => 'number', 'label' => 'Minimum Value'],
                    'max' => ['type' => 'number', 'label' => 'Maximum Value'],
                    'step' => ['type' => 'number', 'label' => 'Step Value', 'default' => 1],
                    'decimal_places' => ['type' => 'number', 'label' => 'Decimal Places', 'default' => 0],
                    'unique' => ['type' => 'boolean', 'label' => 'Must be unique']
                ];
                break;

            case 'date':
                $rules = [
                    'min_date' => ['type' => 'date', 'label' => 'Minimum Date'],
                    'max_date' => ['type' => 'date', 'label' => 'Maximum Date'],
                    'exclude_weekends' => ['type' => 'boolean', 'label' => 'Exclude Weekends'],
                    'exclude_holidays' => ['type' => 'boolean', 'label' => 'Exclude Holidays'],
                    'future_only' => ['type' => 'boolean', 'label' => 'Future Dates Only'],
                    'past_only' => ['type' => 'boolean', 'label' => 'Past Dates Only']
                ];
                break;

            case 'datetime':
                $rules = [
                    'min_datetime' => ['type' => 'datetime', 'label' => 'Minimum DateTime'],
                    'max_datetime' => ['type' => 'datetime', 'label' => 'Maximum DateTime'],
                    'timezone' => ['type' => 'select', 'label' => 'Timezone', 'options' => $this->getTimezones()],
                    'business_hours_only' => ['type' => 'boolean', 'label' => 'Business Hours Only'],
                    'exclude_weekends' => ['type' => 'boolean', 'label' => 'Exclude Weekends']
                ];
                break;

            case 'phone':
                $rules = [
                    'pattern' => ['type' => 'text', 'label' => 'Phone Pattern', 'default' => '/^[\+]?[0-9\s\-\(\)]{10,20}$/'],
                    'country_code' => ['type' => 'select', 'label' => 'Country Code', 'options' => $this->getCountryCodes()],
                    'international' => ['type' => 'boolean', 'label' => 'Allow International'],
                    'unique' => ['type' => 'boolean', 'label' => 'Must be unique']
                ];
                break;

            case 'image':
                $rules = [
                    'max_file_size' => ['type' => 'number', 'label' => 'Max File Size (MB)', 'default' => 5],
                    'allowed_types' => ['type' => 'multiselect', 'label' => 'Allowed Types', 'options' => [
                        'jpg' => 'JPEG',
                        'jpeg' => 'JPEG',
                        'png' => 'PNG',
                        'gif' => 'GIF',
                        'webp' => 'WebP',
                        'svg' => 'SVG'
                    ], 'default' => ['jpg', 'jpeg', 'png']],
                    'min_width' => ['type' => 'number', 'label' => 'Minimum Width (px)'],
                    'max_width' => ['type' => 'number', 'label' => 'Maximum Width (px)'],
                    'min_height' => ['type' => 'number', 'label' => 'Minimum Height (px)'],
                    'max_height' => ['type' => 'number', 'label' => 'Maximum Height (px)'],
                    'aspect_ratio' => ['type' => 'text', 'label' => 'Aspect Ratio (e.g., 16:9)']
                ];
                break;

            case 'file':
                $rules = [
                    'max_file_size' => ['type' => 'number', 'label' => 'Max File Size (MB)', 'default' => 10],
                    'allowed_types' => ['type' => 'multiselect', 'label' => 'Allowed Types', 'options' => [
                        'pdf' => 'PDF',
                        'doc' => 'Word Document',
                        'docx' => 'Word Document',
                        'xls' => 'Excel',
                        'xlsx' => 'Excel',
                        'txt' => 'Text File',
                        'zip' => 'ZIP Archive'
                    ]],
                    'scan_viruses' => ['type' => 'boolean', 'label' => 'Scan for Viruses', 'default' => true]
                ];
                break;

            case 'gallery':
                $rules = [
                    'min_items' => ['type' => 'number', 'label' => 'Minimum Items', 'default' => 0],
                    'max_items' => ['type' => 'number', 'label' => 'Maximum Items', 'default' => 20],
                    'max_file_size' => ['type' => 'number', 'label' => 'Max File Size per Image (MB)', 'default' => 5],
                    'allowed_types' => ['type' => 'multiselect', 'label' => 'Allowed Types', 'options' => [
                        'jpg' => 'JPEG',
                        'jpeg' => 'JPEG',
                        'png' => 'PNG',
                        'gif' => 'GIF',
                        'webp' => 'WebP'
                    ], 'default' => ['jpg', 'jpeg', 'png']]
                ];
                break;

            case 'repeater':
                $rules = [
                    'min_items' => ['type' => 'number', 'label' => 'Minimum Items', 'default' => 0],
                    'max_items' => ['type' => 'number', 'label' => 'Maximum Items', 'default' => 10],
                    'validate_sub_fields' => ['type' => 'boolean', 'label' => 'Validate Sub-fields', 'default' => true]
                ];
                break;

            case 'flexible':
                $rules = [
                    'min_items' => ['type' => 'number', 'label' => 'Minimum Items', 'default' => 0],
                    'max_items' => ['type' => 'number', 'label' => 'Maximum Items', 'default' => 20],
                    'required_layouts' => ['type' => 'multiselect', 'label' => 'Required Layouts'],
                    'validate_sub_fields' => ['type' => 'boolean', 'label' => 'Validate Sub-fields', 'default' => true]
                ];
                break;

            case 'select':
            case 'radio':
                $rules = [
                    'validate_options' => ['type' => 'boolean', 'label' => 'Validate Against Options', 'default' => true]
                ];
                break;

            case 'multiselect':
            case 'checkbox':
                $rules = [
                    'min_selections' => ['type' => 'number', 'label' => 'Minimum Selections', 'default' => 0],
                    'max_selections' => ['type' => 'number', 'label' => 'Maximum Selections'],
                    'validate_options' => ['type' => 'boolean', 'label' => 'Validate Against Options', 'default' => true]
                ];
                break;
        }

        return array_merge($rules, $this->getCommonRules());
    }

    /**
     * Get common validation rules
     */
    private function getCommonRules()
    {
        return [
            'custom_validator' => ['type' => 'text', 'label' => 'Custom Validator Function'],
            'error_message' => ['type' => 'text', 'label' => 'Custom Error Message'],
            'conditional_required' => ['type' => 'text', 'label' => 'Conditional Required (field:value)'],
            'depends_on' => ['type' => 'text', 'label' => 'Depends on Field'],
            'readonly_when' => ['type' => 'text', 'label' => 'Readonly When (field:value)']
        ];
    }

    /**
     * Create validation rule template
     */
    public function createRuleTemplate($name, $description, $rules)
    {
        $templateId = $this->db->insert('validation_rule_templates', [
            'name' => $name,
            'description' => $description,
            'rules' => json_encode($rules),
            'created_by' => auth()->id(),
            'created_at' => date('Y-m-d H:i:s')
        ]);

        return $templateId;
    }

    /**
     * Get validation rule templates
     */
    public function getRuleTemplates($category = null)
    {
        $where = [];
        $params = [];

        if ($category) {
            $where[] = 'category = ?';
            $params[] = $category;
        }

        $whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';

        return $this->db->select(
            "SELECT * FROM validation_rule_templates {$whereClause} ORDER BY name",
            $params
        );
    }

    /**
     * Apply rule template to field
     */
    public function applyRuleTemplate($fieldId, $templateId)
    {
        $template = $this->db->selectOne(
            "SELECT * FROM validation_rule_templates WHERE id = ?",
            [$templateId]
        );

        if (!$template) {
            throw new \RuntimeException('Validation rule template not found');
        }

        $rules = json_decode($template['rules'], true);

        // Update field validation rules
        $this->db->update('fields', [
            'validation_rules' => json_encode($rules)
        ], 'id = ?', [$fieldId]);

        return true;
    }

    /**
     * Register custom validation rule
     */
    public function registerCustomRule($name, $callback, $message = null)
    {
        $this->customRules[$name] = [
            'callback' => $callback,
            'message' => $message ?: "The {field} field is invalid"
        ];

        return $this;
    }

    /**
     * Get custom validation rules
     */
    public function getCustomRules()
    {
        return $this->customRules;
    }

    /**
     * Validate with custom rule
     */
    public function validateCustomRule($ruleName, $value, $parameters = [])
    {
        if (!isset($this->customRules[$ruleName])) {
            throw new \RuntimeException("Custom validation rule '{$ruleName}' not found");
        }

        $rule = $this->customRules[$ruleName];
        $callback = $rule['callback'];

        if (is_callable($callback)) {
            return call_user_func($callback, $value, $parameters);
        }

        return false;
    }

    /**
     * Initialize default validation rules
     */
    private function initializeDefaultRules()
    {
        // Register common custom rules
        $this->registerCustomRule('alpha', function($value) {
            return preg_match('/^[a-zA-Z]+$/', $value);
        }, 'The {field} field must contain only letters');

        $this->registerCustomRule('alpha_num', function($value) {
            return preg_match('/^[a-zA-Z0-9]+$/', $value);
        }, 'The {field} field must contain only letters and numbers');

        $this->registerCustomRule('alpha_dash', function($value) {
            return preg_match('/^[a-zA-Z0-9_-]+$/', $value);
        }, 'The {field} field must contain only letters, numbers, dashes and underscores');

        $this->registerCustomRule('slug', function($value) {
            return preg_match('/^[a-z0-9-]+$/', $value);
        }, 'The {field} field must be a valid slug');

        $this->registerCustomRule('json', function($value) {
            json_decode($value);
            return json_last_error() === JSON_ERROR_NONE;
        }, 'The {field} field must be valid JSON');

        $this->registerCustomRule('base64', function($value) {
            return base64_encode(base64_decode($value, true)) === $value;
        }, 'The {field} field must be valid base64');
    }

    /**
     * Get HTML tags for WYSIWYG validation
     */
    private function getHtmlTags()
    {
        return [
            'p' => 'Paragraph',
            'br' => 'Line Break',
            'strong' => 'Strong',
            'b' => 'Bold',
            'em' => 'Emphasis',
            'i' => 'Italic',
            'u' => 'Underline',
            'h1' => 'Heading 1',
            'h2' => 'Heading 2',
            'h3' => 'Heading 3',
            'h4' => 'Heading 4',
            'h5' => 'Heading 5',
            'h6' => 'Heading 6',
            'ul' => 'Unordered List',
            'ol' => 'Ordered List',
            'li' => 'List Item',
            'a' => 'Link',
            'img' => 'Image',
            'table' => 'Table',
            'tr' => 'Table Row',
            'td' => 'Table Cell',
            'th' => 'Table Header',
            'div' => 'Division',
            'span' => 'Span',
            'blockquote' => 'Blockquote',
            'code' => 'Code',
            'pre' => 'Preformatted'
        ];
    }

    /**
     * Get timezones
     */
    private function getTimezones()
    {
        $timezones = [];
        foreach (timezone_identifiers_list() as $timezone) {
            $timezones[$timezone] = $timezone;
        }
        return $timezones;
    }

    /**
     * Get country codes
     */
    private function getCountryCodes()
    {
        return [
            '+1' => 'United States/Canada (+1)',
            '+44' => 'United Kingdom (+44)',
            '+49' => 'Germany (+49)',
            '+33' => 'France (+33)',
            '+39' => 'Italy (+39)',
            '+34' => 'Spain (+34)',
            '+31' => 'Netherlands (+31)',
            '+32' => 'Belgium (+32)',
            '+41' => 'Switzerland (+41)',
            '+43' => 'Austria (+43)',
            '+90' => 'Turkey (+90)',
            '+86' => 'China (+86)',
            '+81' => 'Japan (+81)',
            '+82' => 'South Korea (+82)',
            '+91' => 'India (+91)',
            '+61' => 'Australia (+61)',
            '+55' => 'Brazil (+55)',
            '+52' => 'Mexico (+52)',
            '+7' => 'Russia (+7)',
            '+48' => 'Poland (+48)'
        ];
    }
}
