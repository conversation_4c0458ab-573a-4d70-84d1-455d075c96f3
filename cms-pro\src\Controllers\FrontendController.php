<?php

namespace CmsPro\Controllers;

use CmsPro\Models\Page;
use CmsPro\Services\SettingsService;
use CmsPro\Services\SeoService;
use CmsPro\Services\CacheService;
use CmsPro\Services\ActivityLogger;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * Frontend Controller
 * 
 * Handles public website pages and functionality
 */
class FrontendController extends BaseController
{
    private $settingsService;
    private $seoService;
    private $cacheService;
    private $activityLogger;

    public function __construct()
    {
        parent::__construct();
        
        $this->settingsService = new SettingsService();
        $this->seoService = new SeoService();
        $this->cacheService = new CacheService();
        $this->activityLogger = new ActivityLogger();
    }

    /**
     * Display homepage
     */
    public function index(Request $request)
    {
        $this->request = $request;

        try {
            // Check maintenance mode
            if ($this->isMaintenanceMode() && !$this->isAdminUser()) {
                return $this->maintenancePage();
            }

            // Try to get cached homepage
            $cacheKey = 'homepage_' . $this->getCurrentLanguage();
            $cachedContent = $this->cacheService->get($cacheKey);
            
            if ($cachedContent && !$this->settingsService->get('debug_mode', false)) {
                return new Response($cachedContent);
            }

            // Get homepage content
            $homepage = $this->getHomepage();
            
            // Get recent pages for homepage
            $recentPages = $this->getRecentPages(6);
            
            // Get site settings
            $siteSettings = $this->getSiteSettings();
            
            // Generate SEO data
            $seoData = $this->seoService->generateSeoData([
                'title' => $siteSettings['seo_title'] ?: $siteSettings['site_name'],
                'description' => $siteSettings['seo_description'] ?: $siteSettings['site_description'],
                'keywords' => $siteSettings['seo_keywords'],
                'url' => $siteSettings['site_url'],
                'type' => 'website'
            ]);

            // Log page view
            $this->activityLogger->log('page_viewed', [
                'page_type' => 'homepage',
                'ip_address' => $request->getClientIp(),
                'user_agent' => $request->headers->get('User-Agent'),
                'referrer' => $request->headers->get('Referer')
            ]);

            $data = [
                'page' => $homepage,
                'recent_pages' => $recentPages,
                'site_settings' => $siteSettings,
                'seo' => $seoData,
                'is_homepage' => true
            ];

            $content = $this->view('frontend/index.twig', $data);
            
            // Cache the content
            if ($this->settingsService->get('cache_enabled', true)) {
                $cacheLifetime = $this->settingsService->get('cache_lifetime', 60);
                $this->cacheService->set($cacheKey, $content, $cacheLifetime * 60);
            }

            return new Response($content);

        } catch (\Exception $e) {
            $this->logError('frontend_homepage_error', $e, $request);
            return $this->errorPage(500, 'Internal Server Error');
        }
    }

    /**
     * Display page by slug
     */
    public function page(Request $request, $slug)
    {
        $this->request = $request;

        try {
            // Check maintenance mode
            if ($this->isMaintenanceMode() && !$this->isAdminUser()) {
                return $this->maintenancePage();
            }

            // Try to get cached page
            $cacheKey = 'page_' . $slug . '_' . $this->getCurrentLanguage();
            $cachedContent = $this->cacheService->get($cacheKey);
            
            if ($cachedContent && !$this->settingsService->get('debug_mode', false)) {
                return new Response($cachedContent);
            }

            // Find page by slug
            $page = Page::findBySlug($slug);
            
            if (!$page || $page->getStatus() !== 'published') {
                return $this->errorPage(404, 'Page Not Found');
            }

            // Check if page is password protected
            if ($page->isPasswordProtected() && !$this->isPageAccessGranted($page)) {
                return $this->passwordProtectedPage($page);
            }

            // Get site settings
            $siteSettings = $this->getSiteSettings();
            
            // Generate SEO data
            $seoData = $this->seoService->generateSeoData([
                'title' => $page->getSeoTitle() ?: $page->getTitle(),
                'description' => $page->getSeoDescription() ?: $page->getExcerpt(),
                'keywords' => $page->getSeoKeywords(),
                'url' => $siteSettings['site_url'] . '/' . $slug,
                'type' => 'article',
                'image' => $page->getFeaturedImage(),
                'published_time' => $page->getPublishedAt(),
                'modified_time' => $page->getUpdatedAt(),
                'author' => $page->getAuthor()
            ]);

            // Increment page views
            $page->incrementViews();

            // Log page view
            $this->activityLogger->log('page_viewed', [
                'page_id' => $page->getId(),
                'page_slug' => $slug,
                'page_title' => $page->getTitle(),
                'ip_address' => $request->getClientIp(),
                'user_agent' => $request->headers->get('User-Agent'),
                'referrer' => $request->headers->get('Referer')
            ]);

            $data = [
                'page' => $page,
                'site_settings' => $siteSettings,
                'seo' => $seoData,
                'is_homepage' => false
            ];

            $content = $this->view('frontend/page.twig', $data);
            
            // Cache the content
            if ($this->settingsService->get('cache_enabled', true)) {
                $cacheLifetime = $this->settingsService->get('cache_lifetime', 60);
                $this->cacheService->set($cacheKey, $content, $cacheLifetime * 60);
            }

            return new Response($content);

        } catch (\Exception $e) {
            $this->logError('frontend_page_error', $e, $request);
            return $this->errorPage(500, 'Internal Server Error');
        }
    }

    /**
     * Handle contact form submission
     */
    public function contact(Request $request)
    {
        $this->request = $request;

        try {
            if ($request->getMethod() === 'POST') {
                return $this->handleContactForm($request);
            }

            // Display contact page
            $contactPage = $this->getContactPage();
            $siteSettings = $this->getSiteSettings();
            
            $seoData = $this->seoService->generateSeoData([
                'title' => 'Contact Us - ' . $siteSettings['site_name'],
                'description' => 'Get in touch with us',
                'url' => $siteSettings['site_url'] . '/contact',
                'type' => 'website'
            ]);

            $data = [
                'page' => $contactPage,
                'site_settings' => $siteSettings,
                'seo' => $seoData,
                'is_homepage' => false
            ];

            return new Response($this->view('frontend/contact.twig', $data));

        } catch (\Exception $e) {
            $this->logError('frontend_contact_error', $e, $request);
            return $this->errorPage(500, 'Internal Server Error');
        }
    }

    /**
     * Generate sitemap.xml
     */
    public function sitemap(Request $request)
    {
        $this->request = $request;

        try {
            // Try to get cached sitemap
            $cacheKey = 'sitemap_xml';
            $cachedSitemap = $this->cacheService->get($cacheKey);
            
            if ($cachedSitemap && !$this->settingsService->get('debug_mode', false)) {
                return new Response($cachedSitemap, 200, [
                    'Content-Type' => 'application/xml'
                ]);
            }

            // Generate sitemap
            $sitemap = $this->seoService->generateSitemap();
            
            // Cache the sitemap
            if ($this->settingsService->get('cache_enabled', true)) {
                $this->cacheService->set($cacheKey, $sitemap, 24 * 60 * 60); // Cache for 24 hours
            }

            return new Response($sitemap, 200, [
                'Content-Type' => 'application/xml'
            ]);

        } catch (\Exception $e) {
            $this->logError('frontend_sitemap_error', $e, $request);
            return $this->errorPage(500, 'Internal Server Error');
        }
    }

    /**
     * Generate robots.txt
     */
    public function robots(Request $request)
    {
        $this->request = $request;

        try {
            $robotsTxt = $this->settingsService->get('robots_txt', "User-agent: *\nDisallow:");
            $siteUrl = $this->settingsService->get('site_url', 'http://localhost');
            
            // Add sitemap reference
            $robotsTxt .= "\n\nSitemap: " . $siteUrl . "/sitemap.xml";

            return new Response($robotsTxt, 200, [
                'Content-Type' => 'text/plain'
            ]);

        } catch (\Exception $e) {
            $this->logError('frontend_robots_error', $e, $request);
            return new Response("User-agent: *\nDisallow:", 200, [
                'Content-Type' => 'text/plain'
            ]);
        }
    }

    /**
     * Check if site is in maintenance mode
     */
    private function isMaintenanceMode()
    {
        return $this->settingsService->get('maintenance_mode', false);
    }

    /**
     * Check if current user is admin
     */
    private function isAdminUser()
    {
        return auth()->check() && auth()->can('admin.access');
    }

    /**
     * Get current language
     */
    private function getCurrentLanguage()
    {
        return $this->settingsService->get('language', 'en');
    }

    /**
     * Get homepage content
     */
    private function getHomepage()
    {
        // Try to find a page marked as homepage
        $homepage = Page::findHomepage();
        
        if (!$homepage) {
            // Create a default homepage object
            $homepage = new \stdClass();
            $homepage->title = $this->settingsService->get('site_name', 'Welcome');
            $homepage->content = $this->settingsService->get('site_description', 'Welcome to our website');
            $homepage->slug = '';
        }
        
        return $homepage;
    }

    /**
     * Get recent pages
     */
    private function getRecentPages($limit = 6)
    {
        return Page::getPublished($limit);
    }

    /**
     * Get site settings for frontend
     */
    private function getSiteSettings()
    {
        return [
            'site_name' => $this->settingsService->get('site_name', 'CMS Pro'),
            'site_description' => $this->settingsService->get('site_description', ''),
            'site_url' => $this->settingsService->get('site_url', 'http://localhost'),
            'site_logo' => $this->settingsService->get('site_logo', ''),
            'site_favicon' => $this->settingsService->get('site_favicon', ''),
            'seo_title' => $this->settingsService->get('seo_title', ''),
            'seo_description' => $this->settingsService->get('seo_description', ''),
            'seo_keywords' => $this->settingsService->get('seo_keywords', ''),
            'google_analytics' => $this->settingsService->get('google_analytics', ''),
            'facebook_pixel' => $this->settingsService->get('facebook_pixel', ''),
            'language' => $this->settingsService->get('language', 'en'),
            'timezone' => $this->settingsService->get('timezone', 'UTC'),
            'date_format' => $this->settingsService->get('date_format', 'Y-m-d'),
            'time_format' => $this->settingsService->get('time_format', 'H:i:s')
        ];
    }

    /**
     * Display maintenance page
     */
    private function maintenancePage()
    {
        $siteSettings = $this->getSiteSettings();
        
        $data = [
            'site_settings' => $siteSettings,
            'message' => 'We are currently performing scheduled maintenance. Please check back soon.'
        ];

        return new Response($this->view('frontend/maintenance.twig', $data), 503);
    }

    /**
     * Display error page
     */
    private function errorPage($code, $message)
    {
        $siteSettings = $this->getSiteSettings();
        
        $data = [
            'site_settings' => $siteSettings,
            'error_code' => $code,
            'error_message' => $message
        ];

        return new Response($this->view('frontend/error.twig', $data), $code);
    }

    /**
     * Log errors
     */
    private function logError($event, \Exception $e, Request $request)
    {
        $logData = [
            'event' => $event,
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'ip_address' => $request->getClientIp(),
            'user_agent' => $request->headers->get('User-Agent'),
            'url' => $request->getUri()
        ];

        error_log(json_encode($logData), 3, storage_path('logs/frontend.log'));
        
        if ($this->activityLogger) {
            $this->activityLogger->log($event, $logData);
        }
    }
}
