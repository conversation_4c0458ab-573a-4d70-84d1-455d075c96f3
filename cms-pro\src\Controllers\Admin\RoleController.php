<?php

namespace CmsPro\Controllers\Admin;

use CmsPro\Controllers\BaseController;
use CmsPro\Models\Role;
use CmsPro\Models\Permission;
use CmsPro\Models\User;
use CmsPro\Services\ValidationService;
use CmsPro\Services\SanitizationService;
use CmsPro\Services\ActivityLogger;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Role Controller for Admin Panel
 * 
 * @package CmsPro\Controllers\Admin
 */
class RoleController extends BaseController
{
    private $validationService;
    private $sanitizationService;
    private $activityLogger;

    public function __construct()
    {
        parent::__construct();
        
        $this->validationService = new ValidationService();
        $this->sanitizationService = new SanitizationService();
        $this->activityLogger = new ActivityLogger();
    }

    /**
     * Display roles list
     */
    public function index(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('roles.view')) {
                return $this->forbidden('You do not have permission to view roles.');
            }

            // Get filters
            $status = $request->query->get('status', 'all');
            $type = $request->query->get('type', 'all'); // system or custom
            $search = $request->query->get('search', '');

            // Build query
            $db = app()->getDatabase();
            $whereConditions = [];
            $params = [];

            // Apply filters
            if ($status !== 'all') {
                $whereConditions[] = "status = ?";
                $params[] = $status;
            }

            if ($type === 'system') {
                $whereConditions[] = "is_system = 1";
            } elseif ($type === 'custom') {
                $whereConditions[] = "is_system = 0";
            }

            if ($search) {
                $search = $this->sanitizationService->sanitizeInput($search);
                $whereConditions[] = "(name LIKE ? OR description LIKE ?)";
                $searchTerm = "%{$search}%";
                $params = array_merge($params, [$searchTerm, $searchTerm]);
            }

            $whereClause = $whereConditions ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

            // Get roles with user counts
            $rolesQuery = "
                SELECT r.*, 
                       COUNT(ur.user_id) as users_count,
                       GROUP_CONCAT(p.name SEPARATOR ', ') as permission_names
                FROM roles r 
                LEFT JOIN user_roles ur ON r.id = ur.role_id 
                LEFT JOIN role_permissions rp ON r.id = rp.role_id
                LEFT JOIN permissions p ON rp.permission_id = p.id AND p.status = 'active'
                {$whereClause}
                GROUP BY r.id 
                ORDER BY r.level DESC, r.name ASC
            ";

            $roles = $db->select($rolesQuery, $params);

            // Get role statistics
            $stats = [
                'total' => $db->selectOne("SELECT COUNT(*) as count FROM roles")['count'],
                'active' => $db->selectOne("SELECT COUNT(*) as count FROM roles WHERE status = 'active'")['count'],
                'system' => $db->selectOne("SELECT COUNT(*) as count FROM roles WHERE is_system = 1")['count'],
                'custom' => $db->selectOne("SELECT COUNT(*) as count FROM roles WHERE is_system = 0")['count']
            ];

            // Log activity
            $this->activityLogger->log('roles_viewed', [
                'user_id' => auth()->id(),
                'filters' => compact('status', 'type', 'search')
            ]);

            $data = [
                'title' => __('Roles & Permissions'),
                'roles' => $roles,
                'stats' => $stats,
                'statuses' => Role::getStatuses(),
                'current_filters' => compact('status', 'type', 'search')
            ];

            return $this->view('admin/roles/index.twig', $data);

        } catch (\Exception $e) {
            $this->logError('roles_index_error', $e, $request);
            $this->flashError(__('An error occurred while loading roles.'));
            return $this->redirectToRoute('admin.dashboard');
        }
    }

    /**
     * Show create role form
     */
    public function create(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('roles.create')) {
                return $this->forbidden('You do not have permission to create roles.');
            }

            // Get available permissions grouped by category
            $permissions = Permission::getGroupedPermissions();

            $data = [
                'title' => __('Create New Role'),
                'role' => new Role(), // Empty role for form
                'permissions' => $permissions,
                'statuses' => Role::getStatuses()
            ];

            return $this->view('admin/roles/create.twig', $data);

        } catch (\Exception $e) {
            $this->logError('role_create_form_error', $e, $request);
            $this->flashError(__('An error occurred while loading the create form.'));
            return $this->redirectToRoute('admin.roles.index');
        }
    }

    /**
     * Store new role
     */
    public function store(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('roles.create')) {
                return $this->forbidden('You do not have permission to create roles.');
            }

            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Invalid CSRF token'
                    ], 403);
                }
                
                $this->flashError(__('Security token mismatch. Please try again.'));
                return $this->back();
            }

            // Validate input
            $rules = [
                'name' => 'required|string|max:100',
                'slug' => 'nullable|string|max:100|unique:roles,slug',
                'description' => 'nullable|string|max:500',
                'level' => 'required|integer|min:1|max:99',
                'status' => 'required|in:' . implode(',', array_keys(Role::getStatuses())),
                'permissions' => 'nullable|array'
            ];

            $data = $this->validationService->validate($request->request->all(), $rules);

            // Sanitize input
            $data['name'] = $this->sanitizationService->sanitizeInput($data['name']);
            $data['description'] = $this->sanitizationService->sanitizeInput($data['description'] ?? '');
            
            // Generate slug if not provided
            if (empty($data['slug'])) {
                $data['slug'] = $this->generateSlug($data['name']);
            } else {
                $data['slug'] = $this->sanitizationService->sanitizeInput($data['slug']);
            }

            // Ensure level doesn't exceed current user's highest role level
            $currentUserMaxLevel = $this->getCurrentUserMaxRoleLevel();
            if ($data['level'] >= $currentUserMaxLevel) {
                throw new \Exception('You cannot create a role with level equal to or higher than your highest role.');
            }

            // Set additional fields
            $data['is_system'] = 0; // Custom roles are never system roles
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');

            // Create role
            $db = app()->getDatabase();
            $roleId = $db->insert('roles', $data);

            if ($roleId) {
                $role = Role::find($roleId);

                // Assign permissions
                if (!empty($data['permissions'])) {
                    foreach ($data['permissions'] as $permissionId) {
                        $role->assignPermission($permissionId);
                    }
                }

                // Log activity
                $this->activityLogger->log('role_created', [
                    'user_id' => auth()->id(),
                    'role_id' => $roleId,
                    'role_name' => $data['name'],
                    'permissions' => $data['permissions'] ?? []
                ]);

                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => true,
                        'message' => __('Role created successfully.'),
                        'redirect' => route('admin.roles.edit', ['id' => $roleId])
                    ]);
                }

                $this->flashSuccess(__('Role created successfully.'));
                
                // Redirect based on action
                $action = $request->request->get('action', 'save');
                if ($action === 'save_and_continue') {
                    return $this->redirectToRoute('admin.roles.edit', ['id' => $roleId]);
                } elseif ($action === 'save_and_new') {
                    return $this->redirectToRoute('admin.roles.create');
                }
                
                return $this->redirectToRoute('admin.roles.index');
            }

            throw new \Exception('Failed to create role');

        } catch (\Exception $e) {
            $this->logError('role_store_error', $e, $request);
            
            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => $e->getMessage()
                ], 500);
            }
            
            $this->flashError($e->getMessage());
            return $this->back();
        }
    }

    /**
     * Generate unique slug
     */
    private function generateSlug($name)
    {
        $baseSlug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '_', $name), '_'));
        $slug = $baseSlug;
        $counter = 1;

        $db = app()->getDatabase();
        
        // Check for existing slugs
        while ($db->selectOne("SELECT id FROM roles WHERE slug = ?", [$slug])) {
            $slug = $baseSlug . '_' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Get current user's maximum role level
     */
    private function getCurrentUserMaxRoleLevel()
    {
        $user = auth()->user();
        $roles = $user->getRoles();
        
        $maxLevel = 0;
        foreach ($roles as $role) {
            if ($role['level'] > $maxLevel) {
                $maxLevel = $role['level'];
            }
        }
        
        return $maxLevel;
    }

    /**
     * Validate CSRF token
     */
    private function validateCsrfToken(Request $request)
    {
        $token = $request->request->get('_token') ?: $request->headers->get('X-CSRF-TOKEN');
        
        if (!$token) {
            return false;
        }

        $sessionToken = session()->get('_token');
        return $sessionToken && hash_equals($sessionToken, $token);
    }

    /**
     * Show edit role form
     */
    public function edit(Request $request, $id)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('roles.edit')) {
                return $this->forbidden('You do not have permission to edit roles.');
            }

            // Find role
            $role = Role::find($id);
            if (!$role) {
                $this->flashError(__('Role not found.'));
                return $this->redirectToRoute('admin.roles.index');
            }

            // Check if user can edit this role
            if (!$this->canEditRole($role)) {
                return $this->forbidden('You cannot edit this role.');
            }

            // Get role's current permissions
            $rolePermissions = $role->getPermissions();
            $rolePermissionIds = array_column($rolePermissions, 'id');

            // Get available permissions grouped by category
            $permissions = Permission::getGroupedPermissions();

            // Get users with this role
            $usersWithRole = $this->getUsersWithRole($id);

            $data = [
                'title' => __('Edit Role: :name', ['name' => $role->name]),
                'role' => $role,
                'role_permissions' => $rolePermissionIds,
                'permissions' => $permissions,
                'statuses' => Role::getStatuses(),
                'users_with_role' => $usersWithRole
            ];

            return $this->view('admin/roles/edit.twig', $data);

        } catch (\Exception $e) {
            $this->logError('role_edit_form_error', $e, $request);
            $this->flashError(__('An error occurred while loading the edit form.'));
            return $this->redirectToRoute('admin.roles.index');
        }
    }

    /**
     * Update role
     */
    public function update(Request $request, $id)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('roles.edit')) {
                return $this->forbidden('You do not have permission to edit roles.');
            }

            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Invalid CSRF token'
                    ], 403);
                }

                $this->flashError(__('Security token mismatch. Please try again.'));
                return $this->back();
            }

            // Find role
            $role = Role::find($id);
            if (!$role) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Role not found'
                    ], 404);
                }

                $this->flashError(__('Role not found.'));
                return $this->redirectToRoute('admin.roles.index');
            }

            // Check if user can edit this role
            if (!$this->canEditRole($role)) {
                return $this->forbidden('You cannot edit this role.');
            }

            // Validate input
            $rules = [
                'name' => 'required|string|max:100',
                'slug' => 'nullable|string|max:100|unique:roles,slug,' . $id,
                'description' => 'nullable|string|max:500',
                'level' => 'required|integer|min:1|max:99',
                'status' => 'required|in:' . implode(',', array_keys(Role::getStatuses())),
                'permissions' => 'nullable|array'
            ];

            $data = $this->validationService->validate($request->request->all(), $rules);

            // Sanitize input
            $data['name'] = $this->sanitizationService->sanitizeInput($data['name']);
            $data['description'] = $this->sanitizationService->sanitizeInput($data['description'] ?? '');

            // Generate slug if not provided
            if (empty($data['slug'])) {
                $data['slug'] = $this->generateSlug($data['name']);
            } else {
                $data['slug'] = $this->sanitizationService->sanitizeInput($data['slug']);
            }

            // Ensure level doesn't exceed current user's highest role level (unless editing own role)
            $currentUserMaxLevel = $this->getCurrentUserMaxRoleLevel();
            if ($data['level'] >= $currentUserMaxLevel && $role->level != $data['level']) {
                throw new \Exception('You cannot set a role level equal to or higher than your highest role.');
            }

            // Update role
            $data['updated_at'] = date('Y-m-d H:i:s');
            $role->update($data);

            // Update permissions (if user has permission)
            if (auth()->can('roles.manage_permissions') && isset($data['permissions'])) {
                $role->syncPermissions($data['permissions']);
            }

            // Log activity
            $this->activityLogger->log('role_updated', [
                'user_id' => auth()->id(),
                'role_id' => $role->id,
                'role_name' => $role->name,
                'changes' => array_keys($data)
            ]);

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => true,
                    'message' => __('Role updated successfully.'),
                    'redirect' => route('admin.roles.edit', ['id' => $role->id])
                ]);
            }

            $this->flashSuccess(__('Role updated successfully.'));

            // Redirect based on action
            $action = $request->request->get('action', 'save');
            if ($action === 'save_and_continue') {
                return $this->redirectToRoute('admin.roles.edit', ['id' => $role->id]);
            } elseif ($action === 'save_and_new') {
                return $this->redirectToRoute('admin.roles.create');
            }

            return $this->redirectToRoute('admin.roles.index');

        } catch (\Exception $e) {
            $this->logError('role_update_error', $e, $request);

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => $e->getMessage()
                ], 500);
            }

            $this->flashError($e->getMessage());
            return $this->back();
        }
    }

    /**
     * Delete role
     */
    public function destroy(Request $request, $id)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('roles.delete')) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'You do not have permission to delete roles.'
                    ], 403);
                }

                return $this->forbidden('You do not have permission to delete roles.');
            }

            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Invalid CSRF token'
                    ], 403);
                }

                $this->flashError(__('Security token mismatch. Please try again.'));
                return $this->back();
            }

            // Find role
            $role = Role::find($id);
            if (!$role) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Role not found'
                    ], 404);
                }

                $this->flashError(__('Role not found.'));
                return $this->redirectToRoute('admin.roles.index');
            }

            // Check if role can be deleted
            if (!$role->canBeDeleted()) {
                $message = $role->isSystem()
                    ? 'System roles cannot be deleted.'
                    : 'This role cannot be deleted because it has users assigned to it.';

                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => $message
                    ], 400);
                }

                $this->flashError(__($message));
                return $this->back();
            }

            // Check if user can delete this role
            if (!$this->canEditRole($role)) {
                return $this->forbidden('You cannot delete this role.');
            }

            $roleName = $role->name;

            // Delete role (this will also remove role_permissions via foreign key)
            $db = app()->getDatabase();
            $db->delete("DELETE FROM role_permissions WHERE role_id = ?", [$role->id]);
            $db->delete("DELETE FROM roles WHERE id = ?", [$role->id]);

            // Log activity
            $this->activityLogger->log('role_deleted', [
                'user_id' => auth()->id(),
                'role_id' => $role->id,
                'role_name' => $roleName
            ]);

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => true,
                    'message' => __('Role deleted successfully.')
                ]);
            }

            $this->flashSuccess(__('Role deleted successfully.'));
            return $this->redirectToRoute('admin.roles.index');

        } catch (\Exception $e) {
            $this->logError('role_delete_error', $e, $request);

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => __('An error occurred while deleting the role.')
                ], 500);
            }

            $this->flashError(__('An error occurred while deleting the role.'));
            return $this->back();
        }
    }

    /**
     * Check if current user can edit target role
     */
    private function canEditRole(Role $targetRole)
    {
        $currentUser = auth()->user();

        // Super admin can edit any role
        if ($currentUser->isSuperAdmin()) {
            return true;
        }

        // Cannot edit system roles unless you are super admin
        if ($targetRole->isSystem()) {
            return false;
        }

        // Cannot edit roles with level equal to or higher than your highest role
        $currentUserMaxLevel = $this->getCurrentUserMaxRoleLevel();
        return $targetRole->getLevel() < $currentUserMaxLevel;
    }

    /**
     * Get users with specific role
     */
    private function getUsersWithRole($roleId)
    {
        $db = app()->getDatabase();

        return $db->select(
            "SELECT u.id, u.first_name, u.last_name, u.email, u.status
             FROM users u
             INNER JOIN user_roles ur ON u.id = ur.user_id
             WHERE ur.role_id = ? AND u.status != 'deleted'
             ORDER BY u.first_name, u.last_name
             LIMIT 10",
            [$roleId]
        );
    }

    /**
     * Log errors
     */
    private function logError($event, \Exception $e, Request $request)
    {
        $logData = [
            'event' => $event,
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'user_id' => auth()->id(),
            'ip_address' => $request->getClientIp(),
            'user_agent' => $request->headers->get('User-Agent'),
            'url' => $request->getUri()
        ];

        error_log(json_encode($logData), 3, storage_path('logs/roles.log'));

        if ($this->activityLogger) {
            $this->activityLogger->log($event, $logData);
        }
    }
}
