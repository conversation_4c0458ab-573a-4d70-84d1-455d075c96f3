<?php

namespace CmsPro\Services;

use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

/**
 * Mail Service
 * 
 * @package CmsPro\Services
 */
class MailService
{
    private $mailer;

    public function __construct()
    {
        $this->mailer = new PHPMailer(true);
        $this->configure();
    }

    /**
     * Configure PHPMailer
     */
    private function configure()
    {
        try {
            // Server settings
            $this->mailer->isSMTP();
            $this->mailer->Host = env('MAIL_HOST', 'smtp.gmail.com');
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = env('MAIL_USERNAME');
            $this->mailer->Password = env('MAIL_PASSWORD');
            $this->mailer->SMTPSecure = env('MAIL_ENCRYPTION', 'tls');
            $this->mailer->Port = env('MAIL_PORT', 587);

            // Default sender
            $this->mailer->setFrom(
                env('MAIL_FROM_ADDRESS', '<EMAIL>'),
                env('MAIL_FROM_NAME', 'CMS Pro')
            );

            // Content settings
            $this->mailer->isHTML(true);
            $this->mailer->CharSet = 'UTF-8';

        } catch (Exception $e) {
            throw new \RuntimeException('Mail configuration error: ' . $e->getMessage());
        }
    }

    /**
     * Send email
     */
    public function send($to, $toName, $subject, $body, $altBody = null)
    {
        try {
            // Recipients
            $this->mailer->addAddress($to, $toName);

            // Content
            $this->mailer->Subject = $subject;
            $this->mailer->Body = $body;
            
            if ($altBody) {
                $this->mailer->AltBody = $altBody;
            }

            $this->mailer->send();
            
            // Clear addresses for next email
            $this->mailer->clearAddresses();
            
            return true;

        } catch (Exception $e) {
            // Clear addresses even on error
            $this->mailer->clearAddresses();
            
            throw new \RuntimeException('Message could not be sent. Mailer Error: ' . $this->mailer->ErrorInfo);
        }
    }

    /**
     * Send email to multiple recipients
     */
    public function sendToMultiple($recipients, $subject, $body, $altBody = null)
    {
        try {
            // Add all recipients
            foreach ($recipients as $recipient) {
                if (is_array($recipient)) {
                    $this->mailer->addAddress($recipient['email'], $recipient['name'] ?? '');
                } else {
                    $this->mailer->addAddress($recipient);
                }
            }

            // Content
            $this->mailer->Subject = $subject;
            $this->mailer->Body = $body;
            
            if ($altBody) {
                $this->mailer->AltBody = $altBody;
            }

            $this->mailer->send();
            
            // Clear addresses for next email
            $this->mailer->clearAddresses();
            
            return true;

        } catch (Exception $e) {
            // Clear addresses even on error
            $this->mailer->clearAddresses();
            
            throw new \RuntimeException('Message could not be sent. Mailer Error: ' . $this->mailer->ErrorInfo);
        }
    }

    /**
     * Add attachment
     */
    public function addAttachment($path, $name = '')
    {
        try {
            $this->mailer->addAttachment($path, $name);
        } catch (Exception $e) {
            throw new \RuntimeException('Could not add attachment: ' . $e->getMessage());
        }
    }

    /**
     * Set reply-to address
     */
    public function setReplyTo($email, $name = '')
    {
        try {
            $this->mailer->addReplyTo($email, $name);
        } catch (Exception $e) {
            throw new \RuntimeException('Could not set reply-to: ' . $e->getMessage());
        }
    }

    /**
     * Add CC recipient
     */
    public function addCC($email, $name = '')
    {
        try {
            $this->mailer->addCC($email, $name);
        } catch (Exception $e) {
            throw new \RuntimeException('Could not add CC: ' . $e->getMessage());
        }
    }

    /**
     * Add BCC recipient
     */
    public function addBCC($email, $name = '')
    {
        try {
            $this->mailer->addBCC($email, $name);
        } catch (Exception $e) {
            throw new \RuntimeException('Could not add BCC: ' . $e->getMessage());
        }
    }

    /**
     * Test mail configuration
     */
    public function testConnection()
    {
        try {
            $this->mailer->smtpConnect();
            $this->mailer->smtpClose();
            return true;
        } catch (Exception $e) {
            throw new \RuntimeException('SMTP connection failed: ' . $e->getMessage());
        }
    }

    /**
     * Send test email
     */
    public function sendTestEmail($to, $toName = '')
    {
        $subject = 'Test Email from ' . config('app.name');
        $body = view('emails/test.twig', [
            'app_name' => config('app.name'),
            'test_time' => date('Y-m-d H:i:s')
        ]);

        return $this->send($to, $toName, $subject, $body);
    }
}
