<?php

namespace CmsPro\Controllers\Admin;

use CmsPro\Controllers\BaseController;
use CmsPro\Models\Content;
use CmsPro\Models\User;
use CmsPro\Services\ActivityLogger;
use CmsPro\Services\SecurityService;
use CmsPro\Services\PerformanceService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Admin Dashboard Controller
 *
 * @package CmsPro\Controllers\Admin
 */
class DashboardController extends BaseController
{
    private $activityLogger;
    private $securityService;
    private $performanceService;

    public function __construct()
    {
        parent::__construct();
        $this->activityLogger = new ActivityLogger();
        $this->securityService = new SecurityService();
        $this->performanceService = new PerformanceService();
    }

    /**
     * Admin dashboard - SECURITY & PERFORMANCE ENHANCED
     */
    public function index(Request $request)
    {
        $this->request = $request;

        try {
            // Enhanced security checks
            if (!auth()->check()) {
                return $this->redirectToRoute('admin.login');
            }

            if (!auth()->can('admin.access')) {
                $this->logSecurityEvent('unauthorized_admin_access', $request);
                return $this->forbidden('You do not have permission to access the admin panel.');
            }

            // Log admin access
            $this->activityLogger->log('admin_dashboard_accessed', [
                'user_id' => auth()->id(),
                'ip_address' => $request->getClientIp(),
                'user_agent' => $request->headers->get('User-Agent')
            ]);

            // Check for AJAX request (for dashboard widgets)
            if ($request->isXmlHttpRequest()) {
                return $this->handleAjaxRequest($request);
            }

            // Performance monitoring start
            $startTime = microtime(true);

            // Get dashboard data with caching
            $cacheKey = 'dashboard_data_' . auth()->id();
            $data = cache()->remember($cacheKey, 300, function() { // 5 minutes cache
                return [
                    'stats' => $this->getDashboardStats(),
                    'recent_activity' => $this->getRecentActivity(),
                    'quick_stats' => $this->getQuickStats(),
                    'system_info' => $this->getSystemInfo(),
                    'upcoming_schedules' => $this->getUpcomingSchedules(),
                    'popular_content' => $this->getPopularContent(),
                    'security_alerts' => $this->getSecurityAlerts()
                ];
            });

            // Add real-time data (not cached)
            $data['current_user'] = auth()->user();
            $data['notifications'] = $this->getNotifications();
            $data['title'] = __('Dashboard');
            $data['meta_description'] = __('Admin dashboard overview');

            // Performance monitoring
            $loadTime = microtime(true) - $startTime;
            $data['performance'] = [
                'load_time' => round($loadTime * 1000, 2), // in milliseconds
                'memory_usage' => $this->formatBytes(memory_get_usage(true)),
                'peak_memory' => $this->formatBytes(memory_get_peak_usage(true))
            ];

            return $this->view('admin/dashboard/index.twig', $data);

        } catch (\Exception $e) {
            $this->logSecurityEvent('dashboard_error', $request, [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->flashError(__('An error occurred while loading the dashboard.'));
            return $this->view('admin/dashboard/error.twig', [
                'title' => __('Dashboard Error'),
                'error' => app()->isDebug() ? $e->getMessage() : 'Internal server error'
            ]);
        }
    }

    /**
     * Get dashboard statistics
     */
    private function getDashboardStats()
    {
        $db = app()->getDatabase();
        
        $stats = [];
        
        try {
            // Users count
            $userCount = $db->selectOne("SELECT COUNT(*) as count FROM users WHERE deleted_at IS NULL");
            $stats['users'] = $userCount['count'] ?? 0;
            
            // Content count (will be implemented later)
            $stats['content'] = 0;
            
            // Pages count (will be implemented later)
            $stats['pages'] = 0;
            
            // Blog posts count (will be implemented later)
            $stats['blog_posts'] = 0;
            
        } catch (\Exception $e) {
            // If tables don't exist yet, return default stats
            $stats = [
                'users' => 0,
                'content' => 0,
                'pages' => 0,
                'blog_posts' => 0,
            ];
        }
        
        return $stats;
    }

    /**
     * Get recent activity
     */
    private function getRecentActivity()
    {
        $db = app()->getDatabase();
        
        try {
            $activities = $db->select(
                "SELECT ual.*, u.first_name, u.last_name, u.username 
                 FROM user_activity_log ual 
                 LEFT JOIN users u ON ual.user_id = u.id 
                 ORDER BY ual.created_at DESC 
                 LIMIT 10"
            );
            
            return $activities;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get system information - ENHANCED
     */
    private function getSystemInfo()
    {
        return [
            'php_version' => PHP_VERSION,
            'cms_version' => '1.0.0',
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'database_type' => 'MySQL',
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'disk_free_space' => $this->formatBytes(disk_free_space('.')),
            'disk_total_space' => $this->formatBytes(disk_total_space('.')),
            'server_load' => $this->getServerLoad(),
            'uptime' => $this->getServerUptime()
        ];
    }

    /**
     * Handle AJAX requests for dashboard widgets
     */
    private function handleAjaxRequest(Request $request)
    {
        $widget = $request->query->get('widget');

        switch ($widget) {
            case 'stats':
                return new JsonResponse($this->getDashboardStats());

            case 'activity':
                return new JsonResponse($this->getRecentActivity());

            case 'notifications':
                return new JsonResponse($this->getNotifications());

            case 'security':
                return new JsonResponse($this->getSecurityAlerts());

            default:
                return new JsonResponse(['error' => 'Invalid widget'], 400);
        }
    }

    /**
     * Get quick statistics
     */
    private function getQuickStats()
    {
        $db = app()->getDatabase();

        try {
            return [
                'today_logins' => $this->getTodayLogins(),
                'active_sessions' => $this->getActiveSessions(),
                'failed_logins' => $this->getFailedLogins(),
                'storage_usage' => $this->getStorageUsage()
            ];
        } catch (\Exception $e) {
            return [
                'today_logins' => 0,
                'active_sessions' => 0,
                'failed_logins' => 0,
                'storage_usage' => '0 MB'
            ];
        }
    }

    /**
     * Get upcoming schedules
     */
    private function getUpcomingSchedules()
    {
        $db = app()->getDatabase();

        try {
            return $db->select(
                "SELECT * FROM content_schedules
                 WHERE scheduled_at > NOW()
                 AND status = 'pending'
                 ORDER BY scheduled_at ASC
                 LIMIT 5"
            );
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get popular content
     */
    private function getPopularContent()
    {
        $db = app()->getDatabase();

        try {
            return $db->select(
                "SELECT c.*, COUNT(cv.id) as views
                 FROM content c
                 LEFT JOIN content_views cv ON c.id = cv.content_id
                 WHERE c.status = 'published'
                 AND cv.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                 GROUP BY c.id
                 ORDER BY views DESC
                 LIMIT 5"
            );
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get security alerts
     */
    private function getSecurityAlerts()
    {
        $alerts = [];

        try {
            // Check for recent failed logins
            $failedLogins = $this->getFailedLogins();
            if ($failedLogins > 10) {
                $alerts[] = [
                    'type' => 'warning',
                    'message' => "High number of failed login attempts: {$failedLogins}",
                    'action' => 'Review security logs'
                ];
            }

            // Check for outdated PHP version
            if (version_compare(PHP_VERSION, '7.4.0', '<')) {
                $alerts[] = [
                    'type' => 'danger',
                    'message' => 'PHP version is outdated and may have security vulnerabilities',
                    'action' => 'Update PHP to latest version'
                ];
            }

            // Check for weak passwords (placeholder)
            $weakPasswords = $this->checkWeakPasswords();
            if ($weakPasswords > 0) {
                $alerts[] = [
                    'type' => 'warning',
                    'message' => "{$weakPasswords} users have weak passwords",
                    'action' => 'Enforce strong password policy'
                ];
            }

            // Check disk space
            $freeSpace = disk_free_space('.');
            $totalSpace = disk_total_space('.');
            $usagePercent = (($totalSpace - $freeSpace) / $totalSpace) * 100;

            if ($usagePercent > 90) {
                $alerts[] = [
                    'type' => 'danger',
                    'message' => 'Disk space is critically low (' . round($usagePercent, 1) . '% used)',
                    'action' => 'Free up disk space immediately'
                ];
            } elseif ($usagePercent > 80) {
                $alerts[] = [
                    'type' => 'warning',
                    'message' => 'Disk space is running low (' . round($usagePercent, 1) . '% used)',
                    'action' => 'Consider cleaning up old files'
                ];
            }

        } catch (\Exception $e) {
            $alerts[] = [
                'type' => 'danger',
                'message' => 'Error checking security status',
                'action' => 'Check system logs'
            ];
        }

        return $alerts;
    }

    /**
     * Get notifications for current user
     */
    private function getNotifications()
    {
        $db = app()->getDatabase();

        try {
            return $db->select(
                "SELECT * FROM notifications
                 WHERE user_id = ?
                 AND read_at IS NULL
                 ORDER BY created_at DESC
                 LIMIT 10",
                [auth()->id()]
            );
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get today's login count
     */
    private function getTodayLogins()
    {
        $db = app()->getDatabase();

        try {
            $result = $db->selectOne(
                "SELECT COUNT(*) as count FROM user_activity_log
                 WHERE action = 'user_login'
                 AND DATE(created_at) = CURDATE()"
            );
            return $result['count'] ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get active sessions count
     */
    private function getActiveSessions()
    {
        $db = app()->getDatabase();

        try {
            $result = $db->selectOne(
                "SELECT COUNT(*) as count FROM user_sessions
                 WHERE last_activity > DATE_SUB(NOW(), INTERVAL 30 MINUTE)"
            );
            return $result['count'] ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get failed login attempts count
     */
    private function getFailedLogins()
    {
        $db = app()->getDatabase();

        try {
            $result = $db->selectOne(
                "SELECT COUNT(*) as count FROM security_log
                 WHERE event = 'login_failed'
                 AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)"
            );
            return $result['count'] ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get storage usage
     */
    private function getStorageUsage()
    {
        try {
            $uploadPath = storage_path('uploads');
            $size = $this->getDirectorySize($uploadPath);
            return $this->formatBytes($size);
        } catch (\Exception $e) {
            return '0 MB';
        }
    }

    /**
     * Check for weak passwords
     */
    private function checkWeakPasswords()
    {
        // This would need to be implemented based on your password policy
        // For now, return 0 as placeholder
        return 0;
    }

    /**
     * Get server load average
     */
    private function getServerLoad()
    {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return round($load[0], 2);
        }
        return 'N/A';
    }

    /**
     * Get server uptime
     */
    private function getServerUptime()
    {
        if (file_exists('/proc/uptime')) {
            $uptime = file_get_contents('/proc/uptime');
            $uptime = explode(' ', $uptime)[0];
            return $this->formatUptime($uptime);
        }
        return 'N/A';
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Format uptime to human readable format
     */
    private function formatUptime($seconds)
    {
        $days = floor($seconds / 86400);
        $hours = floor(($seconds % 86400) / 3600);
        $minutes = floor(($seconds % 3600) / 60);

        return "{$days}d {$hours}h {$minutes}m";
    }

    /**
     * Get directory size recursively
     */
    private function getDirectorySize($directory)
    {
        $size = 0;

        if (is_dir($directory)) {
            $files = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($directory)
            );

            foreach ($files as $file) {
                if ($file->isFile()) {
                    $size += $file->getSize();
                }
            }
        }

        return $size;
    }

    /**
     * Log security events
     */
    private function logSecurityEvent($event, Request $request, $additionalData = [])
    {
        try {
            $logData = array_merge([
                'event' => $event,
                'timestamp' => date('Y-m-d H:i:s'),
                'ip_address' => $request->getClientIp(),
                'user_agent' => $request->headers->get('User-Agent'),
                'user_id' => auth()->id(),
                'url' => $request->getUri()
            ], $additionalData);

            error_log(json_encode($logData), 3, storage_path('logs/security.log'));

        } catch (\Exception $e) {
            error_log('Failed to log security event: ' . $e->getMessage());
        }
    }
}
