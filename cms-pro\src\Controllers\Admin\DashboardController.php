<?php

namespace CmsPro\Controllers\Admin;

use CmsPro\Controllers\BaseController;
use Symfony\Component\HttpFoundation\Request;

/**
 * Admin Dashboard Controller
 * 
 * @package CmsPro\Controllers\Admin
 */
class DashboardController extends BaseController
{
    /**
     * Display admin dashboard
     */
    public function index(Request $request)
    {
        $this->request = $request;
        
        // Check admin access
        if (!auth()->can('admin.access')) {
            return $this->forbidden('You do not have permission to access the admin panel.');
        }
        
        $data = [
            'title' => __('Dashboard'),
            'meta_description' => __('Admin Dashboard'),
            'stats' => $this->getDashboardStats(),
            'recent_activity' => $this->getRecentActivity(),
            'system_info' => $this->getSystemInfo(),
        ];
        
        return $this->view('admin/dashboard/index.twig', $data);
    }

    /**
     * Get dashboard statistics
     */
    private function getDashboardStats()
    {
        $db = app()->getDatabase();
        
        $stats = [];
        
        try {
            // Users count
            $userCount = $db->selectOne("SELECT COUNT(*) as count FROM users WHERE deleted_at IS NULL");
            $stats['users'] = $userCount['count'] ?? 0;
            
            // Content count (will be implemented later)
            $stats['content'] = 0;
            
            // Pages count (will be implemented later)
            $stats['pages'] = 0;
            
            // Blog posts count (will be implemented later)
            $stats['blog_posts'] = 0;
            
        } catch (\Exception $e) {
            // If tables don't exist yet, return default stats
            $stats = [
                'users' => 0,
                'content' => 0,
                'pages' => 0,
                'blog_posts' => 0,
            ];
        }
        
        return $stats;
    }

    /**
     * Get recent activity
     */
    private function getRecentActivity()
    {
        $db = app()->getDatabase();
        
        try {
            $activities = $db->select(
                "SELECT ual.*, u.first_name, u.last_name, u.username 
                 FROM user_activity_log ual 
                 LEFT JOIN users u ON ual.user_id = u.id 
                 ORDER BY ual.created_at DESC 
                 LIMIT 10"
            );
            
            return $activities;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get system information
     */
    private function getSystemInfo()
    {
        return [
            'php_version' => PHP_VERSION,
            'cms_version' => '1.0.0',
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'database_type' => 'MySQL',
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
        ];
    }
}
