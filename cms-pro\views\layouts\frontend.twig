<!DOCTYPE html>
<html lang="{{ site_settings.language ?? 'en' }}" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- SEO Meta Tags -->
    <title>{{ seo.title ?? site_settings.site_name }}</title>
    <meta name="description" content="{{ seo.description ?? site_settings.site_description }}">
    {% if seo.keywords %}
    <meta name="keywords" content="{{ seo.keywords }}">
    {% endif %}
    <link rel="canonical" href="{{ seo.canonical_url ?? site_settings.site_url }}">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{{ seo.og_title ?? site_settings.site_name }}">
    <meta property="og:description" content="{{ seo.og_description ?? site_settings.site_description }}">
    <meta property="og:url" content="{{ seo.og_url ?? site_settings.site_url }}">
    <meta property="og:type" content="{{ seo.og_type ?? 'website' }}">
    <meta property="og:site_name" content="{{ seo.og_site_name ?? site_settings.site_name }}">
    {% if seo.og_image %}
    <meta property="og:image" content="{{ seo.og_image }}">
    {% endif %}
    {% if seo.article_published_time %}
    <meta property="article:published_time" content="{{ seo.article_published_time }}">
    {% endif %}
    {% if seo.article_modified_time %}
    <meta property="article:modified_time" content="{{ seo.article_modified_time }}">
    {% endif %}
    {% if seo.article_author %}
    <meta property="article:author" content="{{ seo.article_author }}">
    {% endif %}

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="{{ seo.twitter_card ?? 'summary_large_image' }}">
    <meta name="twitter:title" content="{{ seo.twitter_title ?? site_settings.site_name }}">
    <meta name="twitter:description" content="{{ seo.twitter_description ?? site_settings.site_description }}">
    {% if seo.twitter_image %}
    <meta name="twitter:image" content="{{ seo.twitter_image }}">
    {% endif %}

    <!-- Favicon -->
    {% if site_settings.site_favicon %}
    <link rel="icon" type="image/x-icon" href="{{ site_settings.site_favicon }}">
    {% endif %}
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    {% block styles %}
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
        }

        .navbar-brand {
            font-weight: bold;
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            padding: 4rem 0;
        }

        .hero-image {
            width: 100%;
            height: 400px;
            object-fit: cover;
            border-radius: 0.5rem;
        }

        .intro-section {
            padding: 3rem 0;
        }

        .intro-content {
            font-size: 1.2rem;
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .page-header {
            text-align: center;
            padding: 2rem 0;
            border-bottom: 1px solid #e9ecef;
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }

        .page-excerpt {
            font-size: 1.2rem;
            color: var(--secondary-color);
            max-width: 600px;
            margin: 0 auto 2rem;
        }

        .page-featured-image {
            max-width: 800px;
            margin: 0 auto;
        }

        .page-featured-image img {
            width: 100%;
            height: 400px;
            object-fit: cover;
            border-radius: 0.5rem;
        }

        .field {
            margin-bottom: 1.5rem;
        }

        .field-label {
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: block;
        }

        .field-description {
            font-size: 0.9rem;
            color: var(--secondary-color);
            margin-bottom: 0.5rem;
        }

        .field-value {
            line-height: 1.6;
        }

        .field-empty {
            color: var(--secondary-color);
            font-style: italic;
        }

        .required {
            color: var(--danger-color);
        }

        .wysiwyg-content h1,
        .wysiwyg-content h2,
        .wysiwyg-content h3,
        .wysiwyg-content h4,
        .wysiwyg-content h5,
        .wysiwyg-content h6 {
            margin-top: 2rem;
            margin-bottom: 1rem;
        }

        .wysiwyg-content p {
            margin-bottom: 1rem;
        }

        .wysiwyg-content img {
            max-width: 100%;
            height: auto;
            border-radius: 0.25rem;
        }

        .embed-responsive {
            position: relative;
            display: block;
            width: 100%;
            padding: 0;
            overflow: hidden;
        }

        .embed-responsive-16by9 {
            padding-bottom: 56.25%;
        }

        .embed-responsive-item {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 0;
        }

        .content-block {
            margin-bottom: 3rem;
        }

        .cta-block {
            border-radius: 0.5rem;
            margin: 3rem 0;
        }

        .cta-block.text-center {
            text-align: center;
        }

        .cta-heading {
            color: white;
        }

        .cta-description {
            color: rgba(255, 255, 255, 0.9);
        }

        .footer {
            background-color: var(--dark-color);
            color: white;
            padding: 3rem 0 1rem;
            margin-top: 4rem;
        }

        .footer a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }

        .footer a:hover {
            color: white;
        }

        @media (max-width: 768px) {
            .page-title {
                font-size: 2rem;
            }

            .hero-section {
                padding: 2rem 0;
            }

            .intro-content {
                font-size: 1.1rem;
            }
        }
    </style>
    {% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url('/') }}">{{ app.name }}</a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url('/') }}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url('/about') }}">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url('/blog') }}">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url('/contact') }}">Contact</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    {% if user %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                            {{ user.getFullName() }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url('/admin') }}">Admin Panel</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url('/logout') }}">Logout</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url('/login') }}">Login</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>{{ app.name }}</h5>
                    <p>{{ app.description|default('A powerful CMS built with PHP and Twig.') }}</p>
                </div>
                <div class="col-md-3">
                    <h6>Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="{{ url('/') }}">Home</a></li>
                        <li><a href="{{ url('/about') }}">About</a></li>
                        <li><a href="{{ url('/blog') }}">Blog</a></li>
                        <li><a href="{{ url('/contact') }}">Contact</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h6>Admin</h6>
                    <ul class="list-unstyled">
                        <li><a href="{{ url('/admin') }}">Admin Panel</a></li>
                        <li><a href="{{ url('/login') }}">Login</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; {{ "now"|date("Y") }} {{ app.name }}. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">Powered by CMS Pro</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
