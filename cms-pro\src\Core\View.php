<?php

namespace CmsPro\Core;

use Twig\Environment;
use Twig\Loader\FilesystemLoader;
use Twig\Extension\DebugExtension;
use Twig\TwigFunction;
use Twig\TwigFilter;

/**
 * View Engine using Twig
 * 
 * @package CmsPro\Core
 */
class View
{
    private $twig;
    private $config;

    public function __construct(Config $config)
    {
        $this->config = $config;
        $this->initializeTwig();
    }

    /**
     * Initialize Twig environment
     */
    private function initializeTwig()
    {
        $viewPaths = [
            ROOT_PATH . '/views',
            ROOT_PATH . '/views/admin',
            ROOT_PATH . '/views/frontend',
            ROOT_PATH . '/views/layouts',
        ];

        $loader = new FilesystemLoader($viewPaths);
        
        $this->twig = new Environment($loader, [
            'cache' => $this->config->get('app.debug') ? false : storage_path('cache/views'),
            'debug' => $this->config->get('app.debug', false),
            'auto_reload' => $this->config->get('app.debug', false),
            'strict_variables' => false,
        ]);

        if ($this->config->get('app.debug')) {
            $this->twig->addExtension(new DebugExtension());
        }

        $this->addCustomFunctions();
        $this->addCustomFilters();
    }

    /**
     * Add custom Twig functions
     */
    private function addCustomFunctions()
    {
        // URL functions
        $this->twig->addFunction(new TwigFunction('url', function($path = '') {
            return url($path);
        }));

        $this->twig->addFunction(new TwigFunction('asset', function($path) {
            return asset($path);
        }));

        $this->twig->addFunction(new TwigFunction('route', function($name, $parameters = []) {
            return route($name, $parameters);
        }));

        // Auth functions
        $this->twig->addFunction(new TwigFunction('auth', function() {
            return auth();
        }));

        $this->twig->addFunction(new TwigFunction('user', function() {
            return user();
        }));

        // Session functions
        $this->twig->addFunction(new TwigFunction('session', function($key = null, $default = null) {
            return session($key, $default);
        }));

        $this->twig->addFunction(new TwigFunction('old', function($key, $default = null) {
            return old($key, $default);
        }));

        // CSRF functions
        $this->twig->addFunction(new TwigFunction('csrf_token', function() {
            return csrf_token();
        }));

        $this->twig->addFunction(new TwigFunction('csrf_field', function() {
            return csrf_field();
        }, ['is_safe' => ['html']]));

        // Config function
        $this->twig->addFunction(new TwigFunction('config', function($key, $default = null) {
            return config($key, $default);
        }));

        // Translation functions
        $this->twig->addFunction(new TwigFunction('trans', function($key, $replace = [], $locale = null) {
            return trans($key, $replace, $locale);
        }));

        $this->twig->addFunction(new TwigFunction('__', function($key, $replace = [], $locale = null) {
            return __($key, $replace, $locale);
        }));

        // Custom field function for dynamic fields
        $this->twig->addFunction(new TwigFunction('field', function($name, $context = null) {
            return $this->getCustomField($name, $context);
        }));

        // Include dynamic content
        $this->twig->addFunction(new TwigFunction('dynamic_content', function($type, $id = null) {
            return $this->getDynamicContent($type, $id);
        }));
    }

    /**
     * Add custom Twig filters
     */
    private function addCustomFilters()
    {
        // Sanitize filter
        $this->twig->addFilter(new TwigFilter('sanitize', function($string) {
            return sanitize($string);
        }));

        // Slug filter
        $this->twig->addFilter(new TwigFilter('slug', function($string) {
            return slug($string);
        }));

        // Truncate filter
        $this->twig->addFilter(new TwigFilter('truncate', function($string, $length = 100, $suffix = '...') {
            if (strlen($string) <= $length) {
                return $string;
            }
            return substr($string, 0, $length) . $suffix;
        }));

        // Date format filter
        $this->twig->addFilter(new TwigFilter('date_format', function($date, $format = 'Y-m-d H:i:s') {
            if ($date instanceof \DateTime) {
                return $date->format($format);
            }
            return date($format, strtotime($date));
        }));

        // Money format filter
        $this->twig->addFilter(new TwigFilter('money', function($amount, $currency = 'TRY') {
            return number_format($amount, 2) . ' ' . $currency;
        }));
    }

    /**
     * Render template
     */
    public function render($template, $data = [])
    {
        // Add global variables
        $data = array_merge($data, $this->getGlobalVariables());
        
        return $this->twig->render($template, $data);
    }

    /**
     * Get global template variables
     */
    private function getGlobalVariables()
    {
        return [
            'app' => [
                'name' => config('app.name'),
                'url' => config('app.url'),
                'debug' => config('app.debug'),
                'locale' => config('app.locale'),
            ],
            'auth' => auth(),
            'user' => user(),
            'session' => session(),
            'csrf_token' => csrf_token(),
        ];
    }

    /**
     * Get custom field value
     */
    private function getCustomField($name, $context = null)
    {
        // This will be implemented when we create the dynamic field system
        // For now, return empty string
        return '';
    }

    /**
     * Get dynamic content
     */
    private function getDynamicContent($type, $id = null)
    {
        // This will be implemented when we create the content management system
        // For now, return empty string
        return '';
    }

    /**
     * Add global variable
     */
    public function addGlobal($name, $value)
    {
        $this->twig->addGlobal($name, $value);
    }

    /**
     * Get Twig environment
     */
    public function getTwig()
    {
        return $this->twig;
    }
}
