# CMS Pro Environment Configuration

# Application
APP_NAME="CMS Pro"
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost:8000
APP_TIMEZONE=Europe/Istanbul

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=cms_pro
DB_USERNAME=root
DB_PASSWORD=

# Security
APP_KEY=
JWT_SECRET=
CSRF_TOKEN_NAME=_token

# Mail
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="CMS Pro"

# File Storage
FILESYSTEM_DRIVER=local
UPLOAD_MAX_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,txt

# Cache
CACHE_DRIVER=file
CACHE_PREFIX=cms_pro

# Session
SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false

# Logging
LOG_CHANNEL=single
LOG_LEVEL=debug

# Third Party Services
GOOGLE_ANALYTICS_ID=
GOOGLE_TAG_MANAGER_ID=

# Two Factor Authentication
2FA_ENABLED=true
2FA_ISSUER="CMS Pro"

# API Rate Limiting
API_RATE_LIMIT=60
API_RATE_LIMIT_WINDOW=60

# Backup
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
