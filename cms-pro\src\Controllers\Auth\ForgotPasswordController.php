<?php

namespace CmsPro\Controllers\Auth;

use CmsPro\Controllers\BaseController;
use CmsPro\Models\User;
use CmsPro\Services\MailService;
use Symfony\Component\HttpFoundation\Request;

/**
 * Forgot Password Controller
 * 
 * @package CmsPro\Controllers\Auth
 */
class ForgotPasswordController extends BaseController
{
    private $mailService;

    public function __construct()
    {
        parent::__construct();
        $this->mailService = new MailService();
    }

    /**
     * Show forgot password form
     */
    public function showLinkRequestForm(Request $request)
    {
        $this->request = $request;

        // Redirect if already authenticated
        if (auth()->check()) {
            return $this->redirectToRoute('home');
        }

        $data = [
            'title' => __('Reset Password'),
            'meta_description' => __('Reset your password'),
        ];

        return $this->view('auth/passwords/email.twig', $data);
    }

    /**
     * Send password reset link
     */
    public function sendResetLinkEmail(Request $request)
    {
        $this->request = $request;

        // Redirect if already authenticated
        if (auth()->check()) {
            return $this->redirectToRoute('home');
        }

        $email = $request->request->get('email');

        if (!$email) {
            $this->flashError(__('Please enter your email address.'));
            return $this->back();
        }

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->flashError(__('Please enter a valid email address.'));
            return $this->back();
        }

        $user = User::findByEmail($email);

        if (!$user) {
            // Don't reveal if email exists or not for security
            $this->flashSuccess(__('If your email address exists in our database, you will receive a password recovery link at your email address in a few minutes.'));
            return $this->back();
        }

        if ($user->getStatus() !== 'active') {
            $this->flashError(__('Your account is not active. Please contact support.'));
            return $this->back();
        }

        try {
            // Generate reset token
            $token = $this->generateResetToken();
            $expiresAt = date('Y-m-d H:i:s', strtotime('+1 hour'));

            // Save token to user
            $user->update([
                'password_reset_token' => $token,
                'password_reset_expires' => $expiresAt
            ]);

            // Send email
            $this->sendResetEmail($user, $token);

            $this->flashSuccess(__('If your email address exists in our database, you will receive a password recovery link at your email address in a few minutes.'));

        } catch (\Exception $e) {
            $this->flashError(__('An error occurred while sending the reset email. Please try again.'));
        }

        return $this->back();
    }

    /**
     * Generate secure reset token
     */
    private function generateResetToken()
    {
        return bin2hex(random_bytes(32));
    }

    /**
     * Send password reset email
     */
    private function sendResetEmail(User $user, $token)
    {
        $resetUrl = url('/password/reset/' . $token);
        
        $subject = __('Reset Your Password - :app', ['app' => config('app.name')]);
        
        $body = view('emails/password-reset.twig', [
            'user' => $user,
            'reset_url' => $resetUrl,
            'expires_in' => '1 hour'
        ]);

        $this->mailService->send(
            $user->getEmail(),
            $user->getFullName(),
            $subject,
            $body
        );
    }
}
