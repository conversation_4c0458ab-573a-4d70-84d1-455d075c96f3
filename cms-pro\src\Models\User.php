<?php

namespace CmsPro\Models;

use CmsPro\Core\Database;
// Role model will be loaded when needed
use Ramsey\Uuid\Uuid;

/**
 * User Model
 * 
 * @package CmsPro\Models
 */
class User
{
    private $db;
    private $data = [];

    public function __construct(Database $db = null)
    {
        $this->db = $db ?: app()->getDatabase();
    }

    /**
     * Find user by ID
     */
    public static function find($id)
    {
        $instance = new static();
        $userData = $instance->db->selectOne(
            "SELECT * FROM users WHERE id = ? AND deleted_at IS NULL",
            [$id]
        );

        if ($userData) {
            $instance->data = $userData;
            return $instance;
        }

        return null;
    }

    /**
     * Find user by email
     */
    public static function findByEmail($email)
    {
        $instance = new static();
        $userData = $instance->db->selectOne(
            "SELECT * FROM users WHERE email = ? AND deleted_at IS NULL",
            [$email]
        );

        if ($userData) {
            $instance->data = $userData;
            return $instance;
        }

        return null;
    }

    /**
     * Find user by username
     */
    public static function findByUsername($username)
    {
        $instance = new static();
        $userData = $instance->db->selectOne(
            "SELECT * FROM users WHERE username = ? AND deleted_at IS NULL",
            [$username]
        );

        if ($userData) {
            $instance->data = $userData;
            return $instance;
        }

        return null;
    }

    /**
     * Find user by remember token
     */
    public static function findByRememberToken($token)
    {
        $instance = new static();
        $userData = $instance->db->selectOne(
            "SELECT * FROM users WHERE remember_token = ? AND deleted_at IS NULL",
            [$token]
        );

        if ($userData) {
            $instance->data = $userData;
            return $instance;
        }

        return null;
    }

    /**
     * Create new user
     */
    public static function create($data)
    {
        $instance = new static();
        
        // Generate UUID
        $data['uuid'] = Uuid::uuid4()->toString();
        
        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }

        // Set default role if not provided
        if (!isset($data['role_id'])) {
            $defaultRole = Role::getDefault();
            $data['role_id'] = $defaultRole ? $defaultRole->getId() : 3; // Default to Editor
        }

        $instance->db->insert('users', $data);
        $userId = $instance->db->lastInsertId();

        return static::find($userId);
    }

    /**
     * Update user
     */
    public function update($data)
    {
        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }

        $this->db->update('users', $data, 'id = ?', [$this->getId()]);
        
        // Refresh data
        $this->data = array_merge($this->data, $data);
        
        return $this;
    }

    /**
     * Delete user (soft delete)
     */
    public function delete()
    {
        $this->db->update('users', ['deleted_at' => date('Y-m-d H:i:s')], 'id = ?', [$this->getId()]);
        return true;
    }

    /**
     * Verify password
     */
    public function verifyPassword($password)
    {
        return password_verify($password, $this->data['password']);
    }

    /**
     * Generate remember token
     */
    public function generateRememberToken()
    {
        $token = bin2hex(random_bytes(32));
        $this->update(['remember_token' => $token]);
        return $token;
    }

    /**
     * Clear remember token
     */
    public function clearRememberToken()
    {
        $this->update(['remember_token' => null]);
    }

    /**
     * Update last login
     */
    public function updateLastLogin($ipAddress = null)
    {
        $this->update([
            'last_login_at' => date('Y-m-d H:i:s'),
            'last_login_ip' => $ipAddress,
            'login_attempts' => 0,
            'locked_until' => null
        ]);
    }

    /**
     * Increment login attempts
     */
    public function incrementLoginAttempts()
    {
        $attempts = $this->getLoginAttempts() + 1;
        $data = ['login_attempts' => $attempts];
        
        // Lock account after 5 failed attempts for 30 minutes
        if ($attempts >= 5) {
            $data['locked_until'] = date('Y-m-d H:i:s', strtotime('+30 minutes'));
        }
        
        $this->update($data);
    }

    /**
     * Check if account is locked
     */
    public function isLocked()
    {
        $lockedUntil = $this->data['locked_until'] ?? null;
        
        if (!$lockedUntil) {
            return false;
        }
        
        return strtotime($lockedUntil) > time();
    }

    /**
     * Get user role
     */
    public function getRole()
    {
        return Role::find($this->getRoleId());
    }

    /**
     * Check if user has permission
     */
    public function hasPermission($permission)
    {
        $role = $this->getRole();
        return $role ? $role->hasPermission($permission) : false;
    }

    /**
     * Check if user has role
     */
    public function hasRole($roleSlug)
    {
        $role = $this->getRole();
        return $role ? $role->getSlug() === $roleSlug : false;
    }

    /**
     * Get full name
     */
    public function getFullName()
    {
        return trim($this->getFirstName() . ' ' . $this->getLastName());
    }

    /**
     * Get avatar URL
     */
    public function getAvatarUrl()
    {
        if ($this->data['avatar']) {
            return asset('uploads/avatars/' . $this->data['avatar']);
        }
        
        // Generate Gravatar URL
        $hash = md5(strtolower(trim($this->getEmail())));
        return "https://www.gravatar.com/avatar/{$hash}?d=identicon&s=150";
    }

    /**
     * Check if email is verified
     */
    public function isEmailVerified()
    {
        return !empty($this->data['email_verified_at']);
    }

    /**
     * Check if 2FA is enabled
     */
    public function isTwoFactorEnabled()
    {
        return (bool) $this->data['two_factor_enabled'];
    }

    /**
     * Get user preferences
     */
    public function getPreferences()
    {
        $preferences = $this->data['preferences'] ?? '{}';
        return json_decode($preferences, true) ?: [];
    }

    /**
     * Set user preference
     */
    public function setPreference($key, $value)
    {
        $preferences = $this->getPreferences();
        $preferences[$key] = $value;
        $this->update(['preferences' => json_encode($preferences)]);
    }

    /**
     * Get user preference
     */
    public function getPreference($key, $default = null)
    {
        $preferences = $this->getPreferences();
        return $preferences[$key] ?? $default;
    }

    // Getters
    public function getId() { return $this->data['id'] ?? null; }
    public function getUuid() { return $this->data['uuid'] ?? null; }
    public function getUsername() { return $this->data['username'] ?? null; }
    public function getEmail() { return $this->data['email'] ?? null; }
    public function getFirstName() { return $this->data['first_name'] ?? null; }
    public function getLastName() { return $this->data['last_name'] ?? null; }
    public function getPhone() { return $this->data['phone'] ?? null; }
    public function getBio() { return $this->data['bio'] ?? null; }
    public function getStatus() { return $this->data['status'] ?? null; }
    public function getRoleId() { return $this->data['role_id'] ?? null; }
    public function getLastLoginAt() { return $this->data['last_login_at'] ?? null; }
    public function getLastLoginIp() { return $this->data['last_login_ip'] ?? null; }
    public function getLoginAttempts() { return $this->data['login_attempts'] ?? 0; }
    public function getCreatedAt() { return $this->data['created_at'] ?? null; }
    public function getUpdatedAt() { return $this->data['updated_at'] ?? null; }

    /**
     * Convert to array
     */
    public function toArray()
    {
        $data = $this->data;
        unset($data['password']); // Never expose password
        unset($data['remember_token']); // Never expose remember token
        unset($data['two_factor_secret']); // Never expose 2FA secret
        return $data;
    }

    /**
     * Magic getter
     */
    public function __get($key)
    {
        return $this->data[$key] ?? null;
    }

    /**
     * Magic isset
     */
    public function __isset($key)
    {
        return isset($this->data[$key]);
    }
}
