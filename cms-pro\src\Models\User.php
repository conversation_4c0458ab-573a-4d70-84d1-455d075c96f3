<?php

namespace CmsPro\Models;

use CmsPro\Core\Database;
// Role model will be loaded when needed
use Ramsey\Uuid\Uuid;

/**
 * User Model
 * 
 * @package CmsPro\Models
 */
class User
{
    private $db;
    private $data = [];

    public function __construct(Database $db = null)
    {
        $this->db = $db ?: app()->getDatabase();
    }

    /**
     * Find user by ID
     */
    public static function find($id)
    {
        $instance = new static();
        $userData = $instance->db->selectOne(
            "SELECT * FROM users WHERE id = ? AND deleted_at IS NULL",
            [$id]
        );

        if ($userData) {
            $instance->data = $userData;
            return $instance;
        }

        return null;
    }

    /**
     * Find user by email
     */
    public static function findByEmail($email)
    {
        $instance = new static();
        $userData = $instance->db->selectOne(
            "SELECT * FROM users WHERE email = ? AND deleted_at IS NULL",
            [$email]
        );

        if ($userData) {
            $instance->data = $userData;
            return $instance;
        }

        return null;
    }

    /**
     * Find user by username
     */
    public static function findByUsername($username)
    {
        $instance = new static();
        $userData = $instance->db->selectOne(
            "SELECT * FROM users WHERE username = ? AND deleted_at IS NULL",
            [$username]
        );

        if ($userData) {
            $instance->data = $userData;
            return $instance;
        }

        return null;
    }

    /**
     * Find user by remember token
     */
    public static function findByRememberToken($token)
    {
        $instance = new static();
        $userData = $instance->db->selectOne(
            "SELECT * FROM users WHERE remember_token = ? AND deleted_at IS NULL",
            [$token]
        );

        if ($userData) {
            $instance->data = $userData;
            return $instance;
        }

        return null;
    }

    /**
     * Create new user
     */
    public static function create($data)
    {
        $instance = new static();
        
        // Generate UUID
        $data['uuid'] = Uuid::uuid4()->toString();
        
        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }

        // Set default role if not provided
        if (!isset($data['role_id'])) {
            $defaultRole = Role::getDefault();
            $data['role_id'] = $defaultRole ? $defaultRole->getId() : 3; // Default to Editor
        }

        $instance->db->insert('users', $data);
        $userId = $instance->db->lastInsertId();

        return static::find($userId);
    }

    /**
     * Update user
     */
    public function update($data)
    {
        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }

        $this->db->update('users', $data, 'id = ?', [$this->getId()]);
        
        // Refresh data
        $this->data = array_merge($this->data, $data);
        
        return $this;
    }

    /**
     * Delete user (soft delete)
     */
    public function delete()
    {
        $this->db->update('users', ['deleted_at' => date('Y-m-d H:i:s')], 'id = ?', [$this->getId()]);
        return true;
    }

    /**
     * Verify password
     */
    public function verifyPassword($password)
    {
        return password_verify($password, $this->data['password']);
    }

    /**
     * Generate remember token
     */
    public function generateRememberToken()
    {
        $token = bin2hex(random_bytes(32));
        $this->update(['remember_token' => $token]);
        return $token;
    }

    /**
     * Clear remember token
     */
    public function clearRememberToken()
    {
        $this->update(['remember_token' => null]);
    }

    /**
     * Update last login
     */
    public function updateLastLogin($ipAddress = null)
    {
        $this->update([
            'last_login_at' => date('Y-m-d H:i:s'),
            'last_login_ip' => $ipAddress,
            'login_attempts' => 0,
            'locked_until' => null
        ]);
    }

    /**
     * Increment login attempts
     */
    public function incrementLoginAttempts()
    {
        $attempts = $this->getLoginAttempts() + 1;
        $data = ['login_attempts' => $attempts];
        
        // Lock account after 5 failed attempts for 30 minutes
        if ($attempts >= 5) {
            $data['locked_until'] = date('Y-m-d H:i:s', strtotime('+30 minutes'));
        }
        
        $this->update($data);
    }

    /**
     * Check if account is locked
     */
    public function isLocked()
    {
        $lockedUntil = $this->data['locked_until'] ?? null;
        
        if (!$lockedUntil) {
            return false;
        }
        
        return strtotime($lockedUntil) > time();
    }

    /**
     * Get user role
     */
    public function getRole()
    {
        return Role::find($this->getRoleId());
    }

    /**
     * Check if user has permission
     */
    public function hasPermission($permission)
    {
        $role = $this->getRole();
        return $role ? $role->hasPermission($permission) : false;
    }

    /**
     * Check if user has role
     */
    public function hasRole($roleSlug)
    {
        $role = $this->getRole();
        return $role ? $role->getSlug() === $roleSlug : false;
    }

    /**
     * Get full name
     */
    public function getFullName()
    {
        return trim($this->getFirstName() . ' ' . $this->getLastName());
    }

    /**
     * Get avatar URL
     */
    public function getAvatarUrl()
    {
        if ($this->data['avatar']) {
            return asset('uploads/avatars/' . $this->data['avatar']);
        }
        
        // Generate Gravatar URL
        $hash = md5(strtolower(trim($this->getEmail())));
        return "https://www.gravatar.com/avatar/{$hash}?d=identicon&s=150";
    }

    /**
     * Check if email is verified
     */
    public function isEmailVerified()
    {
        return !empty($this->data['email_verified_at']);
    }

    /**
     * Check if 2FA is enabled
     */
    public function isTwoFactorEnabled()
    {
        return (bool) $this->data['two_factor_enabled'];
    }

    /**
     * Get user preferences
     */
    public function getPreferences()
    {
        $preferences = $this->data['preferences'] ?? '{}';
        return json_decode($preferences, true) ?: [];
    }

    /**
     * Set user preference
     */
    public function setPreference($key, $value)
    {
        $preferences = $this->getPreferences();
        $preferences[$key] = $value;
        $this->update(['preferences' => json_encode($preferences)]);
    }

    /**
     * Get user preference
     */
    public function getPreference($key, $default = null)
    {
        $preferences = $this->getPreferences();
        return $preferences[$key] ?? $default;
    }

    // Getters
    public function getId() { return $this->data['id'] ?? null; }
    public function getUuid() { return $this->data['uuid'] ?? null; }
    public function getUsername() { return $this->data['username'] ?? null; }
    public function getEmail() { return $this->data['email'] ?? null; }
    public function getFirstName() { return $this->data['first_name'] ?? null; }
    public function getLastName() { return $this->data['last_name'] ?? null; }
    public function getPhone() { return $this->data['phone'] ?? null; }
    public function getBio() { return $this->data['bio'] ?? null; }
    public function getStatus() { return $this->data['status'] ?? null; }
    public function getRoleId() { return $this->data['role_id'] ?? null; }
    public function getLastLoginAt() { return $this->data['last_login_at'] ?? null; }
    public function getLastLoginIp() { return $this->data['last_login_ip'] ?? null; }
    public function getLoginAttempts() { return $this->data['login_attempts'] ?? 0; }
    public function getCreatedAt() { return $this->data['created_at'] ?? null; }
    public function getUpdatedAt() { return $this->data['updated_at'] ?? null; }

    /**
     * Convert to array
     */
    public function toArray()
    {
        $data = $this->data;
        unset($data['password']); // Never expose password
        unset($data['remember_token']); // Never expose remember token
        unset($data['two_factor_secret']); // Never expose 2FA secret
        return $data;
    }

    /**
     * Magic getter
     */
    public function __get($key)
    {
        return $this->data[$key] ?? null;
    }

    /**
     * Magic isset
     */
    public function __isset($key)
    {
        return isset($this->data[$key]);
    }

    /**
     * Get user roles
     */
    public function getRoles()
    {
        if (!$this->id) return [];

        $roles = $this->db->select(
            "SELECT r.* FROM roles r
             INNER JOIN user_roles ur ON r.id = ur.role_id
             WHERE ur.user_id = ? AND r.status = 'active'",
            [$this->id]
        );

        return $roles;
    }

    /**
     * Get user permissions
     */
    public function getPermissions()
    {
        if (!$this->id) return [];

        $permissions = $this->db->select(
            "SELECT DISTINCT p.* FROM permissions p
             INNER JOIN role_permissions rp ON p.id = rp.permission_id
             INNER JOIN user_roles ur ON rp.role_id = ur.role_id
             WHERE ur.user_id = ? AND p.status = 'active'
             UNION
             SELECT p.* FROM permissions p
             INNER JOIN user_permissions up ON p.id = up.permission_id
             WHERE up.user_id = ? AND p.status = 'active'",
            [$this->id, $this->id]
        );

        return $permissions;
    }

    /**
     * Check if user has role
     */
    public function hasRole($roleName)
    {
        $roles = $this->getRoles();
        foreach ($roles as $role) {
            if ($role['name'] === $roleName || $role['slug'] === $roleName) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if user has permission
     */
    public function hasPermission($permissionName)
    {
        $permissions = $this->getPermissions();
        foreach ($permissions as $permission) {
            if ($permission['name'] === $permissionName || $permission['slug'] === $permissionName) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if user can perform action
     */
    public function can($permission)
    {
        return $this->hasPermission($permission);
    }

    /**
     * Check if user cannot perform action
     */
    public function cannot($permission)
    {
        return !$this->can($permission);
    }

    /**
     * Assign role to user
     */
    public function assignRole($roleId)
    {
        if (!$this->id) return false;

        // Check if role already assigned
        $existing = $this->db->selectOne(
            "SELECT id FROM user_roles WHERE user_id = ? AND role_id = ?",
            [$this->id, $roleId]
        );

        if ($existing) return true;

        return $this->db->insert('user_roles', [
            'user_id' => $this->id,
            'role_id' => $roleId,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Remove role from user
     */
    public function removeRole($roleId)
    {
        if (!$this->id) return false;

        return $this->db->delete(
            "DELETE FROM user_roles WHERE user_id = ? AND role_id = ?",
            [$this->id, $roleId]
        );
    }

    /**
     * Assign permission to user
     */
    public function assignPermission($permissionId)
    {
        if (!$this->id) return false;

        // Check if permission already assigned
        $existing = $this->db->selectOne(
            "SELECT id FROM user_permissions WHERE user_id = ? AND permission_id = ?",
            [$this->id, $permissionId]
        );

        if ($existing) return true;

        return $this->db->insert('user_permissions', [
            'user_id' => $this->id,
            'permission_id' => $permissionId,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Remove permission from user
     */
    public function removePermission($permissionId)
    {
        if (!$this->id) return false;

        return $this->db->delete(
            "DELETE FROM user_permissions WHERE user_id = ? AND permission_id = ?",
            [$this->id, $permissionId]
        );
    }

    /**
     * Get user's full name
     */
    public function getFullName()
    {
        return trim(($this->first_name ?? '') . ' ' . ($this->last_name ?? ''));
    }

    /**
     * Get user's initials
     */
    public function getInitials()
    {
        $firstName = $this->first_name ?? '';
        $lastName = $this->last_name ?? '';

        $initials = '';
        if ($firstName) $initials .= strtoupper($firstName[0]);
        if ($lastName) $initials .= strtoupper($lastName[0]);

        return $initials ?: strtoupper(($this->email ?? 'U')[0]);
    }

    /**
     * Check if user is admin
     */
    public function isAdmin()
    {
        return $this->hasRole('admin') || $this->hasRole('super_admin');
    }

    /**
     * Check if user is super admin
     */
    public function isSuperAdmin()
    {
        return $this->hasRole('super_admin');
    }

    /**
     * Get user avatar URL
     */
    public function getAvatarUrl()
    {
        if ($this->avatar) {
            return asset($this->avatar);
        }

        // Generate Gravatar URL
        $email = strtolower(trim($this->email ?? ''));
        $hash = md5($email);
        return "https://www.gravatar.com/avatar/{$hash}?d=identicon&s=150";
    }

    /**
     * Check if user is active
     */
    public function isActive()
    {
        return $this->status === 'active';
    }

    /**
     * Check if user is banned
     */
    public function isBanned()
    {
        return $this->status === 'banned';
    }

    /**
     * Activate user
     */
    public function activate()
    {
        return $this->update(['status' => 'active']);
    }

    /**
     * Deactivate user
     */
    public function deactivate()
    {
        return $this->update(['status' => 'inactive']);
    }

    /**
     * Ban user
     */
    public function ban()
    {
        return $this->update(['status' => 'banned']);
    }

    /**
     * Get user activity log
     */
    public function getActivityLog($limit = 50)
    {
        if (!$this->id) return [];

        return $this->db->select(
            "SELECT * FROM user_activity_log
             WHERE user_id = ?
             ORDER BY created_at DESC
             LIMIT ?",
            [$this->id, $limit]
        );
    }

    /**
     * Log user activity
     */
    public function logActivity($action, $data = [])
    {
        if (!$this->id) return false;

        return $this->db->insert('user_activity_log', [
            'user_id' => $this->id,
            'action' => $action,
            'data' => json_encode($data),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Get user sessions
     */
    public function getSessions()
    {
        if (!$this->id) return [];

        return $this->db->select(
            "SELECT * FROM user_sessions
             WHERE user_id = ?
             ORDER BY last_activity DESC",
            [$this->id]
        );
    }

    /**
     * Revoke all user sessions except current
     */
    public function revokeOtherSessions($currentSessionId = null)
    {
        if (!$this->id) return false;

        $query = "DELETE FROM user_sessions WHERE user_id = ?";
        $params = [$this->id];

        if ($currentSessionId) {
            $query .= " AND id != ?";
            $params[] = $currentSessionId;
        }

        return $this->db->delete($query, $params);
    }

    // Status constants
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_BANNED = 'banned';
    const STATUS_DELETED = 'deleted';

    /**
     * Get all available statuses
     */
    public static function getStatuses()
    {
        return [
            self::STATUS_ACTIVE => __('Active'),
            self::STATUS_INACTIVE => __('Inactive'),
            self::STATUS_BANNED => __('Banned'),
            self::STATUS_DELETED => __('Deleted')
        ];
    }

    /**
     * Create new user
     */
    public static function create($data)
    {
        $instance = new static();

        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');

        $id = $instance->db->insert('users', $data);

        if ($id) {
            $instance->data = array_merge($data, ['id' => $id]);
            return $instance;
        }

        return false;
    }
}
