{"name": "symfony/validator", "type": "library", "description": "Provides tools to validate values", "keywords": [], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^1.1|^2"}, "require-dev": {"symfony/http-client": "^4.3|^5.0", "symfony/http-foundation": "^4.1|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^4.3|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/cache": "^3.4|^4.0|^5.0", "symfony/mime": "^4.4|^5.0", "symfony/property-access": "^3.4|^4.0|^5.0", "symfony/property-info": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2", "doctrine/annotations": "^1.10.4", "doctrine/cache": "^1.0|^2.0", "egulias/email-validator": "^2.1.10|^3"}, "conflict": {"doctrine/lexer": "<1.1", "phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/intl": "<4.3", "symfony/translation": ">=5.0", "symfony/yaml": "<3.4"}, "suggest": {"psr/cache-implementation": "For using the mapping cache.", "doctrine/annotations": "For using the annotation mapping. You will also need doctrine/cache.", "doctrine/cache": "For using the default cached annotation reader.", "symfony/http-foundation": "", "symfony/intl": "", "symfony/translation": "For translating validation errors.", "symfony/yaml": "", "symfony/config": "", "egulias/email-validator": "Strict (RFC compliant) email validation", "symfony/property-access": "For accessing properties within comparison constraints", "symfony/property-info": "To automatically add NotNull and Type constraints", "symfony/expression-language": "For using the Expression validator"}, "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "minimum-stability": "dev"}