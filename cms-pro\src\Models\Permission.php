<?php

namespace CmsPro\Models;

use CmsPro\Core\Database;

/**
 * Permission Model
 * 
 * @package CmsPro\Models
 */
class Permission
{
    private $db;
    private $data = [];

    public function __construct(Database $db = null)
    {
        $this->db = $db ?: app()->getDatabase();
    }

    /**
     * Find permission by ID
     */
    public static function find($id)
    {
        $instance = new static();
        $permissionData = $instance->db->selectOne(
            "SELECT * FROM permissions WHERE id = ?",
            [$id]
        );

        if ($permissionData) {
            $instance->data = $permissionData;
            return $instance;
        }

        return null;
    }

    /**
     * Find permission by slug
     */
    public static function findBySlug($slug)
    {
        $instance = new static();
        $permissionData = $instance->db->selectOne(
            "SELECT * FROM permissions WHERE slug = ?",
            [$slug]
        );

        if ($permissionData) {
            $instance->data = $permissionData;
            return $instance;
        }

        return null;
    }

    /**
     * Get all permissions
     */
    public static function all()
    {
        $instance = new static();
        $permissionsData = $instance->db->select("SELECT * FROM permissions ORDER BY `group`, name");
        
        $permissions = [];
        foreach ($permissionsData as $permissionData) {
            $permission = new static();
            $permission->data = $permissionData;
            $permissions[] = $permission;
        }
        
        return $permissions;
    }

    /**
     * Get permissions grouped by category
     */
    public static function getAllGrouped()
    {
        $instance = new static();
        $permissionsData = $instance->db->select("SELECT * FROM permissions ORDER BY `group`, name");
        
        $grouped = [];
        foreach ($permissionsData as $permissionData) {
            $group = $permissionData['group'];
            if (!isset($grouped[$group])) {
                $grouped[$group] = [];
            }
            
            $permission = new static();
            $permission->data = $permissionData;
            $grouped[$group][] = $permission;
        }
        
        return $grouped;
    }

    /**
     * Get permissions by group
     */
    public static function getByGroup($group)
    {
        $instance = new static();
        $permissionsData = $instance->db->select(
            "SELECT * FROM permissions WHERE `group` = ? ORDER BY name",
            [$group]
        );
        
        $permissions = [];
        foreach ($permissionsData as $permissionData) {
            $permission = new static();
            $permission->data = $permissionData;
            $permissions[] = $permission;
        }
        
        return $permissions;
    }

    /**
     * Create new permission
     */
    public static function create($data)
    {
        $instance = new static();
        
        // Generate slug if not provided
        if (!isset($data['slug'])) {
            $data['slug'] = strtolower(str_replace(' ', '.', $data['name']));
        }

        // Set default group if not provided
        if (!isset($data['group'])) {
            $data['group'] = 'general';
        }

        $instance->db->insert('permissions', $data);
        $permissionId = $instance->db->lastInsertId();

        return static::find($permissionId);
    }

    /**
     * Update permission
     */
    public function update($data)
    {
        $this->db->update('permissions', $data, 'id = ?', [$this->getId()]);
        
        // Refresh data
        $this->data = array_merge($this->data, $data);
        
        return $this;
    }

    /**
     * Delete permission
     */
    public function delete()
    {
        // Remove from all roles first
        $this->db->delete('role_permissions', 'permission_id = ?', [$this->getId()]);
        
        // Delete permission
        $this->db->delete('permissions', 'id = ?', [$this->getId()]);
        
        return true;
    }

    /**
     * Get roles that have this permission
     */
    public function getRoles()
    {
        $rolesData = $this->db->select(
            "SELECT r.* FROM roles r 
             INNER JOIN role_permissions rp ON r.id = rp.role_id 
             WHERE rp.permission_id = ? 
             ORDER BY r.name",
            [$this->getId()]
        );

        $roles = [];
        foreach ($rolesData as $roleData) {
            $role = new Role();
            $role->data = $roleData;
            $roles[] = $role;
        }

        return $roles;
    }

    /**
     * Check if permission is assigned to any role
     */
    public function isAssigned()
    {
        $result = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM role_permissions WHERE permission_id = ?",
            [$this->getId()]
        );

        return $result['count'] > 0;
    }

    /**
     * Get permission groups
     */
    public static function getGroups()
    {
        $instance = new static();
        $groups = $instance->db->select("SELECT DISTINCT `group` FROM permissions ORDER BY `group`");
        
        return array_column($groups, 'group');
    }

    // Getters
    public function getId() { return $this->data['id'] ?? null; }
    public function getName() { return $this->data['name'] ?? null; }
    public function getSlug() { return $this->data['slug'] ?? null; }
    public function getDescription() { return $this->data['description'] ?? null; }
    public function getGroup() { return $this->data['group'] ?? null; }
    public function getCreatedAt() { return $this->data['created_at'] ?? null; }
    public function getUpdatedAt() { return $this->data['updated_at'] ?? null; }

    /**
     * Convert to array
     */
    public function toArray()
    {
        return $this->data;
    }

    /**
     * Magic getter
     */
    public function __get($key)
    {
        return $this->data[$key] ?? null;
    }

    /**
     * Magic isset
     */
    public function __isset($key)
    {
        return isset($this->data[$key]);
    }

    // Status constants
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    /**
     * Get all available statuses
     */
    public static function getStatuses()
    {
        return [
            self::STATUS_ACTIVE => __('Active'),
            self::STATUS_INACTIVE => __('Inactive')
        ];
    }

    /**
     * Get system permissions
     */
    public static function getSystemPermissions()
    {
        return [
            // Admin Panel Access
            'admin' => [
                'admin.access' => __('Access Admin Panel'),
                'admin.dashboard' => __('View Dashboard'),
            ],

            // User Management
            'users' => [
                'users.view' => __('View Users'),
                'users.create' => __('Create Users'),
                'users.edit' => __('Edit Users'),
                'users.delete' => __('Delete Users'),
                'users.manage_roles' => __('Manage User Roles'),
                'users.manage_permissions' => __('Manage User Permissions'),
            ],

            // Role Management
            'roles' => [
                'roles.view' => __('View Roles'),
                'roles.create' => __('Create Roles'),
                'roles.edit' => __('Edit Roles'),
                'roles.delete' => __('Delete Roles'),
                'roles.manage_permissions' => __('Manage Role Permissions'),
            ],

            // Permission Management
            'permissions' => [
                'permissions.view' => __('View Permissions'),
                'permissions.create' => __('Create Permissions'),
                'permissions.edit' => __('Edit Permissions'),
                'permissions.delete' => __('Delete Permissions'),
            ],

            // Page Management
            'pages' => [
                'pages.view' => __('View Pages'),
                'pages.create' => __('Create Pages'),
                'pages.edit' => __('Edit Pages'),
                'pages.delete' => __('Delete Pages'),
                'pages.publish' => __('Publish Pages'),
                'pages.manage_all' => __('Manage All Pages'),
            ],

            // Content Management
            'content' => [
                'content.view' => __('View Content'),
                'content.create' => __('Create Content'),
                'content.edit' => __('Edit Content'),
                'content.delete' => __('Delete Content'),
                'content.publish' => __('Publish Content'),
                'content.manage_all' => __('Manage All Content'),
            ],

            // Media Management
            'media' => [
                'media.view' => __('View Media'),
                'media.upload' => __('Upload Media'),
                'media.edit' => __('Edit Media'),
                'media.delete' => __('Delete Media'),
                'media.manage_all' => __('Manage All Media'),
            ],

            // Field Management
            'fields' => [
                'fields.view' => __('View Fields'),
                'fields.create' => __('Create Fields'),
                'fields.edit' => __('Edit Fields'),
                'fields.delete' => __('Delete Fields'),
            ],

            // Settings Management
            'settings' => [
                'settings.view' => __('View Settings'),
                'settings.edit' => __('Edit Settings'),
                'settings.system' => __('System Settings'),
                'settings.security' => __('Security Settings'),
            ],

            // System Management
            'system' => [
                'system.logs' => __('View System Logs'),
                'system.backup' => __('System Backup'),
                'system.maintenance' => __('System Maintenance'),
                'system.updates' => __('System Updates'),
            ]
        ];
    }

    /**
     * Create system permissions if they don't exist
     */
    public static function createSystemPermissions()
    {
        $instance = new static();
        $systemPermissions = self::getSystemPermissions();

        foreach ($systemPermissions as $category => $permissions) {
            foreach ($permissions as $slug => $name) {
                // Check if permission exists
                $existing = $instance->db->selectOne(
                    "SELECT id FROM permissions WHERE slug = ?",
                    [$slug]
                );

                if (!$existing) {
                    $instance->db->insert('permissions', [
                        'name' => $name,
                        'slug' => $slug,
                        'category' => $category,
                        'description' => $name,
                        'status' => self::STATUS_ACTIVE,
                        'is_system' => 1,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                }
            }
        }
    }

    /**
     * Get permissions grouped by category
     */
    public static function getGroupedPermissions()
    {
        $instance = new static();
        $permissions = $instance->db->select(
            "SELECT * FROM permissions WHERE status = 'active' ORDER BY category, name"
        );

        $grouped = [];
        foreach ($permissions as $permission) {
            $category = $permission['category'] ?? 'other';
            if (!isset($grouped[$category])) {
                $grouped[$category] = [];
            }
            $grouped[$category][] = $permission;
        }

        return $grouped;
    }

    /**
     * Check if permission is system permission
     */
    public function isSystem()
    {
        return (bool) ($this->is_system ?? false);
    }

    /**
     * Check if permission can be deleted
     */
    public function canBeDeleted()
    {
        // System permissions cannot be deleted
        if ($this->isSystem()) {
            return false;
        }

        return true;
    }

    /**
     * Get roles that have this permission
     */
    public function getRoles()
    {
        if (!$this->id) return [];

        return $this->db->select(
            "SELECT r.* FROM roles r
             INNER JOIN role_permissions rp ON r.id = rp.role_id
             WHERE rp.permission_id = ? AND r.status = 'active'
             ORDER BY r.name",
            [$this->id]
        );
    }
}
