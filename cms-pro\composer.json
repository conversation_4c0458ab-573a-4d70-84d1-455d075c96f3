{"name": "cms-pro/professional-cms", "description": "Professional PHP Content Management System with Dynamic Field Creation", "type": "project", "license": "MIT", "authors": [{"name": "CMS Pro Team", "email": "<EMAIL>"}], "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=7.1.0", "twig/twig": "^3.0", "doctrine/dbal": "^3.0", "symfony/http-foundation": "^5.4", "symfony/routing": "^5.4", "symfony/security-csrf": "^5.4", "symfony/validator": "^5.4", "monolog/monolog": "^2.0", "phpmailer/phpmailer": "^6.6", "intervention/image": "^2.7", "league/flysystem": "^3.0", "ramsey/uuid": "^4.2", "firebase/php-jwt": "^6.0", "pragmarx/google2fa": "^8.0", "guzzlehttp/guzzle": "^7.4", "league/csv": "^9.8", "spatie/image-optimizer": "^1.7", "cocur/slugify": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "^3.6", "phpstan/phpstan": "^1.4", "symfony/var-dumper": "^5.4", "fakerphp/faker": "^1.19"}, "autoload": {"psr-4": {"CmsPro\\": "src/", "CmsPro\\Controllers\\": "src/Controllers/", "CmsPro\\Models\\": "src/Models/", "CmsPro\\Services\\": "src/Services/", "CmsPro\\Middleware\\": "src/Middleware/", "CmsPro\\Helpers\\": "src/Helpers/"}, "files": ["src/helpers.php"]}, "autoload-dev": {"psr-4": {"CmsPro\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "cs-check": "phpcs --standard=PSR12 src/", "cs-fix": "phpcbf --standard=PSR12 src/", "analyse": "phpstan analyse src/", "post-install-cmd": ["@php -r \"copy('.env.example', '.env');\""], "serve": "php -S localhost:8000 -t public/"}, "config": {"optimize-autoloader": true, "sort-packages": true}, "extra": {"branch-alias": {"dev-main": "1.0-dev"}}}