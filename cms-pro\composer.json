{"name": "cms-pro/professional-cms", "description": "Professional PHP Content Management System with Dynamic Field Creation", "type": "project", "license": "MIT", "authors": [{"name": "CMS Pro Team", "email": "<EMAIL>"}], "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=7.1.0", "twig/twig": "^2.0", "doctrine/dbal": "^2.13", "symfony/http-foundation": "^4.4", "symfony/routing": "^4.4", "symfony/security-csrf": "^4.4", "symfony/validator": "^4.4", "monolog/monolog": "^1.25", "phpmailer/phpmailer": "^6.6", "intervention/image": "^2.7", "league/flysystem": "^1.0", "ramsey/uuid": "^3.9", "firebase/php-jwt": "^5.0", "pragmarx/google2fa": "^7.0", "guzzlehttp/guzzle": "^6.5", "league/csv": "^9.0", "cocur/slugify": "^3.0"}, "require-dev": {"phpunit/phpunit": "^7.5", "squizlabs/php_codesniffer": "^3.5", "symfony/var-dumper": "^4.4", "fzaninotto/faker": "^1.9"}, "autoload": {"psr-4": {"CmsPro\\": "src/", "CmsPro\\Controllers\\": "src/Controllers/", "CmsPro\\Models\\": "src/Models/", "CmsPro\\Services\\": "src/Services/", "CmsPro\\Middleware\\": "src/Middleware/", "CmsPro\\Helpers\\": "src/Helpers/"}, "files": ["src/helpers.php", "src/Helpers/functions.php"]}, "autoload-dev": {"psr-4": {"CmsPro\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "cs-check": "phpcs --standard=PSR12 src/", "cs-fix": "phpcbf --standard=PSR12 src/", "analyse": "phpstan analyse src/", "post-install-cmd": ["@php -r \"copy('.env.example', '.env');\""], "serve": "php -S localhost:8000 -t public/"}, "config": {"optimize-autoloader": true, "sort-packages": true}, "extra": {"branch-alias": {"dev-main": "1.0-dev"}}}