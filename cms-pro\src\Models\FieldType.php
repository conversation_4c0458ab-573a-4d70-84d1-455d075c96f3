<?php

namespace CmsPro\Models;

use CmsPro\Core\Database;

/**
 * Field Type Model
 * 
 * @package CmsPro\Models
 */
class FieldType
{
    private $db;
    private $data = [];

    public function __construct(Database $db = null)
    {
        $this->db = $db ?: app()->getDatabase();
    }

    /**
     * Find field type by ID
     */
    public static function find($id)
    {
        $instance = new static();
        $fieldTypeData = $instance->db->selectOne(
            "SELECT * FROM field_types WHERE id = ?",
            [$id]
        );

        if ($fieldTypeData) {
            $instance->data = $fieldTypeData;
            return $instance;
        }

        return null;
    }

    /**
     * Find field type by slug
     */
    public static function findBySlug($slug)
    {
        $instance = new static();
        $fieldTypeData = $instance->db->selectOne(
            "SELECT * FROM field_types WHERE slug = ?",
            [$slug]
        );

        if ($fieldTypeData) {
            $instance->data = $fieldTypeData;
            return $instance;
        }

        return null;
    }

    /**
     * Get all active field types
     */
    public static function getAllActive()
    {
        $instance = new static();
        $fieldTypesData = $instance->db->select(
            "SELECT * FROM field_types WHERE is_active = 1 ORDER BY sort_order, name"
        );
        
        $fieldTypes = [];
        foreach ($fieldTypesData as $fieldTypeData) {
            $fieldType = new static();
            $fieldType->data = $fieldTypeData;
            $fieldTypes[] = $fieldType;
        }
        
        return $fieldTypes;
    }

    /**
     * Get all field types
     */
    public static function getAll()
    {
        $instance = new static();
        $fieldTypesData = $instance->db->select(
            "SELECT * FROM field_types ORDER BY sort_order, name"
        );
        
        $fieldTypes = [];
        foreach ($fieldTypesData as $fieldTypeData) {
            $fieldType = new static();
            $fieldType->data = $fieldTypeData;
            $fieldTypes[] = $fieldType;
        }
        
        return $fieldTypes;
    }

    /**
     * Get field types grouped by category
     */
    public static function getGrouped()
    {
        $fieldTypes = static::getAllActive();
        $grouped = [];

        foreach ($fieldTypes as $fieldType) {
            $category = $fieldType->getCategory();
            if (!isset($grouped[$category])) {
                $grouped[$category] = [];
            }
            $grouped[$category][] = $fieldType;
        }

        return $grouped;
    }

    /**
     * Create new field type
     */
    public static function create($data)
    {
        $instance = new static();
        
        // Generate slug if not provided
        if (!isset($data['slug'])) {
            $data['slug'] = strtolower(str_replace(' ', '_', $data['name']));
        }

        // Encode JSON fields
        if (isset($data['validation_rules']) && is_array($data['validation_rules'])) {
            $data['validation_rules'] = json_encode($data['validation_rules']);
        }

        if (isset($data['default_settings']) && is_array($data['default_settings'])) {
            $data['default_settings'] = json_encode($data['default_settings']);
        }

        $instance->db->insert('field_types', $data);
        $fieldTypeId = $instance->db->lastInsertId();

        return static::find($fieldTypeId);
    }

    /**
     * Update field type
     */
    public function update($data)
    {
        // Encode JSON fields
        if (isset($data['validation_rules']) && is_array($data['validation_rules'])) {
            $data['validation_rules'] = json_encode($data['validation_rules']);
        }

        if (isset($data['default_settings']) && is_array($data['default_settings'])) {
            $data['default_settings'] = json_encode($data['default_settings']);
        }

        $this->db->update('field_types', $data, 'id = ?', [$this->getId()]);
        
        // Refresh data
        $this->data = array_merge($this->data, $data);
        
        return $this;
    }

    /**
     * Delete field type
     */
    public function delete()
    {
        // Check if field type is used by any fields
        $fieldCount = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM fields WHERE field_type_id = ?",
            [$this->getId()]
        );

        if ($fieldCount['count'] > 0) {
            throw new \RuntimeException('Cannot delete field type that is used by fields.');
        }

        $this->db->delete('field_types', 'id = ?', [$this->getId()]);
        return true;
    }

    /**
     * Get validation rules as array
     */
    public function getValidationRules()
    {
        $rules = $this->data['validation_rules'] ?? '{}';
        return json_decode($rules, true) ?: [];
    }

    /**
     * Get default settings as array
     */
    public function getDefaultSettings()
    {
        $settings = $this->data['default_settings'] ?? '{}';
        return json_decode($settings, true) ?: [];
    }

    /**
     * Get field type category based on slug
     */
    public function getCategory()
    {
        $categories = [
            'text' => 'Text Fields',
            'textarea' => 'Text Fields',
            'wysiwyg' => 'Text Fields',
            'password' => 'Text Fields',
            'number' => 'Number Fields',
            'range' => 'Number Fields',
            'select' => 'Selection Fields',
            'multiselect' => 'Selection Fields',
            'radio' => 'Selection Fields',
            'checkbox' => 'Selection Fields',
            'boolean' => 'Selection Fields',
            'date' => 'Date & Time Fields',
            'time' => 'Date & Time Fields',
            'datetime' => 'Date & Time Fields',
            'file' => 'File & Media Fields',
            'image' => 'File & Media Fields',
            'gallery' => 'File & Media Fields',
            'color' => 'Advanced Fields',
            'url' => 'Advanced Fields',
            'email' => 'Advanced Fields',
            'phone' => 'Advanced Fields',
            'heading' => 'Layout Fields',
            'separator' => 'Layout Fields',
            'html' => 'Layout Fields',
            'content_relation' => 'Relationship Fields',
            'user_relation' => 'Relationship Fields',
            'repeater' => 'Repeater Fields',
            'flexible' => 'Repeater Fields',
        ];

        return $categories[$this->getSlug()] ?? 'Other Fields';
    }

    /**
     * Check if field type supports options
     */
    public function supportsOptions()
    {
        $optionTypes = ['select', 'multiselect', 'radio', 'checkbox'];
        return in_array($this->getSlug(), $optionTypes);
    }

    /**
     * Check if field type supports multiple values
     */
    public function supportsMultiple()
    {
        $multipleTypes = ['multiselect', 'checkbox', 'gallery', 'repeater', 'flexible'];
        return in_array($this->getSlug(), $multipleTypes);
    }

    /**
     * Check if field type is a layout field
     */
    public function isLayoutField()
    {
        $layoutTypes = ['heading', 'separator', 'html'];
        return in_array($this->getSlug(), $layoutTypes);
    }

    /**
     * Get field type icon with fallback
     */
    public function getIconClass()
    {
        return $this->getIcon() ?: 'fas fa-square';
    }

    /**
     * Get component name for frontend rendering
     */
    public function getComponentName()
    {
        return $this->getComponent() ?: 'TextInput';
    }

    // Getters
    public function getId() { return $this->data['id'] ?? null; }
    public function getName() { return $this->data['name'] ?? null; }
    public function getSlug() { return $this->data['slug'] ?? null; }
    public function getDescription() { return $this->data['description'] ?? null; }
    public function getIcon() { return $this->data['icon'] ?? null; }
    public function getComponent() { return $this->data['component'] ?? null; }
    public function isActive() { return (bool) ($this->data['is_active'] ?? false); }
    public function getSortOrder() { return $this->data['sort_order'] ?? 0; }
    public function getCreatedAt() { return $this->data['created_at'] ?? null; }
    public function getUpdatedAt() { return $this->data['updated_at'] ?? null; }

    /**
     * Convert to array
     */
    public function toArray()
    {
        $data = $this->data;
        $data['validation_rules'] = $this->getValidationRules();
        $data['default_settings'] = $this->getDefaultSettings();
        $data['category'] = $this->getCategory();
        $data['supports_options'] = $this->supportsOptions();
        $data['supports_multiple'] = $this->supportsMultiple();
        $data['is_layout_field'] = $this->isLayoutField();
        return $data;
    }

    /**
     * Magic getter
     */
    public function __get($key)
    {
        return $this->data[$key] ?? null;
    }

    /**
     * Magic isset
     */
    public function __isset($key)
    {
        return isset($this->data[$key]);
    }
}
