#!/bin/bash

# CMS Pro Production Deployment Script
# This script automates the deployment process for production environment

set -e  # Exit on any error

# Configuration
PROJECT_NAME="cms-pro"
DOMAIN_NAME="${DOMAIN_NAME:-your-domain.com}"
SSL_EMAIL="${SSL_EMAIL:-<EMAIL>}"
BACKUP_RETENTION_DAYS=30

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
        exit 1
    fi
}

# Check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if user is in docker group
    if ! groups $USER | grep -q docker; then
        error "User $USER is not in the docker group. Please add user to docker group."
        exit 1
    fi
    
    log "System requirements check passed"
}

# Setup environment variables
setup_environment() {
    log "Setting up environment variables..."
    
    if [[ ! -f .env.production ]]; then
        warning ".env.production file not found. Creating from template..."
        cp .env.example .env.production
        
        # Generate secure passwords
        DB_PASSWORD=$(openssl rand -base64 32)
        REDIS_PASSWORD=$(openssl rand -base64 32)
        MYSQL_ROOT_PASSWORD=$(openssl rand -base64 32)
        GRAFANA_PASSWORD=$(openssl rand -base64 32)
        
        # Update .env.production
        sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=${DB_PASSWORD}/" .env.production
        sed -i "s/REDIS_PASSWORD=.*/REDIS_PASSWORD=${REDIS_PASSWORD}/" .env.production
        sed -i "s/MYSQL_ROOT_PASSWORD=.*/MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}/" .env.production
        sed -i "s/GRAFANA_PASSWORD=.*/GRAFANA_PASSWORD=${GRAFANA_PASSWORD}/" .env.production
        sed -i "s/DOMAIN_NAME=.*/DOMAIN_NAME=${DOMAIN_NAME}/" .env.production
        sed -i "s/SSL_EMAIL=.*/SSL_EMAIL=${SSL_EMAIL}/" .env.production
        
        warning "Please review and update .env.production file with your specific settings"
        info "Generated passwords have been set. Please save them securely."
    fi
    
    # Load environment variables
    export $(cat .env.production | grep -v '^#' | xargs)
    
    log "Environment variables configured"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    mkdir -p storage/logs/{nginx,php,mysql,app}
    mkdir -p storage/cache
    mkdir -p storage/sessions
    mkdir -p storage/uploads
    mkdir -p deploy/ssl
    mkdir -p deploy/backup/data
    
    # Set proper permissions
    chmod -R 755 storage/
    chmod -R 755 deploy/
    
    log "Directories created successfully"
}

# Install SSL certificate
install_ssl() {
    log "Installing SSL certificate..."
    
    if [[ ! -f deploy/ssl/fullchain.pem ]]; then
        info "Obtaining SSL certificate from Let's Encrypt..."
        
        # Start temporary nginx for certificate validation
        docker run --rm -d \
            --name temp-nginx \
            -p 80:80 \
            -v $(pwd)/public:/var/www/html/public \
            nginx:alpine
        
        # Get certificate
        docker run --rm \
            -v $(pwd)/deploy/ssl:/etc/letsencrypt \
            -v $(pwd)/public:/var/www/html/public \
            certbot/certbot \
            certonly --webroot \
            --webroot-path=/var/www/html/public \
            --email ${SSL_EMAIL} \
            --agree-tos \
            --no-eff-email \
            -d ${DOMAIN_NAME}
        
        # Stop temporary nginx
        docker stop temp-nginx
        
        # Copy certificates to nginx directory
        cp deploy/ssl/live/${DOMAIN_NAME}/fullchain.pem deploy/ssl/
        cp deploy/ssl/live/${DOMAIN_NAME}/privkey.pem deploy/ssl/
        
        log "SSL certificate installed successfully"
    else
        info "SSL certificate already exists"
    fi
}

# Build Docker images
build_images() {
    log "Building Docker images..."
    
    # Build PHP-FPM image
    docker build -f deploy/Dockerfile.php -t ${PROJECT_NAME}-php:latest .
    
    # Build backup image
    docker build -f deploy/Dockerfile.backup -t ${PROJECT_NAME}-backup:latest .
    
    log "Docker images built successfully"
}

# Deploy application
deploy_application() {
    log "Deploying application..."
    
    # Stop existing containers
    docker-compose -f deploy/production.yml down
    
    # Start services
    docker-compose -f deploy/production.yml up -d
    
    # Wait for services to be ready
    info "Waiting for services to start..."
    sleep 30
    
    # Run database migrations
    docker-compose -f deploy/production.yml exec php-fpm php artisan migrate --force
    
    # Clear and warm up cache
    docker-compose -f deploy/production.yml exec php-fpm php artisan cache:clear
    docker-compose -f deploy/production.yml exec php-fpm php artisan config:cache
    docker-compose -f deploy/production.yml exec php-fpm php artisan route:cache
    
    log "Application deployed successfully"
}

# Setup monitoring
setup_monitoring() {
    log "Setting up monitoring..."
    
    # Wait for Prometheus to be ready
    info "Configuring Prometheus..."
    sleep 10
    
    # Wait for Grafana to be ready
    info "Configuring Grafana dashboards..."
    sleep 15
    
    log "Monitoring setup completed"
}

# Setup backup
setup_backup() {
    log "Setting up backup system..."
    
    # Create backup cron job
    cat > /tmp/backup-cron << EOF
0 2 * * * cd $(pwd) && docker-compose -f deploy/production.yml exec backup /backup/scripts/backup.sh
0 3 * * 0 cd $(pwd) && docker-compose -f deploy/production.yml exec backup /backup/scripts/cleanup.sh
EOF
    
    crontab /tmp/backup-cron
    rm /tmp/backup-cron
    
    log "Backup system configured"
}

# Health check
health_check() {
    log "Performing health check..."
    
    # Check web server
    if curl -f -s https://${DOMAIN_NAME}/health > /dev/null; then
        log "Web server is healthy"
    else
        error "Web server health check failed"
        return 1
    fi
    
    # Check database
    if docker-compose -f deploy/production.yml exec mysql mysqladmin ping -h localhost > /dev/null 2>&1; then
        log "Database is healthy"
    else
        error "Database health check failed"
        return 1
    fi
    
    # Check Redis
    if docker-compose -f deploy/production.yml exec redis redis-cli ping > /dev/null 2>&1; then
        log "Redis is healthy"
    else
        error "Redis health check failed"
        return 1
    fi
    
    log "All health checks passed"
}

# Setup firewall
setup_firewall() {
    log "Configuring firewall..."
    
    # Enable UFW if not already enabled
    if ! sudo ufw status | grep -q "Status: active"; then
        sudo ufw --force enable
    fi
    
    # Allow SSH
    sudo ufw allow ssh
    
    # Allow HTTP and HTTPS
    sudo ufw allow 80/tcp
    sudo ufw allow 443/tcp
    
    # Allow monitoring (restrict to specific IPs in production)
    sudo ufw allow 3000/tcp  # Grafana
    sudo ufw allow 9090/tcp  # Prometheus
    
    # Deny all other incoming traffic
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    
    log "Firewall configured"
}

# Cleanup old deployments
cleanup() {
    log "Cleaning up old deployments..."
    
    # Remove old Docker images
    docker image prune -f
    
    # Remove old logs
    find storage/logs -name "*.log" -mtime +${BACKUP_RETENTION_DAYS} -delete
    
    log "Cleanup completed"
}

# Main deployment function
main() {
    log "Starting CMS Pro production deployment..."
    
    check_root
    check_requirements
    setup_environment
    create_directories
    install_ssl
    build_images
    deploy_application
    setup_monitoring
    setup_backup
    setup_firewall
    health_check
    cleanup
    
    log "Deployment completed successfully!"
    info "Your CMS Pro installation is now available at: https://${DOMAIN_NAME}"
    info "Admin panel: https://${DOMAIN_NAME}/admin"
    info "Grafana monitoring: https://${DOMAIN_NAME}:3000"
    info "Please save your generated passwords from .env.production file"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "health")
        health_check
        ;;
    "backup")
        docker-compose -f deploy/production.yml exec backup /backup/scripts/backup.sh
        ;;
    "logs")
        docker-compose -f deploy/production.yml logs -f
        ;;
    "restart")
        docker-compose -f deploy/production.yml restart
        ;;
    "stop")
        docker-compose -f deploy/production.yml down
        ;;
    "update")
        log "Updating application..."
        git pull origin main
        build_images
        docker-compose -f deploy/production.yml up -d
        health_check
        log "Update completed"
        ;;
    *)
        echo "Usage: $0 {deploy|health|backup|logs|restart|stop|update}"
        exit 1
        ;;
esac
