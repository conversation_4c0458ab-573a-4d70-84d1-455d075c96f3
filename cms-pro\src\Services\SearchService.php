<?php

namespace CmsPro\Services;

use CmsPro\Core\Database;
use CmsPro\Models\Page;
use CmsPro\Models\BlogPost;
use CmsPro\Models\Category;
use CmsPro\Models\Tag;

/**
 * Search Service
 * 
 * Provides full-text search functionality across content types
 */
class SearchService
{
    private $db;
    private $cacheService;

    public function __construct()
    {
        $this->db = app()->getDatabase();
        $this->cacheService = new CacheService();
    }

    /**
     * Perform global search across all content types
     */
    public function search($query, $options = [])
    {
        $query = trim($query);
        
        if (strlen($query) < 2) {
            return [
                'results' => [],
                'total' => 0,
                'query' => $query,
                'suggestions' => []
            ];
        }

        // Default options
        $options = array_merge([
            'types' => ['pages', 'blog_posts', 'categories', 'tags'],
            'limit' => 20,
            'page' => 1,
            'status' => 'published',
            'highlight' => true,
            'fuzzy' => true
        ], $options);

        // Check cache first
        $cacheKey = 'search_' . md5(serialize([$query, $options]));
        $cached = $this->cacheService->get($cacheKey);
        
        if ($cached) {
            return $cached;
        }

        $results = [];
        $total = 0;

        // Search in different content types
        foreach ($options['types'] as $type) {
            switch ($type) {
                case 'pages':
                    $pageResults = $this->searchPages($query, $options);
                    $results = array_merge($results, $pageResults);
                    break;
                    
                case 'blog_posts':
                    $blogResults = $this->searchBlogPosts($query, $options);
                    $results = array_merge($results, $blogResults);
                    break;
                    
                case 'categories':
                    $categoryResults = $this->searchCategories($query, $options);
                    $results = array_merge($results, $categoryResults);
                    break;
                    
                case 'tags':
                    $tagResults = $this->searchTags($query, $options);
                    $results = array_merge($results, $tagResults);
                    break;
            }
        }

        // Sort results by relevance
        $results = $this->sortByRelevance($results, $query);

        // Apply pagination
        $total = count($results);
        $offset = ($options['page'] - 1) * $options['limit'];
        $results = array_slice($results, $offset, $options['limit']);

        // Add highlighting
        if ($options['highlight']) {
            $results = $this->addHighlighting($results, $query);
        }

        // Generate suggestions
        $suggestions = $this->generateSuggestions($query);

        $searchResults = [
            'results' => $results,
            'total' => $total,
            'query' => $query,
            'suggestions' => $suggestions,
            'page' => $options['page'],
            'limit' => $options['limit'],
            'pages' => ceil($total / $options['limit'])
        ];

        // Cache results for 15 minutes
        $this->cacheService->set($cacheKey, $searchResults, 900);

        return $searchResults;
    }

    /**
     * Search in pages
     */
    private function searchPages($query, $options)
    {
        $searchTerms = $this->prepareSearchTerms($query, $options['fuzzy']);
        
        $sql = "
            SELECT 
                id, title, slug, content, excerpt, created_at, updated_at, views,
                'page' as type,
                MATCH(title, content, excerpt) AGAINST(? IN BOOLEAN MODE) as relevance
            FROM pages 
            WHERE status = ? 
            AND MATCH(title, content, excerpt) AGAINST(? IN BOOLEAN MODE)
            ORDER BY relevance DESC, views DESC
        ";
        
        $pages = $this->db->select($sql, [$searchTerms, $options['status'], $searchTerms]);
        
        $results = [];
        foreach ($pages as $page) {
            $results[] = [
                'id' => $page['id'],
                'type' => 'page',
                'title' => $page['title'],
                'slug' => $page['slug'],
                'content' => $page['content'],
                'excerpt' => $page['excerpt'] ?: $this->generateExcerpt($page['content']),
                'url' => '/' . $page['slug'],
                'date' => $page['created_at'],
                'views' => $page['views'],
                'relevance' => $page['relevance']
            ];
        }
        
        return $results;
    }

    /**
     * Search in blog posts
     */
    private function searchBlogPosts($query, $options)
    {
        $searchTerms = $this->prepareSearchTerms($query, $options['fuzzy']);
        
        $sql = "
            SELECT 
                bp.id, bp.title, bp.slug, bp.content, bp.excerpt, 
                bp.created_at, bp.updated_at, bp.views,
                c.name as category_name, c.slug as category_slug,
                'blog_post' as type,
                MATCH(bp.title, bp.content, bp.excerpt) AGAINST(? IN BOOLEAN MODE) as relevance
            FROM blog_posts bp
            LEFT JOIN categories c ON bp.category_id = c.id
            WHERE bp.status = ? 
            AND MATCH(bp.title, bp.content, bp.excerpt) AGAINST(? IN BOOLEAN MODE)
            ORDER BY relevance DESC, bp.views DESC
        ";
        
        $posts = $this->db->select($sql, [$searchTerms, $options['status'], $searchTerms]);
        
        $results = [];
        foreach ($posts as $post) {
            $results[] = [
                'id' => $post['id'],
                'type' => 'blog_post',
                'title' => $post['title'],
                'slug' => $post['slug'],
                'content' => $post['content'],
                'excerpt' => $post['excerpt'] ?: $this->generateExcerpt($post['content']),
                'url' => '/blog/' . $post['slug'],
                'date' => $post['created_at'],
                'views' => $post['views'],
                'category' => $post['category_name'],
                'category_slug' => $post['category_slug'],
                'relevance' => $post['relevance']
            ];
        }
        
        return $results;
    }

    /**
     * Search in categories
     */
    private function searchCategories($query, $options)
    {
        $searchTerms = $this->prepareSearchTerms($query, $options['fuzzy']);
        
        $sql = "
            SELECT 
                c.id, c.name, c.slug, c.description, c.created_at,
                COUNT(bp.id) as posts_count,
                'category' as type,
                MATCH(c.name, c.description) AGAINST(? IN BOOLEAN MODE) as relevance
            FROM categories c
            LEFT JOIN blog_posts bp ON c.id = bp.category_id AND bp.status = 'published'
            WHERE MATCH(c.name, c.description) AGAINST(? IN BOOLEAN MODE)
            GROUP BY c.id
            ORDER BY relevance DESC, posts_count DESC
        ";
        
        $categories = $this->db->select($sql, [$searchTerms, $searchTerms]);
        
        $results = [];
        foreach ($categories as $category) {
            $results[] = [
                'id' => $category['id'],
                'type' => 'category',
                'title' => $category['name'],
                'slug' => $category['slug'],
                'content' => $category['description'],
                'excerpt' => $category['description'],
                'url' => '/category/' . $category['slug'],
                'date' => $category['created_at'],
                'posts_count' => $category['posts_count'],
                'relevance' => $category['relevance']
            ];
        }
        
        return $results;
    }

    /**
     * Search in tags
     */
    private function searchTags($query, $options)
    {
        $searchTerms = $this->prepareSearchTerms($query, $options['fuzzy']);
        
        $sql = "
            SELECT 
                t.id, t.name, t.slug, t.description, t.created_at,
                COUNT(bpt.post_id) as posts_count,
                'tag' as type,
                MATCH(t.name, t.description) AGAINST(? IN BOOLEAN MODE) as relevance
            FROM tags t
            LEFT JOIN blog_post_tags bpt ON t.id = bpt.tag_id
            LEFT JOIN blog_posts bp ON bpt.post_id = bp.id AND bp.status = 'published'
            WHERE MATCH(t.name, t.description) AGAINST(? IN BOOLEAN MODE)
            GROUP BY t.id
            ORDER BY relevance DESC, posts_count DESC
        ";
        
        $tags = $this->db->select($sql, [$searchTerms, $searchTerms]);
        
        $results = [];
        foreach ($tags as $tag) {
            $results[] = [
                'id' => $tag['id'],
                'type' => 'tag',
                'title' => $tag['name'],
                'slug' => $tag['slug'],
                'content' => $tag['description'],
                'excerpt' => $tag['description'],
                'url' => '/tag/' . $tag['slug'],
                'date' => $tag['created_at'],
                'posts_count' => $tag['posts_count'],
                'relevance' => $tag['relevance']
            ];
        }
        
        return $results;
    }

    /**
     * Prepare search terms for MySQL FULLTEXT search
     */
    private function prepareSearchTerms($query, $fuzzy = true)
    {
        $terms = explode(' ', $query);
        $searchTerms = [];
        
        foreach ($terms as $term) {
            $term = trim($term);
            if (strlen($term) >= 2) {
                if ($fuzzy) {
                    $searchTerms[] = $term . '*';
                } else {
                    $searchTerms[] = '+' . $term;
                }
            }
        }
        
        return implode(' ', $searchTerms);
    }

    /**
     * Sort results by relevance
     */
    private function sortByRelevance($results, $query)
    {
        $queryWords = array_map('strtolower', explode(' ', $query));
        
        usort($results, function($a, $b) use ($queryWords) {
            // Primary sort by MySQL relevance score
            if ($a['relevance'] != $b['relevance']) {
                return $b['relevance'] <=> $a['relevance'];
            }
            
            // Secondary sort by title match
            $aTitle = strtolower($a['title']);
            $bTitle = strtolower($b['title']);
            
            $aTitleScore = 0;
            $bTitleScore = 0;
            
            foreach ($queryWords as $word) {
                if (strpos($aTitle, $word) !== false) $aTitleScore++;
                if (strpos($bTitle, $word) !== false) $bTitleScore++;
            }
            
            if ($aTitleScore != $bTitleScore) {
                return $bTitleScore <=> $aTitleScore;
            }
            
            // Tertiary sort by views/popularity
            $aViews = $a['views'] ?? $a['posts_count'] ?? 0;
            $bViews = $b['views'] ?? $b['posts_count'] ?? 0;
            
            return $bViews <=> $aViews;
        });
        
        return $results;
    }

    /**
     * Add highlighting to search results
     */
    private function addHighlighting($results, $query)
    {
        $queryWords = explode(' ', $query);
        
        foreach ($results as &$result) {
            $result['highlighted_title'] = $this->highlightText($result['title'], $queryWords);
            $result['highlighted_excerpt'] = $this->highlightText($result['excerpt'], $queryWords);
        }
        
        return $results;
    }

    /**
     * Highlight search terms in text
     */
    private function highlightText($text, $words)
    {
        foreach ($words as $word) {
            $word = trim($word);
            if (strlen($word) >= 2) {
                $text = preg_replace(
                    '/(' . preg_quote($word, '/') . ')/i',
                    '<mark>$1</mark>',
                    $text
                );
            }
        }
        
        return $text;
    }

    /**
     * Generate search suggestions
     */
    private function generateSuggestions($query)
    {
        // Simple suggestion based on popular searches
        $suggestions = [];
        
        // Get popular tags as suggestions
        $popularTags = $this->db->select(
            "SELECT name FROM tags ORDER BY (
                SELECT COUNT(*) FROM blog_post_tags bpt 
                INNER JOIN blog_posts bp ON bpt.post_id = bp.id 
                WHERE bpt.tag_id = tags.id AND bp.status = 'published'
            ) DESC LIMIT 5"
        );
        
        foreach ($popularTags as $tag) {
            if (stripos($tag['name'], $query) !== false) {
                $suggestions[] = $tag['name'];
            }
        }
        
        return array_slice($suggestions, 0, 3);
    }

    /**
     * Generate excerpt from content
     */
    private function generateExcerpt($content, $length = 160)
    {
        $text = strip_tags($content);
        $text = preg_replace('/\s+/', ' ', $text);
        
        if (strlen($text) <= $length) {
            return $text;
        }
        
        $excerpt = substr($text, 0, $length);
        $lastSpace = strrpos($excerpt, ' ');
        
        if ($lastSpace !== false) {
            $excerpt = substr($excerpt, 0, $lastSpace);
        }
        
        return $excerpt . '...';
    }

    /**
     * Get search statistics
     */
    public function getSearchStatistics()
    {
        return [
            'total_pages' => $this->db->selectOne("SELECT COUNT(*) as count FROM pages WHERE status = 'published'")['count'],
            'total_posts' => $this->db->selectOne("SELECT COUNT(*) as count FROM blog_posts WHERE status = 'published'")['count'],
            'total_categories' => $this->db->selectOne("SELECT COUNT(*) as count FROM categories")['count'],
            'total_tags' => $this->db->selectOne("SELECT COUNT(*) as count FROM tags")['count']
        ];
    }

    /**
     * Clear search cache
     */
    public function clearCache()
    {
        return $this->cacheService->flush('search_*');
    }
}
