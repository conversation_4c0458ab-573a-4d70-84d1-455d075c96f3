<?php

/**
 * Quick Database Setup Script
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Database configuration
$config = [
    'host' => $_ENV['DB_HOST'] ?? '127.0.0.1',
    'port' => $_ENV['DB_PORT'] ?? '3306',
    'database' => $_ENV['DB_DATABASE'] ?? 'cms_pro',
    'username' => $_ENV['DB_USERNAME'] ?? 'root',
    'password' => $_ENV['DB_PASSWORD'] ?? '',
    'charset' => 'utf8mb4'
];

try {
    // Connect to database
    $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$config['charset']}"
    ]);

    echo "✅ Database connection successful!\n";

    // Drop existing tables if they exist
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    
    $tables = ['blog_post_tags', 'comments', 'blog_posts', 'pages', 'media', 'activity_logs', 'user_roles', 'role_permissions', 'users', 'roles', 'permissions', 'categories', 'tags', 'settings'];
    
    foreach ($tables as $table) {
        $pdo->exec("DROP TABLE IF EXISTS {$table}");
    }
    
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    
    echo "🔄 Creating database tables...\n";

    // Create roles table
    $pdo->exec("
        CREATE TABLE roles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            slug VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // Create users table
    $pdo->exec("
        CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            email VARCHAR(255) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            role_id INT NOT NULL,
            status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
            avatar VARCHAR(255),
            bio TEXT,
            last_login_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE RESTRICT
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // Create settings table
    $pdo->exec("
        CREATE TABLE settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            `key` VARCHAR(255) NOT NULL UNIQUE,
            `value` TEXT,
            type VARCHAR(50) DEFAULT 'string',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // Create categories table
    $pdo->exec("
        CREATE TABLE categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            slug VARCHAR(255) NOT NULL UNIQUE,
            description TEXT,
            parent_id INT NULL,
            sort_order INT DEFAULT 0,
            color VARCHAR(7) DEFAULT '#007bff',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // Create tags table
    $pdo->exec("
        CREATE TABLE tags (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            slug VARCHAR(255) NOT NULL UNIQUE,
            description TEXT,
            color VARCHAR(7) DEFAULT '#6c757d',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // Create pages table
    $pdo->exec("
        CREATE TABLE pages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            slug VARCHAR(255) NOT NULL UNIQUE,
            content LONGTEXT,
            excerpt TEXT,
            status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
            author_id INT NOT NULL,
            parent_id INT NULL,
            sort_order INT DEFAULT 0,
            template VARCHAR(100) DEFAULT 'default',
            meta_title VARCHAR(255),
            meta_description TEXT,
            meta_keywords TEXT,
            featured_image VARCHAR(255),
            views INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE RESTRICT,
            FOREIGN KEY (parent_id) REFERENCES pages(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // Create blog_posts table
    $pdo->exec("
        CREATE TABLE blog_posts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            slug VARCHAR(255) NOT NULL UNIQUE,
            content LONGTEXT,
            excerpt TEXT,
            status ENUM('draft', 'published', 'scheduled', 'archived') DEFAULT 'draft',
            author_id INT NOT NULL,
            category_id INT NULL,
            featured_image VARCHAR(255),
            meta_title VARCHAR(255),
            meta_description TEXT,
            meta_keywords TEXT,
            views INT DEFAULT 0,
            likes INT DEFAULT 0,
            comments_count INT DEFAULT 0,
            reading_time INT DEFAULT 0,
            published_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE RESTRICT,
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // Create blog_post_tags table
    $pdo->exec("
        CREATE TABLE blog_post_tags (
            id INT AUTO_INCREMENT PRIMARY KEY,
            post_id INT NOT NULL,
            tag_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_post_tag (post_id, tag_id),
            FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
            FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // Create comments table
    $pdo->exec("
        CREATE TABLE comments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            post_id INT NOT NULL,
            post_type ENUM('blog_post', 'page') DEFAULT 'blog_post',
            parent_id INT NULL,
            author_name VARCHAR(255) NOT NULL,
            author_email VARCHAR(255) NOT NULL,
            author_url VARCHAR(255),
            author_ip VARCHAR(45),
            content TEXT NOT NULL,
            status ENUM('pending', 'approved', 'spam', 'trash') DEFAULT 'pending',
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // Create media table
    $pdo->exec("
        CREATE TABLE media (
            id INT AUTO_INCREMENT PRIMARY KEY,
            filename VARCHAR(255) NOT NULL,
            original_name VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_size INT NOT NULL,
            mime_type VARCHAR(100) NOT NULL,
            file_type ENUM('image', 'document', 'video', 'audio', 'other') DEFAULT 'other',
            folder VARCHAR(255) DEFAULT 'uploads',
            alt_text VARCHAR(255),
            caption TEXT,
            uploaded_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE RESTRICT
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // Create activity_logs table
    $pdo->exec("
        CREATE TABLE activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NULL,
            action VARCHAR(255) NOT NULL,
            description TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            data JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    echo "✅ Database tables created successfully!\n";

    // Insert default data
    echo "🔄 Inserting default data...\n";
    
    // Create admin role
    $pdo->exec("INSERT INTO roles (id, name, slug, description) VALUES 
        (1, 'Administrator', 'administrator', 'Full system access')");
    
    // Create editor role  
    $pdo->exec("INSERT INTO roles (id, name, slug, description) VALUES 
        (2, 'Editor', 'editor', 'Content management access')");
    
    // Create admin user
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $pdo->exec("INSERT INTO users (id, first_name, last_name, email, password, role_id, status) VALUES 
        (1, 'Admin', 'User', '<EMAIL>', '{$adminPassword}', 1, 'active')");
    
    // Create basic settings
    $settings = [
        ['site_name', 'CMS Pro'],
        ['site_description', 'Modern Content Management System'],
        ['site_url', 'http://localhost/cms-pro'],
        ['admin_email', '<EMAIL>'],
        ['timezone', 'Europe/Istanbul'],
        ['language', 'tr'],
        ['theme', 'default'],
        ['posts_per_page', '10'],
        ['allow_comments', '1'],
        ['moderate_comments', '1']
    ];
    
    foreach ($settings as $setting) {
        $pdo->exec("INSERT INTO settings (`key`, `value`) VALUES ('{$setting[0]}', '{$setting[1]}')");
    }
    
    // Create sample category
    $pdo->exec("INSERT INTO categories (id, name, slug, description, sort_order) VALUES 
        (1, 'Genel', 'genel', 'Genel kategori', 1)");
    
    // Create sample tag
    $pdo->exec("INSERT INTO tags (id, name, slug, description) VALUES 
        (1, 'Örnek', 'ornek', 'Örnek etiket')");
    
    // Create sample page
    $pdo->exec("INSERT INTO pages (id, title, slug, content, excerpt, status, author_id) VALUES 
        (1, 'Ana Sayfa', 'ana-sayfa', '<h1>CMS Pro\'ya Hoş Geldiniz!</h1><p>Bu modern içerik yönetim sistemi ile web sitenizi kolayca yönetebilirsiniz.</p>', 'CMS Pro ana sayfası', 'published', 1)");
    
    // Create sample blog post
    $pdo->exec("INSERT INTO blog_posts (id, title, slug, content, excerpt, status, author_id, category_id, reading_time) VALUES 
        (1, 'İlk Blog Yazısı', 'ilk-blog-yazisi', '<p>Bu CMS Pro ile oluşturulmuş ilk blog yazısıdır. Sistem başarıyla kurulmuş ve çalışmaya hazırdır!</p>', 'CMS Pro ile ilk blog yazısı', 'published', 1, 1, 1)");

    echo "✅ Default data inserted successfully!\n";
    echo "\n🎉 CMS Pro installation completed successfully!\n";
    echo "\n📋 Login Information:\n";
    echo "   URL: http://localhost/cms-pro/public/admin\n";
    echo "   Email: <EMAIL>\n";
    echo "   Password: admin123\n";
    echo "\n🌐 Frontend: http://localhost/cms-pro/public/\n";

} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
