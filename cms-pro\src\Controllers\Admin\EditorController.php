<?php

namespace CmsPro\Controllers\Admin;

use CmsPro\Controllers\BaseController;
use CmsPro\Services\EditorService;
use CmsPro\Services\MediaService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * Editor Controller
 * 
 * @package CmsPro\Controllers\Admin
 */
class EditorController extends BaseController
{
    private $editorService;
    private $mediaService;

    public function __construct()
    {
        parent::__construct();
        $this->editorService = new EditorService();
        $this->mediaService = new MediaService();
    }

    /**
     * Get editor configuration
     */
    public function getConfig(Request $request)
    {
        $this->request = $request;

        $fieldType = $request->query->get('type', 'advanced');
        $config = $this->editorService->getEditorConfig($fieldType);

        return new JsonResponse([
            'success' => true,
            'config' => $config
        ]);
    }

    /**
     * Get editor templates
     */
    public function getTemplates(Request $request)
    {
        $this->request = $request;

        $templates = $this->editorService->getTemplates();

        return new JsonResponse([
            'success' => true,
            'templates' => $templates
        ]);
    }

    /**
     * Upload image for editor - SECURITY ENHANCED
     */
    public function uploadImage(Request $request)
    {
        $this->request = $request;

        try {
            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Invalid CSRF token'
                ], 403);
            }

            // Check authentication and permission
            if (!auth()->check()) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Authentication required'
                ], 401);
            }

            if (!auth()->can('media.upload')) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'You do not have permission to upload images.'
                ], 403);
            }

            $file = $request->files->get('file');

            if (!$file instanceof UploadedFile) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'No file uploaded.'
                ], 400);
            }

            // Enhanced file validation
            $validation = $this->validateUploadedFile($file);
            if (!$validation['valid']) {
                return new JsonResponse([
                    'success' => false,
                    'error' => $validation['error']
                ], 400);
            }

            // Rate limiting check
            if (!$this->checkUploadRateLimit()) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Upload rate limit exceeded. Please try again later.'
                ], 429);
            }

            $result = $this->editorService->handleImageUpload($file, [
                'resize' => $request->request->get('resize', true),
                'optimize' => $request->request->get('optimize', true),
                'user_id' => auth()->id(),
                'ip_address' => $request->getClientIp(),
                'user_agent' => $request->headers->get('User-Agent')
            ]);

            if ($result['success']) {
                // Log successful upload
                $this->logSecurityEvent('editor_image_uploaded', [
                    'filename' => $result['filename'] ?? 'unknown',
                    'size' => $result['size'] ?? 0,
                    'user_id' => auth()->id()
                ]);

                return new JsonResponse([
                    'location' => $result['location']
                ]);
            } else {
                return new JsonResponse([
                    'error' => $result['error']
                ], 400);
            }

        } catch (\Exception $e) {
            // Log error for security monitoring
            $this->logSecurityEvent('editor_upload_error', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'ip_address' => $request->getClientIp()
            ]);

            return new JsonResponse([
                'error' => 'Upload failed due to server error'
            ], 500);
        }
    }

    /**
     * Process content before saving
     */
    public function processContent(Request $request)
    {
        $this->request = $request;

        $content = $request->request->get('content', '');
        $options = [
            'validate' => $request->request->get('validate', true),
            'clean' => $request->request->get('clean', true)
        ];

        try {
            $processedContent = $this->editorService->processContent($content, $options);
            $stats = $this->editorService->getContentStats($processedContent);

            return new JsonResponse([
                'success' => true,
                'content' => $processedContent,
                'stats' => $stats
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get content statistics
     */
    public function getContentStats(Request $request)
    {
        $this->request = $request;

        $content = $request->request->get('content', '');
        
        try {
            $stats = $this->editorService->getContentStats($content);

            return new JsonResponse([
                'success' => true,
                'stats' => $stats
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Auto-save content
     */
    public function autoSave(Request $request)
    {
        $this->request = $request;

        // Check permission
        if (!auth()->can('content.edit')) {
            return new JsonResponse([
                'success' => false,
                'error' => 'You do not have permission to save content.'
            ], 403);
        }

        $contentId = $request->request->get('content_id');
        $content = $request->request->get('content', '');
        $fieldName = $request->request->get('field_name', 'content');

        if (!$contentId) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Content ID is required for auto-save.'
            ], 400);
        }

        try {
            // Process content
            $processedContent = $this->editorService->processContent($content);

            // Save to database (auto-save table or draft)
            $this->db->insertOrUpdate('content_auto_saves', [
                'content_id' => $contentId,
                'field_name' => $fieldName,
                'content' => $processedContent,
                'user_id' => auth()->id(),
                'saved_at' => date('Y-m-d H:i:s')
            ], [
                'content_id' => $contentId,
                'field_name' => $fieldName,
                'user_id' => auth()->id()
            ]);

            return new JsonResponse([
                'success' => true,
                'message' => 'Content auto-saved successfully.',
                'saved_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Auto-save failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get auto-saved content
     */
    public function getAutoSaved(Request $request)
    {
        $this->request = $request;

        $contentId = $request->query->get('content_id');
        $fieldName = $request->query->get('field_name', 'content');

        if (!$contentId) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Content ID is required.'
            ], 400);
        }

        try {
            $autoSave = $this->db->selectOne(
                "SELECT * FROM content_auto_saves 
                 WHERE content_id = ? AND field_name = ? AND user_id = ? 
                 ORDER BY saved_at DESC LIMIT 1",
                [$contentId, $fieldName, auth()->id()]
            );

            if ($autoSave) {
                return new JsonResponse([
                    'success' => true,
                    'content' => $autoSave['content'],
                    'saved_at' => $autoSave['saved_at']
                ]);
            } else {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'No auto-saved content found.'
                ], 404);
            }
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to retrieve auto-saved content: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear auto-saved content
     */
    public function clearAutoSaved(Request $request)
    {
        $this->request = $request;

        $contentId = $request->request->get('content_id');
        $fieldName = $request->request->get('field_name', 'content');

        if (!$contentId) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Content ID is required.'
            ], 400);
        }

        try {
            $this->db->delete('content_auto_saves', 
                'content_id = ? AND field_name = ? AND user_id = ?',
                [$contentId, $fieldName, auth()->id()]
            );

            return new JsonResponse([
                'success' => true,
                'message' => 'Auto-saved content cleared.'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to clear auto-saved content: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Spell check content
     */
    public function spellCheck(Request $request)
    {
        $this->request = $request;

        $content = $request->request->get('content', '');
        $language = $request->request->get('language', 'en');

        // This would integrate with a spell checking service
        // For now, return a placeholder response
        return new JsonResponse([
            'success' => true,
            'suggestions' => [],
            'message' => 'Spell check completed. No errors found.'
        ]);
    }

    /**
     * Get word count and reading time
     */
    public function getWordCount(Request $request)
    {
        $this->request = $request;

        $content = $request->request->get('content', '');
        
        try {
            $stats = $this->editorService->getContentStats($content);

            return new JsonResponse([
                'success' => true,
                'word_count' => $stats['word_count'],
                'char_count' => $stats['char_count'],
                'reading_time' => $stats['reading_time']
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Preview content
     */
    public function preview(Request $request)
    {
        $this->request = $request;

        $content = $request->request->get('content', '');
        
        try {
            $processedContent = $this->editorService->processContent($content);

            $data = [
                'title' => 'Content Preview',
                'content' => $processedContent
            ];

            return $this->view('admin/editor/preview.twig', $data);
        } catch (\Exception $e) {
            return $this->view('admin/editor/preview-error.twig', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Export content to different formats
     */
    public function export(Request $request)
    {
        $this->request = $request;

        $content = $request->request->get('content', '');
        $format = $request->request->get('format', 'html');

        try {
            switch ($format) {
                case 'html':
                    $exported = $this->editorService->processContent($content);
                    $contentType = 'text/html';
                    $extension = 'html';
                    break;

                case 'text':
                    $exported = strip_tags($content);
                    $contentType = 'text/plain';
                    $extension = 'txt';
                    break;

                case 'markdown':
                    // This would need a HTML to Markdown converter
                    $exported = $this->convertToMarkdown($content);
                    $contentType = 'text/markdown';
                    $extension = 'md';
                    break;

                default:
                    throw new \InvalidArgumentException('Unsupported export format: ' . $format);
            }

            $filename = 'content_' . date('Y-m-d_H-i-s') . '.' . $extension;

            return new JsonResponse([
                'success' => true,
                'content' => $exported,
                'filename' => $filename,
                'content_type' => $contentType
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Convert HTML to Markdown (basic implementation)
     */
    private function convertToMarkdown($html)
    {
        // Basic HTML to Markdown conversion
        $markdown = $html;
        
        // Headers
        $markdown = preg_replace('/<h1[^>]*>(.*?)<\/h1>/i', '# $1', $markdown);
        $markdown = preg_replace('/<h2[^>]*>(.*?)<\/h2>/i', '## $1', $markdown);
        $markdown = preg_replace('/<h3[^>]*>(.*?)<\/h3>/i', '### $1', $markdown);
        $markdown = preg_replace('/<h4[^>]*>(.*?)<\/h4>/i', '#### $1', $markdown);
        $markdown = preg_replace('/<h5[^>]*>(.*?)<\/h5>/i', '##### $1', $markdown);
        $markdown = preg_replace('/<h6[^>]*>(.*?)<\/h6>/i', '###### $1', $markdown);
        
        // Bold and italic
        $markdown = preg_replace('/<strong[^>]*>(.*?)<\/strong>/i', '**$1**', $markdown);
        $markdown = preg_replace('/<b[^>]*>(.*?)<\/b>/i', '**$1**', $markdown);
        $markdown = preg_replace('/<em[^>]*>(.*?)<\/em>/i', '*$1*', $markdown);
        $markdown = preg_replace('/<i[^>]*>(.*?)<\/i>/i', '*$1*', $markdown);
        
        // Links
        $markdown = preg_replace('/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/i', '[$2]($1)', $markdown);
        
        // Images
        $markdown = preg_replace('/<img[^>]*src="([^"]*)"[^>]*alt="([^"]*)"[^>]*>/i', '![$2]($1)', $markdown);
        
        // Paragraphs
        $markdown = preg_replace('/<p[^>]*>(.*?)<\/p>/i', '$1' . "\n\n", $markdown);
        
        // Line breaks
        $markdown = str_replace('<br>', "\n", $markdown);
        $markdown = str_replace('<br/>', "\n", $markdown);
        $markdown = str_replace('<br />', "\n", $markdown);
        
        // Remove remaining HTML tags
        $markdown = strip_tags($markdown);
        
        // Clean up extra whitespace
        $markdown = preg_replace('/\n{3,}/', "\n\n", $markdown);
        $markdown = trim($markdown);
        
        return $markdown;
    }

    /**
     * Get available custom fields for insertion
     */
    public function getCustomFields(Request $request)
    {
        $this->request = $request;

        try {
            $fields = $this->db->select(
                "SELECT slug, name, type, description 
                 FROM fields 
                 WHERE status = 'active' 
                 ORDER BY name ASC"
            );

            return new JsonResponse([
                'success' => true,
                'fields' => $fields
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to retrieve custom fields: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate uploaded file for security
     */
    private function validateUploadedFile(UploadedFile $file)
    {
        // Check if file was uploaded successfully
        if (!$file->isValid()) {
            return [
                'valid' => false,
                'error' => 'File upload failed: ' . $file->getErrorMessage()
            ];
        }

        // Check file size (5MB limit)
        if ($file->getSize() > 5242880) {
            return [
                'valid' => false,
                'error' => 'File size exceeds 5MB limit'
            ];
        }

        // Check MIME type
        $allowedMimeTypes = [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/webp'
        ];

        if (!in_array($file->getMimeType(), $allowedMimeTypes)) {
            return [
                'valid' => false,
                'error' => 'Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'
            ];
        }

        // Check file extension
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $extension = strtolower($file->getClientOriginalExtension());

        if (!in_array($extension, $allowedExtensions)) {
            return [
                'valid' => false,
                'error' => 'Invalid file extension'
            ];
        }

        // Check filename for suspicious patterns
        $filename = $file->getClientOriginalName();
        $suspiciousPatterns = [
            '/\.php$/i',
            '/\.asp$/i',
            '/\.jsp$/i',
            '/\.exe$/i',
            '/\.bat$/i',
            '/\.cmd$/i',
            '/\.scr$/i',
            '/\.com$/i',
            '/\.pif$/i'
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $filename)) {
                return [
                    'valid' => false,
                    'error' => 'Suspicious filename detected'
                ];
            }
        }

        // Validate image content
        if (!$this->validateImageContent($file)) {
            return [
                'valid' => false,
                'error' => 'Invalid image content'
            ];
        }

        return ['valid' => true];
    }

    /**
     * Validate image content to prevent malicious files
     */
    private function validateImageContent(UploadedFile $file)
    {
        // Get image info
        $imageInfo = getimagesize($file->getPathname());

        if ($imageInfo === false) {
            return false;
        }

        // Check if it's a valid image type
        $validImageTypes = [IMAGETYPE_JPEG, IMAGETYPE_PNG, IMAGETYPE_GIF, IMAGETYPE_WEBP];

        if (!in_array($imageInfo[2], $validImageTypes)) {
            return false;
        }

        // Additional security check: try to create image resource
        try {
            switch ($imageInfo[2]) {
                case IMAGETYPE_JPEG:
                    $image = imagecreatefromjpeg($file->getPathname());
                    break;
                case IMAGETYPE_PNG:
                    $image = imagecreatefrompng($file->getPathname());
                    break;
                case IMAGETYPE_GIF:
                    $image = imagecreatefromgif($file->getPathname());
                    break;
                case IMAGETYPE_WEBP:
                    if (function_exists('imagecreatefromwebp')) {
                        $image = imagecreatefromwebp($file->getPathname());
                    } else {
                        return false;
                    }
                    break;
                default:
                    return false;
            }

            if ($image === false) {
                return false;
            }

            imagedestroy($image);
            return true;

        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Check upload rate limit
     */
    private function checkUploadRateLimit()
    {
        $userId = auth()->id();
        $cacheKey = "upload_rate_limit_{$userId}";

        // Simple rate limiting: max 10 uploads per minute
        $uploads = cache()->get($cacheKey, 0);

        if ($uploads >= 10) {
            return false;
        }

        cache()->put($cacheKey, $uploads + 1, 60); // 60 seconds
        return true;
    }

    /**
     * Validate CSRF token
     */
    private function validateCsrfToken(Request $request)
    {
        $token = $request->request->get('_token') ?: $request->headers->get('X-CSRF-TOKEN');

        if (!$token) {
            return false;
        }

        // Use session to validate CSRF token
        $sessionToken = session()->get('_token');
        return hash_equals($sessionToken, $token);
    }

    /**
     * Log security events
     */
    private function logSecurityEvent($event, $data = [])
    {
        try {
            $logData = array_merge([
                'event' => $event,
                'timestamp' => date('Y-m-d H:i:s'),
                'ip_address' => request()->getClientIp(),
                'user_agent' => request()->headers->get('User-Agent')
            ], $data);

            // Log to security log file
            error_log(json_encode($logData), 3, storage_path('logs/security.log'));

        } catch (\Exception $e) {
            // Fail silently to not break the main functionality
            error_log('Failed to log security event: ' . $e->getMessage());
        }
    }
}
