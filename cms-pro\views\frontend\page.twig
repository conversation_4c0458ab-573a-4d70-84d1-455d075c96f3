{% extends "layouts/frontend.twig" %}

{% block content %}
<!-- <PERSON> Header -->
<section class="page-header py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold">{{ page.title }}</h1>
                {% if page.excerpt %}
                <p class="lead text-muted mt-3">{{ page.excerpt }}</p>
                {% endif %}
                
                <!-- Page Meta -->
                <div class="page-meta mt-4">
                    <span class="badge bg-primary me-2">
                        <i class="fas fa-calendar me-1"></i>
                        {{ page.created_at | date(site_settings.date_format) }}
                    </span>
                    {% if page.updated_at != page.created_at %}
                    <span class="badge bg-secondary me-2">
                        <i class="fas fa-edit me-1"></i>
                        {{ __('Updated') }} {{ page.updated_at | date(site_settings.date_format) }}
                    </span>
                    {% endif %}
                    {% if page.views > 0 %}
                    <span class="badge bg-info">
                        <i class="fas fa-eye me-1"></i>
                        {{ page.views }} {{ __('views') }}
                    </span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Page Content -->
<section class="page-content py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <!-- Featured Image -->
                {% if page.featured_image %}
                <div class="featured-image mb-5">
                    <img src="{{ page.featured_image }}" alt="{{ page.title }}" 
                         class="img-fluid rounded shadow-sm w-100" 
                         style="max-height: 400px; object-fit: cover;">
                </div>
                {% endif %}
                
                <!-- Page Content -->
                <div class="content-body">
                    {{ page.content | raw }}
                </div>
                
                <!-- Page Tags -->
                {% if page.tags and page.tags|length > 0 %}
                <div class="page-tags mt-5 pt-4 border-top">
                    <h6 class="mb-3">{{ __('Tags') }}:</h6>
                    {% for tag in page.tags %}
                    <span class="badge bg-outline-primary me-2 mb-2">
                        <i class="fas fa-tag me-1"></i>{{ tag }}
                    </span>
                    {% endfor %}
                </div>
                {% endif %}
                
                <!-- Share Buttons -->
                <div class="share-buttons mt-5 pt-4 border-top">
                    <h6 class="mb-3">{{ __('Share this page') }}:</h6>
                    <div class="d-flex flex-wrap gap-2">
                        <a href="https://www.facebook.com/sharer/sharer.php?u={{ seo.canonical_url | url_encode }}" 
                           target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fab fa-facebook-f me-1"></i>Facebook
                        </a>
                        <a href="https://twitter.com/intent/tweet?url={{ seo.canonical_url | url_encode }}&text={{ page.title | url_encode }}" 
                           target="_blank" class="btn btn-outline-info btn-sm">
                            <i class="fab fa-twitter me-1"></i>Twitter
                        </a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ seo.canonical_url | url_encode }}" 
                           target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fab fa-linkedin-in me-1"></i>LinkedIn
                        </a>
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="copy-link-btn">
                            <i class="fas fa-link me-1"></i>{{ __('Copy Link') }}
                        </button>
                    </div>
                </div>
                
                <!-- Navigation -->
                <div class="page-navigation mt-5 pt-4 border-top">
                    <div class="row">
                        <div class="col-md-6">
                            {% if previous_page %}
                            <a href="{{ url('/' ~ previous_page.slug) }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>{{ __('Previous') }}
                                <br><small class="text-muted">{{ previous_page.title }}</small>
                            </a>
                            {% endif %}
                        </div>
                        <div class="col-md-6 text-md-end">
                            {% if next_page %}
                            <a href="{{ url('/' ~ next_page.slug) }}" class="btn btn-outline-secondary">
                                {{ __('Next') }}<i class="fas fa-arrow-right ms-2"></i>
                                <br><small class="text-muted">{{ next_page.title }}</small>
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="sidebar">
                    <!-- Table of Contents -->
                    <div class="toc-widget mb-4" id="toc-widget" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-list me-2"></i>{{ __('Table of Contents') }}
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="toc-content"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Related Pages -->
                    {% if related_pages and related_pages|length > 0 %}
                    <div class="related-pages-widget mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-file-alt me-2"></i>{{ __('Related Pages') }}
                                </h6>
                            </div>
                            <div class="card-body">
                                {% for related_page in related_pages %}
                                <div class="related-page-item mb-3">
                                    <h6 class="mb-1">
                                        <a href="{{ url('/' ~ related_page.slug) }}" class="text-decoration-none">
                                            {{ related_page.title }}
                                        </a>
                                    </h6>
                                    {% if related_page.excerpt %}
                                    <p class="text-muted small mb-1">
                                        {{ related_page.excerpt | slice(0, 100) }}...
                                    </p>
                                    {% endif %}
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ related_page.created_at | date(site_settings.date_format) }}
                                    </small>
                                </div>
                                {% if not loop.last %}<hr class="my-3">{% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Quick Contact -->
                    <div class="quick-contact-widget">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h6 class="mb-3">{{ __('Need Help?') }}</h6>
                                <p class="mb-3">{{ __('Get in touch with our team for support.') }}</p>
                                <a href="{{ url('/contact') }}" class="btn btn-light">
                                    <i class="fas fa-envelope me-2"></i>{{ __('Contact Us') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Generate Table of Contents
    generateTableOfContents();
    
    // Copy link functionality
    const copyLinkBtn = document.getElementById('copy-link-btn');
    if (copyLinkBtn) {
        copyLinkBtn.addEventListener('click', function() {
            navigator.clipboard.writeText(window.location.href).then(function() {
                // Show success feedback
                const originalText = copyLinkBtn.innerHTML;
                copyLinkBtn.innerHTML = '<i class="fas fa-check me-1"></i>{{ __("Copied!") }}';
                copyLinkBtn.classList.remove('btn-outline-secondary');
                copyLinkBtn.classList.add('btn-success');
                
                setTimeout(function() {
                    copyLinkBtn.innerHTML = originalText;
                    copyLinkBtn.classList.remove('btn-success');
                    copyLinkBtn.classList.add('btn-outline-secondary');
                }, 2000);
            });
        });
    }
    
    // Smooth scrolling for TOC links
    document.addEventListener('click', function(e) {
        if (e.target.matches('.toc-link')) {
            e.preventDefault();
            const target = document.querySelector(e.target.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }
    });
    
    // Highlight current section in TOC
    highlightCurrentSection();
});

function generateTableOfContents() {
    const contentBody = document.querySelector('.content-body');
    const tocWidget = document.getElementById('toc-widget');
    const tocContent = document.getElementById('toc-content');
    
    if (!contentBody || !tocWidget || !tocContent) return;
    
    const headings = contentBody.querySelectorAll('h1, h2, h3, h4, h5, h6');
    
    if (headings.length === 0) return;
    
    let tocHtml = '<ul class="list-unstyled">';
    
    headings.forEach((heading, index) => {
        const id = heading.id || `heading-${index}`;
        heading.id = id;
        
        const level = parseInt(heading.tagName.charAt(1));
        const indent = (level - 1) * 15;
        
        tocHtml += `
            <li style="margin-left: ${indent}px; margin-bottom: 5px;">
                <a href="#${id}" class="toc-link text-decoration-none">
                    ${heading.textContent}
                </a>
            </li>
        `;
    });
    
    tocHtml += '</ul>';
    tocContent.innerHTML = tocHtml;
    tocWidget.style.display = 'block';
}

function highlightCurrentSection() {
    const tocLinks = document.querySelectorAll('.toc-link');
    const headings = document.querySelectorAll('.content-body h1, .content-body h2, .content-body h3, .content-body h4, .content-body h5, .content-body h6');
    
    if (tocLinks.length === 0 || headings.length === 0) return;
    
    function updateActiveLink() {
        let current = '';
        
        headings.forEach(heading => {
            const rect = heading.getBoundingClientRect();
            if (rect.top <= 100) {
                current = heading.id;
            }
        });
        
        tocLinks.forEach(link => {
            link.classList.remove('text-primary', 'fw-bold');
            if (link.getAttribute('href') === `#${current}`) {
                link.classList.add('text-primary', 'fw-bold');
            }
        });
    }
    
    window.addEventListener('scroll', updateActiveLink);
    updateActiveLink(); // Initial call
}

// Reading progress indicator
function addReadingProgress() {
    const progressBar = document.createElement('div');
    progressBar.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 0%;
        height: 3px;
        background: linear-gradient(to right, #007bff, #17a2b8);
        z-index: 9999;
        transition: width 0.3s ease;
    `;
    document.body.appendChild(progressBar);
    
    window.addEventListener('scroll', function() {
        const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
        const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
        const scrolled = (winScroll / height) * 100;
        progressBar.style.width = scrolled + '%';
    });
}

// Initialize reading progress
addReadingProgress();
</script>

<style>
/* Page specific styles */
.page-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.content-body {
    font-size: 1.1rem;
    line-height: 1.8;
}

.content-body h1,
.content-body h2,
.content-body h3,
.content-body h4,
.content-body h5,
.content-body h6 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    scroll-margin-top: 100px;
}

.content-body p {
    margin-bottom: 1.5rem;
}

.content-body img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin: 1.5rem 0;
}

.content-body blockquote {
    border-left: 4px solid #007bff;
    padding-left: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    color: #6c757d;
}

.content-body code {
    background-color: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.9em;
}

.content-body pre {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 1.5rem 0;
}

.sidebar {
    position: sticky;
    top: 2rem;
}

.toc-link {
    color: #6c757d;
    font-size: 0.9rem;
    transition: color 0.2s ease;
}

.toc-link:hover {
    color: #007bff;
}

.related-page-item:last-child {
    margin-bottom: 0 !important;
}

.share-buttons .btn {
    min-width: 100px;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .sidebar {
        position: static;
        margin-top: 3rem;
    }
    
    .page-navigation .btn {
        width: 100%;
        margin-bottom: 1rem;
    }
}

/* Dark mode support */
[data-bs-theme="dark"] .page-header {
    background: linear-gradient(135deg, #2d3748, #4a5568);
}

[data-bs-theme="dark"] .content-body blockquote {
    color: #a0aec0;
    border-left-color: #60a5fa;
}

[data-bs-theme="dark"] .content-body code,
[data-bs-theme="dark"] .content-body pre {
    background-color: #374151;
    color: #e5e7eb;
}
</style>
{% endblock %}
