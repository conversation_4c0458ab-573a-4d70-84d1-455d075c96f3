-- Insert default field types
INSERT INTO `field_types` (`name`, `slug`, `description`, `icon`, `component`, `validation_rules`, `default_settings`, `sort_order`) VALUES

-- Text Fields
('Text Input', 'text', 'Single line text input field', 'fas fa-font', 'TextInput', 
'{"max_length": 255, "min_length": 0}', 
'{"placeholder": "", "max_length": 255, "autocomplete": true}', 1),

('Textarea', 'textarea', 'Multi-line text input field', 'fas fa-align-left', 'Textarea', 
'{"max_length": 5000, "min_length": 0}', 
'{"placeholder": "", "rows": 4, "max_length": 5000, "resize": true}', 2),

('Rich Text Editor', 'wysiwyg', 'WYSIWYG HTML editor with formatting tools', 'fas fa-edit', 'WysiwygEditor', 
'{"max_length": 50000}', 
'{"toolbar": "full", "height": 300, "plugins": ["link", "image", "table", "code"]}', 3),

('Password', 'password', 'Password input field with masking', 'fas fa-lock', 'PasswordInput', 
'{"min_length": 8, "max_length": 255}', 
'{"placeholder": "", "show_strength": true, "confirm_field": false}', 4),

-- Number Fields
('Number', 'number', 'Numeric input field', 'fas fa-hashtag', 'NumberInput', 
'{"min": null, "max": null, "step": 1}', 
'{"placeholder": "", "min": null, "max": null, "step": 1, "decimal_places": 0}', 5),

('Range Slider', 'range', 'Range slider for numeric values', 'fas fa-sliders-h', 'RangeSlider', 
'{"min": 0, "max": 100, "step": 1}', 
'{"min": 0, "max": 100, "step": 1, "show_value": true, "unit": ""}', 6),

-- Selection Fields
('Select Dropdown', 'select', 'Dropdown selection field', 'fas fa-caret-down', 'SelectDropdown', 
'{"required_options": 1}', 
'{"multiple": false, "searchable": false, "placeholder": "Choose an option"}', 7),

('Multi-Select', 'multiselect', 'Multiple selection dropdown', 'fas fa-list-ul', 'MultiSelect', 
'{"min_selections": 0, "max_selections": null}', 
'{"searchable": true, "placeholder": "Choose options", "max_selections": null}', 8),

('Radio Buttons', 'radio', 'Radio button group for single selection', 'fas fa-dot-circle', 'RadioGroup', 
'{"required_options": 2}', 
'{"layout": "vertical", "inline": false}', 9),

('Checkboxes', 'checkbox', 'Checkbox group for multiple selections', 'fas fa-check-square', 'CheckboxGroup', 
'{"min_selections": 0, "max_selections": null}', 
'{"layout": "vertical", "inline": false, "select_all": false}', 10),

('Single Checkbox', 'boolean', 'Single checkbox for yes/no values', 'fas fa-check', 'BooleanCheckbox', 
'{}', 
'{"label_position": "right", "default_checked": false}', 11),

-- Date and Time Fields
('Date Picker', 'date', 'Date selection field', 'fas fa-calendar-alt', 'DatePicker', 
'{"min_date": null, "max_date": null}', 
'{"format": "Y-m-d", "placeholder": "Select date", "show_today": true, "min_date": null, "max_date": null}', 12),

('Time Picker', 'time', 'Time selection field', 'fas fa-clock', 'TimePicker', 
'{"format": "24h"}', 
'{"format": "24h", "placeholder": "Select time", "step": 15}', 13),

('Date Time Picker', 'datetime', 'Date and time selection field', 'fas fa-calendar-check', 'DateTimePicker', 
'{"min_date": null, "max_date": null}', 
'{"date_format": "Y-m-d", "time_format": "H:i", "placeholder": "Select date and time"}', 14),

-- File and Media Fields
('File Upload', 'file', 'File upload field', 'fas fa-upload', 'FileUpload', 
'{"max_size": "10MB", "allowed_types": ["pdf", "doc", "docx", "txt"]}', 
'{"max_size": "10MB", "multiple": false, "allowed_types": ["pdf", "doc", "docx", "txt"], "show_preview": true}', 15),

('Image Upload', 'image', 'Image upload field with preview', 'fas fa-image', 'ImageUpload', 
'{"max_size": "5MB", "allowed_types": ["jpg", "jpeg", "png", "gif", "webp"]}', 
'{"max_size": "5MB", "multiple": false, "allowed_types": ["jpg", "jpeg", "png", "gif", "webp"], "max_width": 1920, "max_height": 1080, "crop": false}', 16),

('Gallery', 'gallery', 'Multiple image upload with gallery view', 'fas fa-images', 'ImageGallery', 
'{"max_images": 10, "max_size": "5MB"}', 
'{"max_images": 10, "max_size": "5MB", "sortable": true, "crop": false, "thumbnail_size": 150}', 17),

-- Advanced Fields
('Color Picker', 'color', 'Color selection field', 'fas fa-palette', 'ColorPicker', 
'{}', 
'{"format": "hex", "show_alpha": false, "preset_colors": ["#FF0000", "#00FF00", "#0000FF"]}', 18),

('URL Field', 'url', 'URL input field with validation', 'fas fa-link', 'UrlInput', 
'{"protocols": ["http", "https"]}', 
'{"placeholder": "https://example.com", "protocols": ["http", "https"], "show_preview": true}', 19),

('Email Field', 'email', 'Email input field with validation', 'fas fa-envelope', 'EmailInput', 
'{}', 
'{"placeholder": "<EMAIL>", "multiple": false}', 20),

('Phone Field', 'phone', 'Phone number input field', 'fas fa-phone', 'PhoneInput', 
'{}', 
'{"placeholder": "+****************", "format": "international", "country_code": "US"}', 21),

-- Layout and Organization Fields
('Heading', 'heading', 'Section heading for form organization', 'fas fa-heading', 'FormHeading', 
'{}', 
'{"level": "h3", "style": "default", "divider": true}', 22),

('Separator', 'separator', 'Visual separator line', 'fas fa-minus', 'FormSeparator', 
'{}', 
'{"style": "solid", "margin": "medium"}', 23),

('HTML Block', 'html', 'Custom HTML content block', 'fas fa-code', 'HtmlBlock', 
'{}', 
'{"content": "", "allow_scripts": false}', 24),

-- Relationship Fields
('Content Relation', 'content_relation', 'Relationship to other content', 'fas fa-link', 'ContentRelation', 
'{"content_types": [], "min_items": 0, "max_items": null}', 
'{"content_types": ["page", "post"], "multiple": false, "searchable": true, "display_field": "title"}', 25),

('User Relation', 'user_relation', 'Relationship to users', 'fas fa-user', 'UserRelation', 
'{"roles": [], "min_items": 0, "max_items": null}', 
'{"roles": [], "multiple": false, "searchable": true, "display_field": "name"}', 26),

-- Repeater Fields
('Repeater', 'repeater', 'Repeatable group of fields', 'fas fa-copy', 'FieldRepeater', 
'{"min_items": 0, "max_items": null}', 
'{"min_items": 0, "max_items": 10, "layout": "block", "button_label": "Add Item", "sortable": true}', 27),

('Flexible Content', 'flexible', 'Flexible content layouts', 'fas fa-th-large', 'FlexibleContent', 
'{"layouts": [], "min_items": 0, "max_items": null}', 
'{"layouts": [], "min_items": 0, "max_items": null, "button_label": "Add Content"}', 28);

-- Insert default content categories
INSERT INTO `content_categories` (`name`, `slug`, `description`, `color`, `icon`, `sort_order`) VALUES
('General', 'general', 'General content category', '#6c757d', 'fas fa-folder', 1),
('News', 'news', 'News and announcements', '#007bff', 'fas fa-newspaper', 2),
('Blog', 'blog', 'Blog posts and articles', '#28a745', 'fas fa-blog', 3),
('Pages', 'pages', 'Static pages', '#17a2b8', 'fas fa-file-alt', 4),
('Products', 'products', 'Product information', '#ffc107', 'fas fa-box', 5),
('Services', 'services', 'Service descriptions', '#dc3545', 'fas fa-cogs', 6);

-- Insert default content tags
INSERT INTO `content_tags` (`name`, `slug`, `description`, `color`) VALUES
('Featured', 'featured', 'Featured content', '#ff6b6b'),
('Popular', 'popular', 'Popular content', '#4ecdc4'),
('New', 'new', 'New content', '#45b7d1'),
('Important', 'important', 'Important content', '#f9ca24'),
('Archive', 'archive', 'Archived content', '#6c5ce7'),
('Draft', 'draft', 'Draft content', '#a0a0a0');
