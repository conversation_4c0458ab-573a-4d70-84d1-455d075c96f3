<?php

/**
 * Web Routes
 * 
 * Frontend routes for the CMS
 */

use CmsPro\Core\Application;

$app = Application::getInstance();
$router = $app->getRouter();

// Home page
$router->get('/', 'CmsPro\Controllers\Frontend\HomeController@index', 'home');

// Pages
$router->get('/page/{slug}', 'CmsPro\Controllers\Frontend\PageController@show', 'page.show');

// Blog
$router->get('/blog', 'CmsPro\Controllers\Frontend\BlogController@index', 'blog.index');
$router->get('/blog/{slug}', 'CmsPro\Controllers\Frontend\BlogController@show', 'blog.show');
$router->get('/blog/category/{slug}', 'CmsPro\Controllers\Frontend\BlogController@category', 'blog.category');
$router->get('/blog/tag/{slug}', 'CmsPro\Controllers\Frontend\BlogController@tag', 'blog.tag');

// Search
$router->get('/search', 'CmsPro\Controllers\Frontend\SearchController@index', 'search');

// Contact
$router->get('/contact', 'CmsPro\Controllers\Frontend\ContactController@index', 'contact');
$router->post('/contact', 'CmsPro\Controllers\Frontend\ContactController@submit', 'contact.submit');

// Language switching
$router->get('/lang/{locale}', 'CmsPro\Controllers\Frontend\LanguageController@switch', 'language.switch');

// Sitemap
$router->get('/sitemap.xml', 'CmsPro\Controllers\Frontend\SitemapController@xml', 'sitemap.xml');

// RSS Feed
$router->get('/feed', 'CmsPro\Controllers\Frontend\FeedController@rss', 'feed.rss');

// Authentication routes (if public registration is enabled)
$router->get('/login', 'CmsPro\Controllers\Auth\LoginController@showLoginForm', 'login');
$router->post('/login', 'CmsPro\Controllers\Auth\LoginController@login', 'login.submit');
$router->post('/logout', 'CmsPro\Controllers\Auth\LoginController@logout', 'logout');

$router->get('/register', 'CmsPro\Controllers\Auth\RegisterController@showRegistrationForm', 'register');
$router->post('/register', 'CmsPro\Controllers\Auth\RegisterController@register', 'register.submit');

// Password Reset
$router->get('/password/reset', 'CmsPro\Controllers\Auth\ForgotPasswordController@showLinkRequestForm', 'password.request');
$router->post('/password/email', 'CmsPro\Controllers\Auth\ForgotPasswordController@sendResetLinkEmail', 'password.email');
$router->get('/password/reset/{token}', 'CmsPro\Controllers\Auth\ResetPasswordController@showResetForm', 'password.reset');
$router->post('/password/reset', 'CmsPro\Controllers\Auth\ResetPasswordController@reset', 'password.update');

// Two Factor Authentication
$router->get('/2fa', 'CmsPro\Controllers\Auth\TwoFactorController@show', '2fa.show');
$router->post('/2fa', 'CmsPro\Controllers\Auth\TwoFactorController@verify', '2fa.verify');

// File downloads (protected)
$router->get('/download/{file}', 'CmsPro\Controllers\Frontend\DownloadController@download', 'download');

// Dynamic content routes (will be populated from database)
// These routes are registered dynamically based on custom content types
