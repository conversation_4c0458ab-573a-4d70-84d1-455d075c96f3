<?php

namespace CmsPro\Services;

use CmsPro\Core\Database;
use CmsPro\Models\Page;

/**
 * SEO Service
 * 
 * @package CmsPro\Services
 */
class SeoService
{
    private $db;
    private $languageService;
    private $settingsService;

    public function __construct(Database $db = null, LanguageService $languageService = null)
    {
        $this->db = $db ?: app()->getDatabase();
        $this->languageService = $languageService ?: new LanguageService();
        $this->settingsService = new SettingsService();
    }

    /**
     * Generate SEO-friendly slug
     */
    public function generateSlug($title, $entityType = 'content', $entityId = null, $languageCode = null)
    {
        $languageCode = $languageCode ?: $this->languageService->getCurrentLanguage();
        
        // Basic slug generation
        $slug = $this->slugify($title);
        
        // Ensure uniqueness
        $originalSlug = $slug;
        $counter = 1;
        
        while ($this->slugExists($slug, $entityType, $entityId, $languageCode)) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }

    /**
     * Check if slug exists
     */
    private function slugExists($slug, $entityType, $excludeId = null, $languageCode = null)
    {
        $languageCode = $languageCode ?: $this->languageService->getCurrentLanguage();
        $defaultLanguage = $this->languageService->getDefaultLanguage();

        if ($languageCode === $defaultLanguage) {
            // Check in main content table
            $whereClause = "slug = ? AND content_type = ?";
            $params = [$slug, $entityType];
            
            if ($excludeId) {
                $whereClause .= " AND id != ?";
                $params[] = $excludeId;
            }
            
            $exists = $this->db->selectValue(
                "SELECT COUNT(*) FROM content WHERE {$whereClause}",
                $params
            );
        } else {
            // Check in translations table
            $whereClause = "slug = ? AND language_code = ?";
            $params = [$slug, $languageCode];
            
            if ($excludeId) {
                $whereClause .= " AND content_id != ?";
                $params[] = $excludeId;
            }
            
            $exists = $this->db->selectValue(
                "SELECT COUNT(*) FROM content_translations WHERE {$whereClause}",
                $params
            );
        }
        
        return $exists > 0;
    }

    /**
     * Slugify text
     */
    private function slugify($text)
    {
        // Convert to lowercase
        $text = mb_strtolower($text, 'UTF-8');
        
        // Replace Turkish characters
        $text = str_replace(['ç', 'ğ', 'ı', 'ö', 'ş', 'ü'], ['c', 'g', 'i', 'o', 's', 'u'], $text);
        
        // Replace other special characters
        $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
        
        // Replace spaces and multiple hyphens with single hyphen
        $text = preg_replace('/[\s-]+/', '-', $text);
        
        // Trim hyphens from start and end
        return trim($text, '-');
    }

    /**
     * Generate meta description from content
     */
    public function generateMetaDescription($content, $maxLength = 160)
    {
        // Strip HTML tags
        $text = strip_tags($content);
        
        // Remove extra whitespace
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);
        
        // Truncate to max length
        if (mb_strlen($text) > $maxLength) {
            $text = mb_substr($text, 0, $maxLength - 3) . '...';
        }
        
        return $text;
    }

    /**
     * Extract keywords from content
     */
    public function extractKeywords($content, $maxKeywords = 10)
    {
        // Strip HTML tags
        $text = strip_tags($content);
        
        // Convert to lowercase
        $text = mb_strtolower($text, 'UTF-8');
        
        // Remove punctuation
        $text = preg_replace('/[^\w\s]/', ' ', $text);
        
        // Split into words
        $words = preg_split('/\s+/', $text);
        
        // Remove common stop words
        $stopWords = $this->getStopWords();
        $words = array_filter($words, function($word) use ($stopWords) {
            return !in_array($word, $stopWords) && mb_strlen($word) > 2;
        });
        
        // Count word frequency
        $wordCount = array_count_values($words);
        
        // Sort by frequency
        arsort($wordCount);
        
        // Return top keywords
        return array_slice(array_keys($wordCount), 0, $maxKeywords);
    }

    /**
     * Get stop words for current language
     */
    private function getStopWords()
    {
        $language = $this->languageService->getCurrentLanguage();
        
        $stopWords = [
            'en' => ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'],
            'tr' => ['ve', 'ile', 'bir', 'bu', 'şu', 'o', 'da', 'de', 'ta', 've', 'ki', 'mi', 'mu', 'mü', 'mı', 'için', 'gibi', 'kadar', 'daha', 'en', 'çok', 'az', 'var', 'yok', 'olan', 'olarak', 'ama', 'fakat', 'ancak', 'lakin', 'veya', 'yahut', 'hem', 'ne', 'nasıl', 'neden', 'niçin', 'nerede', 'ne zaman', 'hangi', 'kim', 'kime', 'kimi', 'kimin']
        ];
        
        return $stopWords[$language] ?? $stopWords['en'];
    }

    /**
     * Analyze content SEO
     */
    public function analyzeContentSeo($content, $title = '', $metaDescription = '', $targetKeyword = '')
    {
        $analysis = [
            'score' => 0,
            'issues' => [],
            'recommendations' => [],
            'metrics' => []
        ];

        // Content length analysis
        $contentLength = str_word_count(strip_tags($content));
        $analysis['metrics']['content_length'] = $contentLength;
        
        if ($contentLength < 300) {
            $analysis['issues'][] = 'Content is too short (less than 300 words)';
        } elseif ($contentLength > 2000) {
            $analysis['recommendations'][] = 'Consider breaking long content into multiple pages';
        } else {
            $analysis['score'] += 20;
        }

        // Title analysis
        $titleLength = mb_strlen($title);
        $analysis['metrics']['title_length'] = $titleLength;
        
        if (empty($title)) {
            $analysis['issues'][] = 'Title is missing';
        } elseif ($titleLength < 30) {
            $analysis['issues'][] = 'Title is too short (less than 30 characters)';
        } elseif ($titleLength > 60) {
            $analysis['issues'][] = 'Title is too long (more than 60 characters)';
        } else {
            $analysis['score'] += 20;
        }

        // Meta description analysis
        $metaLength = mb_strlen($metaDescription);
        $analysis['metrics']['meta_description_length'] = $metaLength;
        
        if (empty($metaDescription)) {
            $analysis['issues'][] = 'Meta description is missing';
        } elseif ($metaLength < 120) {
            $analysis['issues'][] = 'Meta description is too short (less than 120 characters)';
        } elseif ($metaLength > 160) {
            $analysis['issues'][] = 'Meta description is too long (more than 160 characters)';
        } else {
            $analysis['score'] += 20;
        }

        // Keyword analysis
        if (!empty($targetKeyword)) {
            $keywordDensity = $this->calculateKeywordDensity($content, $targetKeyword);
            $analysis['metrics']['keyword_density'] = $keywordDensity;
            
            if ($keywordDensity < 0.5) {
                $analysis['recommendations'][] = 'Consider using the target keyword more frequently';
            } elseif ($keywordDensity > 3) {
                $analysis['issues'][] = 'Keyword density is too high (keyword stuffing)';
            } else {
                $analysis['score'] += 15;
            }

            // Check keyword in title
            if (stripos($title, $targetKeyword) !== false) {
                $analysis['score'] += 10;
            } else {
                $analysis['recommendations'][] = 'Include target keyword in title';
            }

            // Check keyword in meta description
            if (stripos($metaDescription, $targetKeyword) !== false) {
                $analysis['score'] += 10;
            } else {
                $analysis['recommendations'][] = 'Include target keyword in meta description';
            }
        }

        // Heading analysis
        $headings = $this->extractHeadings($content);
        $analysis['metrics']['headings'] = $headings;
        
        if (empty($headings['h1'])) {
            $analysis['issues'][] = 'No H1 heading found';
        } elseif (count($headings['h1']) > 1) {
            $analysis['issues'][] = 'Multiple H1 headings found';
        } else {
            $analysis['score'] += 5;
        }

        if (empty($headings['h2'])) {
            $analysis['recommendations'][] = 'Add H2 headings to structure content';
        }

        // Image analysis
        $images = $this->extractImages($content);
        $analysis['metrics']['images'] = $images;
        
        $imagesWithoutAlt = array_filter($images, function($img) {
            return empty($img['alt']);
        });
        
        if (!empty($imagesWithoutAlt)) {
            $analysis['issues'][] = count($imagesWithoutAlt) . ' images without alt text';
        }

        // Link analysis
        $links = $this->extractLinks($content);
        $analysis['metrics']['links'] = $links;
        
        if ($links['internal'] < 2) {
            $analysis['recommendations'][] = 'Add more internal links';
        }

        // Readability analysis
        $readability = $this->calculateReadability($content);
        $analysis['metrics']['readability'] = $readability;
        
        if ($readability < 60) {
            $analysis['recommendations'][] = 'Improve content readability';
        }

        return $analysis;
    }

    /**
     * Calculate keyword density
     */
    private function calculateKeywordDensity($content, $keyword)
    {
        $text = strip_tags($content);
        $wordCount = str_word_count($text);
        $keywordCount = substr_count(strtolower($text), strtolower($keyword));
        
        return $wordCount > 0 ? ($keywordCount / $wordCount) * 100 : 0;
    }

    /**
     * Extract headings from content
     */
    private function extractHeadings($content)
    {
        $headings = ['h1' => [], 'h2' => [], 'h3' => [], 'h4' => [], 'h5' => [], 'h6' => []];
        
        for ($i = 1; $i <= 6; $i++) {
            preg_match_all("/<h{$i}[^>]*>(.*?)<\/h{$i}>/i", $content, $matches);
            $headings["h{$i}"] = array_map('strip_tags', $matches[1]);
        }
        
        return $headings;
    }

    /**
     * Extract images from content
     */
    private function extractImages($content)
    {
        preg_match_all('/<img[^>]+>/i', $content, $matches);
        $images = [];
        
        foreach ($matches[0] as $img) {
            preg_match('/src="([^"]+)"/i', $img, $srcMatch);
            preg_match('/alt="([^"]*)"/i', $img, $altMatch);
            
            $images[] = [
                'src' => $srcMatch[1] ?? '',
                'alt' => $altMatch[1] ?? ''
            ];
        }
        
        return $images;
    }

    /**
     * Extract links from content
     */
    private function extractLinks($content)
    {
        preg_match_all('/<a[^>]+href="([^"]+)"[^>]*>(.*?)<\/a>/i', $content, $matches);
        
        $links = ['internal' => 0, 'external' => 0, 'total' => count($matches[0])];
        
        foreach ($matches[1] as $href) {
            if (strpos($href, 'http') === 0 && strpos($href, $_SERVER['HTTP_HOST']) === false) {
                $links['external']++;
            } else {
                $links['internal']++;
            }
        }
        
        return $links;
    }

    /**
     * Calculate readability score (simplified Flesch Reading Ease)
     */
    private function calculateReadability($content)
    {
        $text = strip_tags($content);
        $sentences = preg_split('/[.!?]+/', $text);
        $words = str_word_count($text);
        $syllables = $this->countSyllables($text);
        
        $sentenceCount = count(array_filter($sentences));
        
        if ($sentenceCount === 0 || $words === 0) {
            return 0;
        }
        
        $avgSentenceLength = $words / $sentenceCount;
        $avgSyllablesPerWord = $syllables / $words;
        
        // Simplified Flesch Reading Ease formula
        $score = 206.835 - (1.015 * $avgSentenceLength) - (84.6 * $avgSyllablesPerWord);
        
        return max(0, min(100, $score));
    }

    /**
     * Count syllables in text (simplified)
     */
    private function countSyllables($text)
    {
        $text = strtolower($text);
        $vowels = 'aeiouy';
        $syllableCount = 0;
        $previousWasVowel = false;
        
        for ($i = 0; $i < strlen($text); $i++) {
            $char = $text[$i];
            $isVowel = strpos($vowels, $char) !== false;
            
            if ($isVowel && !$previousWasVowel) {
                $syllableCount++;
            }
            
            $previousWasVowel = $isVowel;
        }
        
        return max(1, $syllableCount);
    }

    /**
     * Generate sitemap XML
     */
    public function generateSitemap($includeImages = true)
    {
        $urls = [];
        $baseUrl = rtrim(config('app.url'), '/');
        
        // Get all published content
        $contents = $this->db->select(
            "SELECT id, slug, updated_at, content_type, published_at 
             FROM content 
             WHERE status = 'published' AND deleted_at IS NULL
             ORDER BY updated_at DESC"
        );

        foreach ($contents as $content) {
            $url = [
                'loc' => $baseUrl . '/' . $content['slug'],
                'lastmod' => date('c', strtotime($content['updated_at'])),
                'changefreq' => $this->getChangeFrequency($content['content_type']),
                'priority' => $this->getPriority($content['content_type'])
            ];

            // Add images if requested
            if ($includeImages) {
                $images = $this->getContentImages($content['id']);
                if (!empty($images)) {
                    $url['images'] = $images;
                }
            }

            $urls[] = $url;
        }

        // Add static pages
        $staticPages = [
            ['loc' => $baseUrl, 'priority' => '1.0', 'changefreq' => 'daily'],
            ['loc' => $baseUrl . '/about', 'priority' => '0.8', 'changefreq' => 'monthly'],
            ['loc' => $baseUrl . '/contact', 'priority' => '0.7', 'changefreq' => 'monthly']
        ];

        $urls = array_merge($staticPages, $urls);

        return $this->buildSitemapXml($urls);
    }

    /**
     * Get change frequency for content type
     */
    private function getChangeFrequency($contentType)
    {
        $frequencies = [
            'page' => 'monthly',
            'post' => 'weekly',
            'news' => 'daily',
            'product' => 'weekly'
        ];

        return $frequencies[$contentType] ?? 'monthly';
    }

    /**
     * Get priority for content type
     */
    private function getPriority($contentType)
    {
        $priorities = [
            'page' => '0.8',
            'post' => '0.6',
            'news' => '0.7',
            'product' => '0.9'
        ];

        return $priorities[$contentType] ?? '0.5';
    }

    /**
     * Get images for content
     */
    private function getContentImages($contentId)
    {
        $images = $this->db->select(
            "SELECT file_url, title, alt_text 
             FROM media m
             INNER JOIN media_usage mu ON m.id = mu.media_id
             WHERE mu.entity_id = ? AND mu.entity_type = 'content' AND m.file_type = 'image'",
            [$contentId]
        );

        $result = [];
        foreach ($images as $image) {
            $result[] = [
                'loc' => config('app.url') . $image['file_url'],
                'title' => $image['title'],
                'caption' => $image['alt_text']
            ];
        }

        return $result;
    }

    /**
     * Build sitemap XML
     */
    private function buildSitemapXml($urls)
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"' . "\n";
        $xml .= '        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">' . "\n";

        foreach ($urls as $url) {
            $xml .= "  <url>\n";
            $xml .= "    <loc>" . htmlspecialchars($url['loc']) . "</loc>\n";
            
            if (isset($url['lastmod'])) {
                $xml .= "    <lastmod>" . $url['lastmod'] . "</lastmod>\n";
            }
            
            if (isset($url['changefreq'])) {
                $xml .= "    <changefreq>" . $url['changefreq'] . "</changefreq>\n";
            }
            
            if (isset($url['priority'])) {
                $xml .= "    <priority>" . $url['priority'] . "</priority>\n";
            }

            // Add images
            if (isset($url['images'])) {
                foreach ($url['images'] as $image) {
                    $xml .= "    <image:image>\n";
                    $xml .= "      <image:loc>" . htmlspecialchars($image['loc']) . "</image:loc>\n";
                    if (!empty($image['title'])) {
                        $xml .= "      <image:title>" . htmlspecialchars($image['title']) . "</image:title>\n";
                    }
                    if (!empty($image['caption'])) {
                        $xml .= "      <image:caption>" . htmlspecialchars($image['caption']) . "</image:caption>\n";
                    }
                    $xml .= "    </image:image>\n";
                }
            }

            $xml .= "  </url>\n";
        }

        $xml .= "</urlset>\n";

        return $xml;
    }

    /**
     * Generate robots.txt
     */
    public function generateRobotsTxt()
    {
        $baseUrl = rtrim(config('app.url'), '/');
        
        $robots = "User-agent: *\n";
        $robots .= "Allow: /\n";
        $robots .= "Disallow: /admin/\n";
        $robots .= "Disallow: /api/\n";
        $robots .= "Disallow: /uploads/temp/\n";
        $robots .= "\n";
        $robots .= "Sitemap: {$baseUrl}/sitemap.xml\n";

        return $robots;
    }

    /**
     * Get SEO recommendations for content
     */
    public function getSeoRecommendations($contentId)
    {
        $content = $this->db->selectOne(
            "SELECT * FROM content WHERE id = ?",
            [$contentId]
        );

        if (!$content) {
            return [];
        }

        $recommendations = [];

        // Check if content has meta description
        if (empty($content['meta_description'])) {
            $recommendations[] = [
                'type' => 'warning',
                'message' => 'Add a meta description to improve search engine visibility',
                'action' => 'Add meta description'
            ];
        }

        // Check if content has meta keywords
        if (empty($content['meta_keywords'])) {
            $recommendations[] = [
                'type' => 'info',
                'message' => 'Consider adding meta keywords',
                'action' => 'Add keywords'
            ];
        }

        // Check content length
        $contentLength = str_word_count(strip_tags($content['content']));
        if ($contentLength < 300) {
            $recommendations[] = [
                'type' => 'warning',
                'message' => 'Content is too short for good SEO (less than 300 words)',
                'action' => 'Expand content'
            ];
        }

        return $recommendations;
    }

    /**
     * Generate SEO data for frontend pages
     */
    public function generateSeoData(array $data)
    {
        $siteSettings = $this->getSiteSettings();

        return [
            'title' => $this->generateTitle($data['title'] ?? '', $siteSettings),
            'description' => $this->generateDescription($data['description'] ?? '', $siteSettings),
            'keywords' => $this->generateKeywords($data['keywords'] ?? '', $siteSettings),
            'canonical_url' => $data['url'] ?? $siteSettings['site_url'],
            'og_title' => $data['title'] ?? $siteSettings['site_name'],
            'og_description' => $data['description'] ?? $siteSettings['site_description'],
            'og_url' => $data['url'] ?? $siteSettings['site_url'],
            'og_type' => $data['type'] ?? 'website',
            'og_image' => $data['image'] ?? $siteSettings['site_logo'],
            'og_site_name' => $siteSettings['site_name'],
            'twitter_card' => 'summary_large_image',
            'twitter_title' => $data['title'] ?? $siteSettings['site_name'],
            'twitter_description' => $data['description'] ?? $siteSettings['site_description'],
            'twitter_image' => $data['image'] ?? $siteSettings['site_logo'],
            'article_published_time' => $data['published_time'] ?? null,
            'article_modified_time' => $data['modified_time'] ?? null,
            'article_author' => $data['author'] ?? null,
            'structured_data' => $this->generateStructuredData($data, $siteSettings)
        ];
    }

    /**
     * Generate sitemap XML
     */
    public function generateSitemap()
    {
        $siteUrl = $this->settingsService->get('site_url', 'http://localhost');
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        // Add homepage
        $xml .= $this->generateSitemapUrl($siteUrl, date('c'), 'daily', '1.0');

        // Add published pages
        $pages = $this->getPublishedPages();
        foreach ($pages as $page) {
            $url = $siteUrl . '/' . $page['slug'];
            $lastmod = date('c', strtotime($page['updated_at']));
            $changefreq = $this->getChangeFrequency($page);
            $priority = $this->getPriority($page);

            $xml .= $this->generateSitemapUrl($url, $lastmod, $changefreq, $priority);
        }

        $xml .= '</urlset>';

        return $xml;
    }

    /**
     * Generate meta title
     */
    private function generateTitle($pageTitle, $siteSettings)
    {
        if (empty($pageTitle)) {
            return $siteSettings['site_name'];
        }

        // If page title already contains site name, don't duplicate
        if (strpos($pageTitle, $siteSettings['site_name']) !== false) {
            return $pageTitle;
        }

        return $pageTitle . ' - ' . $siteSettings['site_name'];
    }

    /**
     * Generate meta description
     */
    private function generateDescription($pageDescription, $siteSettings)
    {
        if (!empty($pageDescription)) {
            return $this->truncateText($pageDescription, 160);
        }

        return $this->truncateText($siteSettings['site_description'], 160);
    }

    /**
     * Generate meta keywords
     */
    private function generateKeywords($pageKeywords, $siteSettings)
    {
        $keywords = [];

        if (!empty($pageKeywords)) {
            $keywords = array_merge($keywords, explode(',', $pageKeywords));
        }

        if (!empty($siteSettings['seo_keywords'])) {
            $keywords = array_merge($keywords, explode(',', $siteSettings['seo_keywords']));
        }

        // Clean and deduplicate keywords
        $keywords = array_unique(array_map('trim', $keywords));
        $keywords = array_filter($keywords); // Remove empty values

        return implode(', ', $keywords);
    }

    /**
     * Generate structured data (JSON-LD)
     */
    private function generateStructuredData($data, $siteSettings)
    {
        $structuredData = [
            '@context' => 'https://schema.org',
            '@type' => $data['type'] === 'article' ? 'Article' : 'WebPage',
            'name' => $data['title'] ?? $siteSettings['site_name'],
            'description' => $data['description'] ?? $siteSettings['site_description'],
            'url' => $data['url'] ?? $siteSettings['site_url']
        ];

        // Add organization data
        $structuredData['publisher'] = [
            '@type' => 'Organization',
            'name' => $siteSettings['site_name'],
            'url' => $siteSettings['site_url']
        ];

        if (!empty($siteSettings['site_logo'])) {
            $structuredData['publisher']['logo'] = [
                '@type' => 'ImageObject',
                'url' => $siteSettings['site_logo']
            ];
        }

        // Add article-specific data
        if ($data['type'] === 'article') {
            if (!empty($data['published_time'])) {
                $structuredData['datePublished'] = date('c', strtotime($data['published_time']));
            }

            if (!empty($data['modified_time'])) {
                $structuredData['dateModified'] = date('c', strtotime($data['modified_time']));
            }

            if (!empty($data['author'])) {
                $structuredData['author'] = [
                    '@type' => 'Person',
                    'name' => $data['author']
                ];
            }

            if (!empty($data['image'])) {
                $structuredData['image'] = [
                    '@type' => 'ImageObject',
                    'url' => $data['image']
                ];
            }
        }

        return json_encode($structuredData, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    }

    /**
     * Generate sitemap URL entry
     */
    private function generateSitemapUrl($url, $lastmod, $changefreq, $priority)
    {
        $xml = "  <url>\n";
        $xml .= "    <loc>" . htmlspecialchars($url) . "</loc>\n";
        $xml .= "    <lastmod>" . $lastmod . "</lastmod>\n";
        $xml .= "    <changefreq>" . $changefreq . "</changefreq>\n";
        $xml .= "    <priority>" . $priority . "</priority>\n";
        $xml .= "  </url>\n";

        return $xml;
    }

    /**
     * Get published pages for sitemap
     */
    private function getPublishedPages()
    {
        return $this->db->select(
            "SELECT slug, updated_at, page_type, views
             FROM pages
             WHERE status = 'published'
             ORDER BY updated_at DESC"
        );
    }

    /**
     * Get change frequency for sitemap
     */
    private function getChangeFrequency($page)
    {
        $daysSinceUpdate = (time() - strtotime($page['updated_at'])) / (24 * 60 * 60);

        if ($daysSinceUpdate < 1) {
            return 'daily';
        } elseif ($daysSinceUpdate < 7) {
            return 'weekly';
        } elseif ($daysSinceUpdate < 30) {
            return 'monthly';
        } else {
            return 'yearly';
        }
    }

    /**
     * Get priority for sitemap
     */
    private function getPriority($page)
    {
        // Homepage gets highest priority
        if ($page['page_type'] === 'homepage') {
            return '1.0';
        }

        // High-traffic pages get higher priority
        if ($page['views'] > 1000) {
            return '0.9';
        } elseif ($page['views'] > 100) {
            return '0.8';
        } else {
            return '0.6';
        }
    }

    /**
     * Get site settings for SEO
     */
    private function getSiteSettings()
    {
        return [
            'site_name' => $this->settingsService->get('site_name', 'CMS Pro'),
            'site_description' => $this->settingsService->get('site_description', ''),
            'site_url' => $this->settingsService->get('site_url', 'http://localhost'),
            'site_logo' => $this->settingsService->get('site_logo', ''),
            'seo_keywords' => $this->settingsService->get('seo_keywords', '')
        ];
    }

    /**
     * Truncate text to specified length
     */
    private function truncateText($text, $length)
    {
        if (strlen($text) <= $length) {
            return $text;
        }

        $truncated = substr($text, 0, $length);
        $lastSpace = strrpos($truncated, ' ');

        if ($lastSpace !== false) {
            $truncated = substr($truncated, 0, $lastSpace);
        }

        return $truncated . '...';
    }
}
