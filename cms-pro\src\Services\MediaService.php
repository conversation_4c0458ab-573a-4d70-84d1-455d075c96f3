<?php

namespace CmsPro\Services;

use CmsPro\Core\Database;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * Media Service
 * 
 * @package CmsPro\Services
 */
class MediaService
{
    private $db;
    private $uploadPath;
    private $allowedTypes;
    private $maxFileSize;
    private $imageQuality;

    public function __construct(Database $db = null)
    {
        $this->db = $db ?: app()->getDatabase();
        $this->uploadPath = app()->getBasePath() . '/public/uploads';
        $this->allowedTypes = config('media.allowed_types', [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
            'application/pdf', 'text/plain', 'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'video/mp4', 'video/avi', 'video/mov', 'video/wmv',
            'audio/mp3', 'audio/wav', 'audio/ogg'
        ]);
        $this->maxFileSize = config('media.max_file_size', 10 * 1024 * 1024); // 10MB
        $this->imageQuality = config('media.image_quality', 85);

        $this->ensureUploadDirectories();
    }

    /**
     * Upload file
     */
    public function uploadFile(UploadedFile $file, array $options = [])
    {
        // Validate file
        $this->validateFile($file, $options);

        // Generate unique filename
        $filename = $this->generateUniqueFilename($file);
        
        // Determine upload directory
        $directory = $this->getUploadDirectory($file, $options);
        $fullPath = $this->uploadPath . '/' . $directory;
        
        // Ensure directory exists
        if (!is_dir($fullPath)) {
            mkdir($fullPath, 0755, true);
        }

        // Move uploaded file
        $file->move($fullPath, $filename);
        
        $filePath = $directory . '/' . $filename;
        $fullFilePath = $fullPath . '/' . $filename;

        // Process image if needed
        if ($this->isImage($file->getMimeType()) && ($options['resize'] ?? true)) {
            $this->processImage($fullFilePath, $options);
        }

        // Get file information
        $fileInfo = $this->getFileInfo($fullFilePath, $filePath);
        
        // Save to database
        $mediaId = $this->saveToDatabase($file, $fileInfo, $options);
        
        $fileInfo['id'] = $mediaId;
        
        return $fileInfo;
    }

    /**
     * Upload multiple files
     */
    public function uploadMultipleFiles(array $files, array $options = [])
    {
        $results = [];
        
        foreach ($files as $file) {
            if ($file instanceof UploadedFile && $file->isValid()) {
                try {
                    $results[] = $this->uploadFile($file, $options);
                } catch (\Exception $e) {
                    $results[] = [
                        'error' => $e->getMessage(),
                        'filename' => $file->getClientOriginalName()
                    ];
                }
            }
        }
        
        return $results;
    }

    /**
     * Get media by ID
     */
    public function getMedia($id)
    {
        return $this->db->selectOne(
            "SELECT * FROM media WHERE id = ? AND deleted_at IS NULL",
            [$id]
        );
    }

    /**
     * Get media list with filters
     */
    public function getMediaList(array $filters = [], $page = 1, $perPage = 20)
    {
        $whereClause = "deleted_at IS NULL";
        $params = [];

        // Apply filters
        if (!empty($filters['type'])) {
            $whereClause .= " AND file_type = ?";
            $params[] = $filters['type'];
        }

        if (!empty($filters['mime_type'])) {
            $whereClause .= " AND mime_type LIKE ?";
            $params[] = $filters['mime_type'] . '%';
        }

        if (!empty($filters['search'])) {
            $whereClause .= " AND (original_name LIKE ? OR alt_text LIKE ? OR title LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        if (!empty($filters['date_from'])) {
            $whereClause .= " AND created_at >= ?";
            $params[] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $whereClause .= " AND created_at <= ?";
            $params[] = $filters['date_to'] . ' 23:59:59';
        }

        if (!empty($filters['user_id'])) {
            $whereClause .= " AND uploaded_by = ?";
            $params[] = $filters['user_id'];
        }

        // Get total count
        $total = $this->db->selectValue(
            "SELECT COUNT(*) FROM media WHERE {$whereClause}",
            $params
        );

        // Get paginated results
        $offset = ($page - 1) * $perPage;
        $orderBy = $filters['order_by'] ?? 'created_at';
        $orderDir = $filters['order_dir'] ?? 'DESC';

        $media = $this->db->select(
            "SELECT m.*, u.full_name as uploader_name 
             FROM media m 
             LEFT JOIN users u ON m.uploaded_by = u.id 
             WHERE {$whereClause} 
             ORDER BY m.{$orderBy} {$orderDir} 
             LIMIT ? OFFSET ?",
            array_merge($params, [$perPage, $offset])
        );

        return [
            'data' => $media,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'last_page' => ceil($total / $perPage)
        ];
    }

    /**
     * Update media metadata
     */
    public function updateMedia($id, array $data)
    {
        $allowedFields = ['title', 'alt_text', 'description', 'caption'];
        $updateData = array_intersect_key($data, array_flip($allowedFields));
        $updateData['updated_at'] = date('Y-m-d H:i:s');

        return $this->db->update('media', $updateData, 'id = ?', [$id]);
    }

    /**
     * Delete media
     */
    public function deleteMedia($id, $permanent = false)
    {
        $media = $this->getMedia($id);
        if (!$media) {
            throw new \RuntimeException('Media not found');
        }

        if ($permanent) {
            // Delete physical file
            $filePath = $this->uploadPath . '/' . $media['file_path'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            // Delete thumbnails
            $this->deleteThumbnails($media);

            // Delete from database
            return $this->db->delete('media', 'id = ?', [$id]);
        } else {
            // Soft delete
            return $this->db->update('media', 
                ['deleted_at' => date('Y-m-d H:i:s')], 
                'id = ?', 
                [$id]
            );
        }
    }

    /**
     * Restore deleted media
     */
    public function restoreMedia($id)
    {
        return $this->db->update('media', 
            ['deleted_at' => null], 
            'id = ?', 
            [$id]
        );
    }

    /**
     * Generate thumbnail
     */
    public function generateThumbnail($mediaId, $width = 150, $height = 150, $crop = true)
    {
        $media = $this->getMedia($mediaId);
        if (!$media || !$this->isImage($media['mime_type'])) {
            return null;
        }

        $originalPath = $this->uploadPath . '/' . $media['file_path'];
        $thumbnailDir = dirname($originalPath) . '/thumbnails';
        
        if (!is_dir($thumbnailDir)) {
            mkdir($thumbnailDir, 0755, true);
        }

        $thumbnailName = pathinfo($media['filename'], PATHINFO_FILENAME) . 
                        "_{$width}x{$height}" . 
                        ($crop ? '_crop' : '') . 
                        '.' . pathinfo($media['filename'], PATHINFO_EXTENSION);
        
        $thumbnailPath = $thumbnailDir . '/' . $thumbnailName;

        // Check if thumbnail already exists
        if (file_exists($thumbnailPath)) {
            return str_replace($this->uploadPath . '/public/', '/uploads/', $thumbnailPath);
        }

        // Create thumbnail
        if ($this->createThumbnail($originalPath, $thumbnailPath, $width, $height, $crop)) {
            return str_replace($this->uploadPath . '/public/', '/uploads/', $thumbnailPath);
        }

        return null;
    }

    /**
     * Get media statistics
     */
    public function getMediaStatistics()
    {
        $stats = [
            'total_files' => $this->db->selectValue("SELECT COUNT(*) FROM media WHERE deleted_at IS NULL"),
            'total_size' => $this->db->selectValue("SELECT SUM(file_size) FROM media WHERE deleted_at IS NULL") ?: 0,
            'images' => $this->db->selectValue("SELECT COUNT(*) FROM media WHERE file_type = 'image' AND deleted_at IS NULL"),
            'documents' => $this->db->selectValue("SELECT COUNT(*) FROM media WHERE file_type = 'document' AND deleted_at IS NULL"),
            'videos' => $this->db->selectValue("SELECT COUNT(*) FROM media WHERE file_type = 'video' AND deleted_at IS NULL"),
            'audio' => $this->db->selectValue("SELECT COUNT(*) FROM media WHERE file_type = 'audio' AND deleted_at IS NULL"),
            'deleted_files' => $this->db->selectValue("SELECT COUNT(*) FROM media WHERE deleted_at IS NOT NULL")
        ];

        // Get monthly upload statistics
        $stats['monthly_uploads'] = $this->db->select(
            "SELECT 
                DATE_FORMAT(created_at, '%Y-%m') as month,
                COUNT(*) as count,
                SUM(file_size) as total_size
             FROM media 
             WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
             AND deleted_at IS NULL
             GROUP BY DATE_FORMAT(created_at, '%Y-%m')
             ORDER BY month DESC"
        );

        // Get top uploaders
        $stats['top_uploaders'] = $this->db->select(
            "SELECT 
                u.full_name,
                COUNT(m.id) as upload_count,
                SUM(m.file_size) as total_size
             FROM media m
             INNER JOIN users u ON m.uploaded_by = u.id
             WHERE m.deleted_at IS NULL
             GROUP BY u.id, u.full_name
             ORDER BY upload_count DESC
             LIMIT 10"
        );

        return $stats;
    }

    /**
     * Clean up orphaned files
     */
    public function cleanupOrphanedFiles()
    {
        $cleaned = 0;
        
        // Get all files from database
        $dbFiles = $this->db->select("SELECT file_path FROM media WHERE deleted_at IS NULL");
        $dbFilePaths = array_column($dbFiles, 'file_path');

        // Scan upload directory
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($this->uploadPath)
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $relativePath = str_replace($this->uploadPath . '/', '', $file->getPathname());
                
                // Skip thumbnails and system files
                if (strpos($relativePath, 'thumbnails/') !== false || 
                    strpos($relativePath, '.') === 0) {
                    continue;
                }

                // Check if file exists in database
                if (!in_array($relativePath, $dbFilePaths)) {
                    unlink($file->getPathname());
                    $cleaned++;
                }
            }
        }

        return $cleaned;
    }

    /**
     * Validate uploaded file
     */
    private function validateFile(UploadedFile $file, array $options = [])
    {
        if (!$file->isValid()) {
            throw new \RuntimeException('Invalid file upload');
        }

        // Check file size
        $maxSize = $options['max_size'] ?? $this->maxFileSize;
        if ($file->getSize() > $maxSize) {
            throw new \RuntimeException('File size exceeds maximum allowed size');
        }

        // Check MIME type
        $allowedTypes = $options['allowed_types'] ?? $this->allowedTypes;
        if (!in_array($file->getMimeType(), $allowedTypes)) {
            throw new \RuntimeException('File type not allowed');
        }

        // Additional security checks
        $this->performSecurityChecks($file);
    }

    /**
     * Perform security checks on uploaded file
     */
    private function performSecurityChecks(UploadedFile $file)
    {
        // Check for executable files
        $dangerousExtensions = ['php', 'phtml', 'php3', 'php4', 'php5', 'pl', 'py', 'jsp', 'asp', 'sh', 'cgi'];
        $extension = strtolower($file->getClientOriginalExtension());
        
        if (in_array($extension, $dangerousExtensions)) {
            throw new \RuntimeException('Executable files are not allowed');
        }

        // Check file content for images
        if ($this->isImage($file->getMimeType())) {
            $imageInfo = getimagesize($file->getPathname());
            if ($imageInfo === false) {
                throw new \RuntimeException('Invalid image file');
            }
        }
    }

    /**
     * Generate unique filename
     */
    private function generateUniqueFilename(UploadedFile $file)
    {
        $extension = $file->getClientOriginalExtension();
        $basename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $basename = $this->sanitizeFilename($basename);
        
        return $basename . '_' . uniqid() . '.' . $extension;
    }

    /**
     * Sanitize filename
     */
    private function sanitizeFilename($filename)
    {
        // Remove special characters and spaces
        $filename = preg_replace('/[^a-zA-Z0-9\-_]/', '', $filename);
        $filename = trim($filename, '-_');
        
        return $filename ?: 'file';
    }

    /**
     * Get upload directory based on file type and date
     */
    private function getUploadDirectory(UploadedFile $file, array $options = [])
    {
        $type = $this->getFileType($file->getMimeType());
        $year = date('Y');
        $month = date('m');
        
        return "{$type}/{$year}/{$month}";
    }

    /**
     * Get file type category
     */
    private function getFileType($mimeType)
    {
        if (strpos($mimeType, 'image/') === 0) {
            return 'image';
        } elseif (strpos($mimeType, 'video/') === 0) {
            return 'video';
        } elseif (strpos($mimeType, 'audio/') === 0) {
            return 'audio';
        } else {
            return 'document';
        }
    }

    /**
     * Check if file is an image
     */
    private function isImage($mimeType)
    {
        return strpos($mimeType, 'image/') === 0;
    }

    /**
     * Process image (resize, optimize)
     */
    private function processImage($filePath, array $options = [])
    {
        $maxWidth = $options['max_width'] ?? config('media.max_image_width', 1920);
        $maxHeight = $options['max_height'] ?? config('media.max_image_height', 1080);
        $quality = $options['quality'] ?? $this->imageQuality;

        $imageInfo = getimagesize($filePath);
        if (!$imageInfo) {
            return false;
        }

        list($width, $height, $type) = $imageInfo;

        // Skip if image is already smaller than max dimensions
        if ($width <= $maxWidth && $height <= $maxHeight) {
            return true;
        }

        // Calculate new dimensions
        $ratio = min($maxWidth / $width, $maxHeight / $height);
        $newWidth = round($width * $ratio);
        $newHeight = round($height * $ratio);

        // Create image resource
        $source = $this->createImageResource($filePath, $type);
        if (!$source) {
            return false;
        }

        // Create new image
        $destination = imagecreatetruecolor($newWidth, $newHeight);
        
        // Preserve transparency for PNG and GIF
        if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
            imagealphablending($destination, false);
            imagesavealpha($destination, true);
            $transparent = imagecolorallocatealpha($destination, 255, 255, 255, 127);
            imagefilledrectangle($destination, 0, 0, $newWidth, $newHeight, $transparent);
        }

        // Resize image
        imagecopyresampled($destination, $source, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);

        // Save resized image
        $result = $this->saveImageResource($destination, $filePath, $type, $quality);

        // Clean up
        imagedestroy($source);
        imagedestroy($destination);

        return $result;
    }

    /**
     * Create image resource from file
     */
    private function createImageResource($filePath, $type)
    {
        switch ($type) {
            case IMAGETYPE_JPEG:
                return imagecreatefromjpeg($filePath);
            case IMAGETYPE_PNG:
                return imagecreatefrompng($filePath);
            case IMAGETYPE_GIF:
                return imagecreatefromgif($filePath);
            case IMAGETYPE_WEBP:
                return imagecreatefromwebp($filePath);
            default:
                return false;
        }
    }

    /**
     * Save image resource to file
     */
    private function saveImageResource($resource, $filePath, $type, $quality)
    {
        switch ($type) {
            case IMAGETYPE_JPEG:
                return imagejpeg($resource, $filePath, $quality);
            case IMAGETYPE_PNG:
                return imagepng($resource, $filePath, round(9 * (100 - $quality) / 100));
            case IMAGETYPE_GIF:
                return imagegif($resource, $filePath);
            case IMAGETYPE_WEBP:
                return imagewebp($resource, $filePath, $quality);
            default:
                return false;
        }
    }

    /**
     * Create thumbnail image
     */
    private function createThumbnail($sourcePath, $thumbnailPath, $width, $height, $crop = true)
    {
        $imageInfo = getimagesize($sourcePath);
        if (!$imageInfo) {
            return false;
        }

        list($sourceWidth, $sourceHeight, $type) = $imageInfo;

        $source = $this->createImageResource($sourcePath, $type);
        if (!$source) {
            return false;
        }

        if ($crop) {
            // Crop to exact dimensions
            $sourceRatio = $sourceWidth / $sourceHeight;
            $targetRatio = $width / $height;

            if ($sourceRatio > $targetRatio) {
                // Source is wider
                $cropWidth = $sourceHeight * $targetRatio;
                $cropHeight = $sourceHeight;
                $cropX = ($sourceWidth - $cropWidth) / 2;
                $cropY = 0;
            } else {
                // Source is taller
                $cropWidth = $sourceWidth;
                $cropHeight = $sourceWidth / $targetRatio;
                $cropX = 0;
                $cropY = ($sourceHeight - $cropHeight) / 2;
            }

            $thumbnail = imagecreatetruecolor($width, $height);
            imagecopyresampled($thumbnail, $source, 0, 0, $cropX, $cropY, $width, $height, $cropWidth, $cropHeight);
        } else {
            // Resize maintaining aspect ratio
            $ratio = min($width / $sourceWidth, $height / $sourceHeight);
            $newWidth = round($sourceWidth * $ratio);
            $newHeight = round($sourceHeight * $ratio);

            $thumbnail = imagecreatetruecolor($newWidth, $newHeight);
            imagecopyresampled($thumbnail, $source, 0, 0, 0, 0, $newWidth, $newHeight, $sourceWidth, $sourceHeight);
        }

        // Preserve transparency
        if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
        }

        $result = $this->saveImageResource($thumbnail, $thumbnailPath, $type, $this->imageQuality);

        imagedestroy($source);
        imagedestroy($thumbnail);

        return $result;
    }

    /**
     * Get file information
     */
    private function getFileInfo($fullPath, $relativePath)
    {
        $info = [
            'filename' => basename($fullPath),
            'file_path' => $relativePath,
            'file_url' => '/uploads/' . $relativePath,
            'file_size' => filesize($fullPath),
            'mime_type' => mime_content_type($fullPath),
            'file_type' => $this->getFileType(mime_content_type($fullPath))
        ];

        // Get image dimensions if it's an image
        if ($this->isImage($info['mime_type'])) {
            $imageInfo = getimagesize($fullPath);
            if ($imageInfo) {
                $info['width'] = $imageInfo[0];
                $info['height'] = $imageInfo[1];
            }
        }

        return $info;
    }

    /**
     * Save file information to database
     */
    private function saveToDatabase(UploadedFile $file, array $fileInfo, array $options = [])
    {
        $data = [
            'filename' => $fileInfo['filename'],
            'original_name' => $file->getClientOriginalName(),
            'file_path' => $fileInfo['file_path'],
            'file_url' => $fileInfo['file_url'],
            'file_size' => $fileInfo['file_size'],
            'mime_type' => $fileInfo['mime_type'],
            'file_type' => $fileInfo['file_type'],
            'width' => $fileInfo['width'] ?? null,
            'height' => $fileInfo['height'] ?? null,
            'title' => $options['title'] ?? pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME),
            'alt_text' => $options['alt_text'] ?? '',
            'description' => $options['description'] ?? '',
            'caption' => $options['caption'] ?? '',
            'uploaded_by' => auth()->id(),
            'created_at' => date('Y-m-d H:i:s')
        ];

        return $this->db->insert('media', $data);
    }

    /**
     * Delete thumbnails for media
     */
    private function deleteThumbnails($media)
    {
        $originalPath = $this->uploadPath . '/' . $media['file_path'];
        $thumbnailDir = dirname($originalPath) . '/thumbnails';
        
        if (is_dir($thumbnailDir)) {
            $basename = pathinfo($media['filename'], PATHINFO_FILENAME);
            $extension = pathinfo($media['filename'], PATHINFO_EXTENSION);
            
            $pattern = $thumbnailDir . '/' . $basename . '_*.' . $extension;
            $thumbnails = glob($pattern);
            
            foreach ($thumbnails as $thumbnail) {
                if (file_exists($thumbnail)) {
                    unlink($thumbnail);
                }
            }
        }
    }

    /**
     * Ensure upload directories exist
     */
    private function ensureUploadDirectories()
    {
        $directories = ['image', 'video', 'audio', 'document'];

        foreach ($directories as $dir) {
            $path = $this->uploadPath . '/' . $dir;
            if (!is_dir($path)) {
                mkdir($path, 0755, true);
            }
        }
    }

    /**
     * Get media URL
     */
    public function getMediaUrl($media)
    {
        if (is_array($media)) {
            return $media['file_url'];
        }

        return $media->file_url ?? null;
    }

    /**
     * Get thumbnail URL
     */
    public function getThumbnailUrl($media, $width = 150, $height = 150, $crop = true)
    {
        if (is_array($media)) {
            $mediaId = $media['id'];
        } else {
            $mediaId = $media->id;
        }

        return $this->generateThumbnail($mediaId, $width, $height, $crop);
    }

    /**
     * Bulk delete media
     */
    public function bulkDeleteMedia(array $ids, $permanent = false)
    {
        $deleted = 0;

        foreach ($ids as $id) {
            try {
                $this->deleteMedia($id, $permanent);
                $deleted++;
            } catch (\Exception $e) {
                // Continue with other files
                continue;
            }
        }

        return $deleted;
    }

    /**
     * Search media
     */
    public function searchMedia($query, $type = null, $limit = 20)
    {
        $whereClause = "deleted_at IS NULL AND (original_name LIKE ? OR title LIKE ? OR alt_text LIKE ?)";
        $params = ["%{$query}%", "%{$query}%", "%{$query}%"];

        if ($type) {
            $whereClause .= " AND file_type = ?";
            $params[] = $type;
        }

        return $this->db->select(
            "SELECT * FROM media WHERE {$whereClause} ORDER BY created_at DESC LIMIT ?",
            array_merge($params, [$limit])
        );
    }

    /**
     * Get media files with filters
     */
    public function getMediaFiles(array $filters = [])
    {
        $folder = $filters['folder'] ?? '';
        $type = $filters['type'] ?? 'all';
        $search = $filters['search'] ?? '';
        $perPage = $filters['per_page'] ?? 24;
        $page = $filters['page'] ?? 1;
        $userId = $filters['user_id'] ?? null;

        $whereConditions = [];
        $params = [];

        // Folder filter
        if ($folder) {
            $whereConditions[] = "folder_path = ?";
            $params[] = $folder;
        }

        // Type filter
        if ($type !== 'all') {
            switch ($type) {
                case 'image':
                    $whereConditions[] = "file_type = 'image'";
                    break;
                case 'document':
                    $whereConditions[] = "file_type = 'document'";
                    break;
                case 'video':
                    $whereConditions[] = "file_type = 'video'";
                    break;
                case 'audio':
                    $whereConditions[] = "file_type = 'audio'";
                    break;
            }
        }

        // Search filter
        if ($search) {
            $whereConditions[] = "(title LIKE ? OR original_name LIKE ? OR alt_text LIKE ?)";
            $searchTerm = "%{$search}%";
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
        }

        // User filter (for non-admin users)
        if ($userId) {
            $whereConditions[] = "user_id = ?";
            $params[] = $userId;
        }

        $whereClause = $whereConditions ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

        // Get total count
        $totalQuery = "SELECT COUNT(*) as total FROM media {$whereClause}";
        $totalResult = $this->db->selectOne($totalQuery, $params);
        $total = $totalResult['total'];

        // Calculate pagination
        $offset = ($page - 1) * $perPage;
        $totalPages = ceil($total / $perPage);

        // Get files
        $filesQuery = "
            SELECT m.*, u.first_name, u.last_name
            FROM media m
            LEFT JOIN users u ON m.user_id = u.id
            {$whereClause}
            ORDER BY m.created_at DESC
            LIMIT {$perPage} OFFSET {$offset}
        ";

        $files = $this->db->select($filesQuery, $params);

        // Add computed properties
        foreach ($files as &$file) {
            $file['file_url'] = $this->getFileUrl($file['filename'], $file['folder_path']);
            $file['thumbnail_url'] = $this->getThumbnailUrl($file);
            $file['uploader_name'] = trim(($file['first_name'] ?? '') . ' ' . ($file['last_name'] ?? ''));
        }

        return [
            'files' => $files,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'pages' => $totalPages
            ]
        ];
    }

    /**
     * Get folders
     */
    public function getFolders()
    {
        // Get all unique folder paths
        $folders = $this->db->select(
            "SELECT folder_path, COUNT(*) as file_count
             FROM media
             WHERE folder_path != ''
             GROUP BY folder_path
             ORDER BY folder_path"
        );

        $result = [];
        foreach ($folders as $folder) {
            $parts = explode('/', $folder['folder_path']);
            $result[] = [
                'name' => end($parts),
                'path' => $folder['folder_path'],
                'file_count' => $folder['file_count']
            ];
        }

        return $result;
    }

    /**
     * Get media statistics
     */
    public function getMediaStatistics()
    {
        $stats = $this->db->selectOne(
            "SELECT
                COUNT(*) as total_files,
                SUM(CASE WHEN file_type = 'image' THEN 1 ELSE 0 END) as images,
                SUM(CASE WHEN file_type = 'document' THEN 1 ELSE 0 END) as documents,
                SUM(CASE WHEN file_type = 'video' THEN 1 ELSE 0 END) as videos,
                SUM(CASE WHEN file_type = 'audio' THEN 1 ELSE 0 END) as audio,
                SUM(file_size) as total_size
             FROM media"
        );

        return [
            'total_files' => (int) $stats['total_files'],
            'images' => (int) $stats['images'],
            'documents' => (int) $stats['documents'],
            'videos' => (int) $stats['videos'],
            'audio' => (int) $stats['audio'],
            'total_size' => (int) $stats['total_size']
        ];
    }

    /**
     * Create folder
     */
    public function createFolder($name, $parent = '')
    {
        // Sanitize folder name
        $name = preg_replace('/[^a-zA-Z0-9\-_]/', '', $name);

        if (empty($name)) {
            return [
                'success' => false,
                'message' => 'Invalid folder name'
            ];
        }

        // Build folder path
        $folderPath = $parent ? $parent . '/' . $name : $name;

        // Check if folder already exists
        $existing = $this->db->selectOne(
            "SELECT id FROM media WHERE folder_path = ? LIMIT 1",
            [$folderPath]
        );

        if ($existing) {
            return [
                'success' => false,
                'message' => 'Folder already exists'
            ];
        }

        // Create physical directory
        $physicalPath = $this->uploadPath . '/' . $folderPath;
        if (!is_dir($physicalPath)) {
            if (!mkdir($physicalPath, 0755, true)) {
                return [
                    'success' => false,
                    'message' => 'Failed to create folder'
                ];
            }
        }

        return [
            'success' => true,
            'message' => 'Folder created successfully',
            'folder' => [
                'name' => $name,
                'path' => $folderPath
            ]
        ];
    }

    /**
     * Get upload limits
     */
    public function getUploadLimits()
    {
        return [
            'max_file_size' => round($this->maxFileSize / 1024 / 1024, 1), // Convert to MB
            'max_files' => 10 // Max files per upload
        ];
    }

    /**
     * Get allowed file types
     */
    public function getAllowedTypes()
    {
        $extensions = [];
        foreach ($this->allowedTypes as $mimeType) {
            switch ($mimeType) {
                case 'image/jpeg':
                    $extensions[] = 'jpg';
                    $extensions[] = 'jpeg';
                    break;
                case 'image/png':
                    $extensions[] = 'png';
                    break;
                case 'image/gif':
                    $extensions[] = 'gif';
                    break;
                case 'image/webp':
                    $extensions[] = 'webp';
                    break;
                case 'image/svg+xml':
                    $extensions[] = 'svg';
                    break;
                case 'application/pdf':
                    $extensions[] = 'pdf';
                    break;
                case 'text/plain':
                    $extensions[] = 'txt';
                    break;
                case 'application/msword':
                    $extensions[] = 'doc';
                    break;
                case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                    $extensions[] = 'docx';
                    break;
                case 'video/mp4':
                    $extensions[] = 'mp4';
                    break;
                case 'audio/mp3':
                    $extensions[] = 'mp3';
                    break;
            }
        }

        return array_unique($extensions);
    }

    /**
     * Get thumbnail URL
     */
    private function getThumbnailUrl($media)
    {
        if ($media['file_type'] === 'image') {
            // For images, use the file itself as thumbnail
            return $this->getFileUrl($media['filename'], $media['folder_path']);
        }

        // For non-images, return a default icon or generate thumbnail
        return '/assets/images/file-icons/' . $media['file_type'] . '.png';
    }
}
