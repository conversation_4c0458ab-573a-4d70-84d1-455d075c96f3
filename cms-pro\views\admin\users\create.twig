{% extends "layouts/admin.twig" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="user-form-container">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ title }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ url('/admin/users') }}">{{ __('Users') }}</a>
                    </li>
                    <li class="breadcrumb-item active">{{ __('Create') }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ url('/admin/users') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>{{ __('Back to Users') }}
            </a>
        </div>
    </div>

    <form method="POST" action="{{ url('/admin/users') }}" class="user-form" id="user-form" novalidate>
        {{ csrf_field() | raw }}
        
        <div class="row">
            <!-- Main Content Column -->
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-user me-2"></i>{{ __('User Information') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- First Name -->
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">
                                    {{ __('First Name') }} <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control {{ errors.first_name ? 'is-invalid' : '' }}" 
                                       id="first_name" 
                                       name="first_name" 
                                       value="{{ old('first_name', user.first_name) }}" 
                                       required
                                       maxlength="100"
                                       placeholder="{{ __('Enter first name...') }}">
                                {% if errors.first_name %}
                                    <div class="invalid-feedback">{{ errors.first_name }}</div>
                                {% endif %}
                            </div>

                            <!-- Last Name -->
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">
                                    {{ __('Last Name') }} <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control {{ errors.last_name ? 'is-invalid' : '' }}" 
                                       id="last_name" 
                                       name="last_name" 
                                       value="{{ old('last_name', user.last_name) }}" 
                                       required
                                       maxlength="100"
                                       placeholder="{{ __('Enter last name...') }}">
                                {% if errors.last_name %}
                                    <div class="invalid-feedback">{{ errors.last_name }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Email -->
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                {{ __('Email Address') }} <span class="text-danger">*</span>
                            </label>
                            <input type="email" 
                                   class="form-control {{ errors.email ? 'is-invalid' : '' }}" 
                                   id="email" 
                                   name="email" 
                                   value="{{ old('email', user.email) }}" 
                                   required
                                   maxlength="255"
                                   placeholder="{{ __('Enter email address...') }}">
                            {% if errors.email %}
                                <div class="invalid-feedback">{{ errors.email }}</div>
                            {% endif %}
                        </div>

                        <!-- Username -->
                        <div class="mb-3">
                            <label for="username" class="form-label">{{ __('Username') }}</label>
                            <input type="text" 
                                   class="form-control {{ errors.username ? 'is-invalid' : '' }}" 
                                   id="username" 
                                   name="username" 
                                   value="{{ old('username', user.username) }}"
                                   maxlength="50"
                                   placeholder="{{ __('Optional username...') }}">
                            {% if errors.username %}
                                <div class="invalid-feedback">{{ errors.username }}</div>
                            {% endif %}
                            <div class="form-text">
                                {{ __('Optional. If not provided, email will be used for login.') }}
                            </div>
                        </div>

                        <!-- Password -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">
                                    {{ __('Password') }} <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <input type="password" 
                                           class="form-control {{ errors.password ? 'is-invalid' : '' }}" 
                                           id="password" 
                                           name="password" 
                                           required
                                           minlength="8"
                                           maxlength="255"
                                           placeholder="{{ __('Enter password...') }}">
                                    <button type="button" class="btn btn-outline-secondary" id="toggle-password">
                                        <i class="fas fa-eye" id="password-toggle-icon"></i>
                                    </button>
                                </div>
                                {% if errors.password %}
                                    <div class="invalid-feedback">{{ errors.password }}</div>
                                {% endif %}
                                <div class="form-text">
                                    {{ __('Minimum 8 characters required') }}
                                </div>
                            </div>

                            <!-- Password Confirmation -->
                            <div class="col-md-6 mb-3">
                                <label for="password_confirmation" class="form-label">
                                    {{ __('Confirm Password') }} <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <input type="password" 
                                           class="form-control {{ errors.password_confirmation ? 'is-invalid' : '' }}" 
                                           id="password_confirmation" 
                                           name="password_confirmation" 
                                           required
                                           minlength="8"
                                           maxlength="255"
                                           placeholder="{{ __('Confirm password...') }}">
                                    <button type="button" class="btn btn-outline-secondary" id="toggle-password-confirmation">
                                        <i class="fas fa-eye" id="password-confirmation-toggle-icon"></i>
                                    </button>
                                </div>
                                {% if errors.password_confirmation %}
                                    <div class="invalid-feedback">{{ errors.password_confirmation }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Roles & Permissions -->
                {% if auth().can('users.manage_roles') or auth().can('users.manage_permissions') %}
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-success">
                            <i class="fas fa-shield-alt me-2"></i>{{ __('Roles & Permissions') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if auth().can('users.manage_roles') %}
                        <!-- Roles -->
                        <div class="mb-4">
                            <label class="form-label">{{ __('Roles') }}</label>
                            <div class="row">
                                {% for role in roles %}
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               id="role_{{ role.id }}" 
                                               name="roles[]" 
                                               value="{{ role.id }}"
                                               {{ old('roles') and role.id in old('roles') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="role_{{ role.id }}">
                                            <strong>{{ role.name }}</strong>
                                            {% if role.description %}
                                            <br><small class="text-muted">{{ role.description }}</small>
                                            {% endif %}
                                        </label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}

                        {% if auth().can('users.manage_permissions') %}
                        <!-- Direct Permissions -->
                        <div class="mb-3">
                            <label class="form-label">{{ __('Additional Permissions') }}</label>
                            <div class="form-text mb-3">
                                {{ __('Grant specific permissions in addition to role-based permissions') }}
                            </div>
                            
                            {% for category, categoryPermissions in permissions %}
                            <div class="permission-category mb-3">
                                <h6 class="text-primary mb-2">{{ category|title }}</h6>
                                <div class="row">
                                    {% for permission in categoryPermissions %}
                                    <div class="col-md-6 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" 
                                                   id="permission_{{ permission.id }}" 
                                                   name="permissions[]" 
                                                   value="{{ permission.id }}"
                                                   {{ old('permissions') and permission.id in old('permissions') ? 'checked' : '' }}>
                                            <label class="form-check-label" for="permission_{{ permission.id }}">
                                                {{ permission.name }}
                                            </label>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Sidebar Column -->
            <div class="col-lg-4">
                <!-- User Settings -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-warning">
                            <i class="fas fa-cog me-2"></i>{{ __('User Settings') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Status -->
                        <div class="mb-3">
                            <label for="status" class="form-label">{{ __('Status') }}</label>
                            <select class="form-select {{ errors.status ? 'is-invalid' : '' }}" 
                                    id="status" 
                                    name="status" 
                                    required>
                                {% for key, label in statuses %}
                                <option value="{{ key }}" {{ old('status', 'active') == key ? 'selected' : '' }}>
                                    {{ label }}
                                </option>
                                {% endfor %}
                            </select>
                            {% if errors.status %}
                                <div class="invalid-feedback">{{ errors.status }}</div>
                            {% endif %}
                        </div>

                        <!-- Avatar Upload -->
                        <div class="mb-3">
                            <label for="avatar" class="form-label">{{ __('Avatar') }}</label>
                            <div class="avatar-upload-container">
                                <div class="avatar-preview mb-2">
                                    <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center mx-auto">
                                        <i class="fas fa-user fa-2x text-muted"></i>
                                    </div>
                                </div>
                                <input type="file" class="form-control" id="avatar" name="avatar" accept="image/*">
                                <div class="form-text">
                                    {{ __('Upload a profile picture (optional)') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" name="action" value="save" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ __('Create User') }}
                            </button>
                            
                            <div class="btn-group">
                                <button type="submit" name="action" value="save_and_continue" class="btn btn-outline-primary">
                                    <i class="fas fa-save me-2"></i>{{ __('Save & Continue') }}
                                </button>
                                <button type="submit" name="action" value="save_and_new" class="btn btn-outline-primary">
                                    <i class="fas fa-plus me-2"></i>{{ __('Save & New') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
/**
 * User Form Management
 * Enhanced with validation and user experience features
 */
class UserForm {
    constructor() {
        this.form = document.getElementById('user-form');
        this.passwordField = document.getElementById('password');
        this.passwordConfirmField = document.getElementById('password_confirmation');
        this.avatarInput = document.getElementById('avatar');

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupPasswordToggle();
        this.setupPasswordValidation();
        this.setupAvatarPreview();
        this.setupFormValidation();
    }

    setupEventListeners() {
        // Form submission
        this.form.addEventListener('submit', (e) => {
            this.handleFormSubmit(e);
        });

        // Real-time validation
        this.form.addEventListener('input', (e) => {
            this.validateField(e.target);
        });

        this.form.addEventListener('change', (e) => {
            this.validateField(e.target);
        });
    }

    setupPasswordToggle() {
        // Password toggle
        document.getElementById('toggle-password').addEventListener('click', () => {
            this.togglePasswordVisibility('password', 'password-toggle-icon');
        });

        // Password confirmation toggle
        document.getElementById('toggle-password-confirmation').addEventListener('click', () => {
            this.togglePasswordVisibility('password_confirmation', 'password-confirmation-toggle-icon');
        });
    }

    setupPasswordValidation() {
        // Real-time password validation
        this.passwordField.addEventListener('input', () => {
            this.validatePassword();
        });

        this.passwordConfirmField.addEventListener('input', () => {
            this.validatePasswordConfirmation();
        });
    }

    setupAvatarPreview() {
        if (this.avatarInput) {
            this.avatarInput.addEventListener('change', (e) => {
                this.previewAvatar(e.target.files[0]);
            });
        }
    }

    setupFormValidation() {
        // Add validation classes to required fields
        const requiredFields = this.form.querySelectorAll('[required]');
        requiredFields.forEach(field => {
            field.addEventListener('blur', () => {
                this.validateField(field);
            });
        });
    }

    togglePasswordVisibility(fieldId, iconId) {
        const field = document.getElementById(fieldId);
        const icon = document.getElementById(iconId);

        if (field.type === 'password') {
            field.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            field.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    validatePassword() {
        const password = this.passwordField.value;
        const isValid = password.length >= 8;

        if (isValid) {
            this.passwordField.classList.remove('is-invalid');
            this.passwordField.classList.add('is-valid');
        } else if (password.length > 0) {
            this.passwordField.classList.remove('is-valid');
            this.passwordField.classList.add('is-invalid');
        }

        // Also validate confirmation if it has a value
        if (this.passwordConfirmField.value) {
            this.validatePasswordConfirmation();
        }

        return isValid;
    }

    validatePasswordConfirmation() {
        const password = this.passwordField.value;
        const confirmation = this.passwordConfirmField.value;
        const isValid = password === confirmation && confirmation.length > 0;

        if (isValid) {
            this.passwordConfirmField.classList.remove('is-invalid');
            this.passwordConfirmField.classList.add('is-valid');
        } else if (confirmation.length > 0) {
            this.passwordConfirmField.classList.remove('is-valid');
            this.passwordConfirmField.classList.add('is-invalid');
        }

        return isValid;
    }

    validateField(field) {
        if (!field.checkValidity) return true;

        const isValid = field.checkValidity();

        if (isValid) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        } else if (field.value.length > 0 || field.hasAttribute('required')) {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
        }

        return isValid;
    }

    previewAvatar(file) {
        if (!file) return;

        if (!file.type.startsWith('image/')) {
            this.showNotification('Please select a valid image file.', 'error');
            return;
        }

        if (file.size > 5 * 1024 * 1024) { // 5MB limit
            this.showNotification('Image file size must be less than 5MB.', 'error');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            const preview = document.querySelector('.avatar-preview .avatar-placeholder');
            preview.innerHTML = `<img src="${e.target.result}" alt="Avatar Preview" class="w-100 h-100 rounded-circle" style="object-fit: cover;">`;
        };
        reader.readAsDataURL(file);
    }

    async handleFormSubmit(e) {
        e.preventDefault();

        // Validate form
        if (!this.form.checkValidity()) {
            e.stopPropagation();
            this.form.classList.add('was-validated');

            // Focus first invalid field
            const firstInvalid = this.form.querySelector(':invalid');
            if (firstInvalid) {
                firstInvalid.focus();
                firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }

            return;
        }

        // Additional validation
        if (!this.validatePassword() || !this.validatePasswordConfirmation()) {
            this.showNotification('Please fix password validation errors.', 'error');
            return;
        }

        // Show loading state
        const submitButton = e.submitter;
        const originalText = submitButton.innerHTML;
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';

        try {
            const formData = new FormData(this.form);

            const response = await fetch(this.form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');

                if (result.redirect) {
                    setTimeout(() => {
                        window.location.href = result.redirect;
                    }, 1000);
                }
            } else {
                this.showNotification(result.message || 'Save failed', 'error');
            }

        } catch (error) {
            console.error('Form submission error:', error);
            this.showNotification('An error occurred while saving', 'error');
        } finally {
            // Restore button state
            submitButton.disabled = false;
            submitButton.innerHTML = originalText;
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
        notification.innerHTML = `
            ${this.escapeHtml(message)}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    new UserForm();
});
</script>

<style>
/* User Form Styles */
.user-form-container .card-header h6 {
    font-size: 0.875rem;
    font-weight: 600;
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-text {
    font-size: 0.8125rem;
}

.avatar-upload-container {
    text-align: center;
}

.avatar-preview {
    width: 80px;
    height: 80px;
    margin: 0 auto;
}

.avatar-placeholder {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.permission-category {
    border-left: 3px solid #007bff;
    padding-left: 1rem;
    margin-left: 0.5rem;
}

/* Form validation styles */
.was-validated .form-control:valid,
.form-control.is-valid {
    border-color: #28a745;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.38 1.38 3.72-**********-4.66 4.66z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:invalid,
.form-control.is-invalid {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 7.4 5.8 6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .user-form-container .col-lg-8,
    .user-form-container .col-lg-4 {
        margin-bottom: 1rem;
    }

    .btn-group {
        width: 100%;
    }

    .btn-group .btn {
        flex: 1;
    }

    .permission-category {
        margin-left: 0;
        padding-left: 0.5rem;
    }
}

/* Dark mode support */
[data-bs-theme="dark"] .card-header {
    background-color: #2d3748 !important;
    border-color: #4a5568;
}

[data-bs-theme="dark"] .form-control {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
}

[data-bs-theme="dark"] .form-control:focus {
    background-color: #2d3748;
    border-color: #0d6efd;
    color: #e2e8f0;
}

[data-bs-theme="dark"] .avatar-placeholder {
    background: linear-gradient(135deg, #4c63d2 0%, #5a4fcf 100%);
}

[data-bs-theme="dark"] .permission-category {
    border-left-color: #0d6efd;
}
</style>
{% endblock %}
