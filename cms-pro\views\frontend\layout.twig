<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ site_settings.site_name }}{% endblock %}</title>
    <meta name="description" content="{% block description %}{{ site_settings.site_description }}{% endblock %}">
    <meta name="keywords" content="{% block keywords %}{{ page.meta_keywords ?? site_settings.site_keywords }}{% endblock %}">
    
    <!-- Open Graph -->
    <meta property="og:title" content="{% block og_title %}{{ block('title') }}{% endblock %}">
    <meta property="og:description" content="{% block og_description %}{{ block('description') }}{% endblock %}">
    <meta property="og:image" content="{% block og_image %}{{ site_settings.site_logo }}{% endblock %}">
    <meta property="og:url" content="{{ app.request.uri }}">
    <meta property="og:type" content="website">
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ block('title') }}">
    <meta name="twitter:description" content="{{ block('description') }}">
    <meta name="twitter:image" content="{{ block('og_image') }}">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    
    <!-- Bootstrap 5 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
            --border-color: #e2e8f0;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: #ffffff;
        }

        /* Header */
        .header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .navbar {
            padding: 1rem 0;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color) !important;
            text-decoration: none;
        }

        .navbar-nav .nav-link {
            font-weight: 500;
            color: var(--dark-color) !important;
            padding: 0.5rem 1rem !important;
            margin: 0 0.25rem;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover,
        .navbar-nav .nav-link.active {
            background-color: var(--primary-color);
            color: white !important;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
            color: white;
            padding: 100px 0;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="1000,100 1000,0 0,100"/></svg>');
            background-size: cover;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .hero p {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .hero .btn {
            padding: 12px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .hero .btn-light {
            background: white;
            color: var(--primary-color);
            border: none;
        }

        .hero .btn-light:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        /* Content Sections */
        .content-section {
            padding: 80px 0;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 1rem;
            color: var(--dark-color);
        }

        .section-subtitle {
            font-size: 1.2rem;
            text-align: center;
            color: var(--secondary-color);
            margin-bottom: 4rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Cards */
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            height: 100%;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 25px rgba(0,0,0,0.15);
        }

        .card-img-top {
            border-radius: 12px 12px 0 0;
            height: 200px;
            object-fit: cover;
        }

        .card-body {
            padding: 1.5rem;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: var(--dark-color);
        }

        .card-text {
            color: var(--secondary-color);
            margin-bottom: 1rem;
        }

        .card-meta {
            font-size: 0.875rem;
            color: var(--secondary-color);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* Blog Post */
        .blog-post {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }

        .blog-post:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
        }

        .blog-post-image {
            height: 250px;
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .blog-post-category {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: var(--primary-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .blog-post-content {
            padding: 1.5rem;
        }

        .blog-post-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: var(--dark-color);
        }

        .blog-post-title a {
            color: inherit;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .blog-post-title a:hover {
            color: var(--primary-color);
        }

        .blog-post-excerpt {
            color: var(--secondary-color);
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .blog-post-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 0.875rem;
            color: var(--secondary-color);
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        /* Footer */
        .footer {
            background: var(--dark-color);
            color: white;
            padding: 60px 0 30px;
        }

        .footer h5 {
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: white;
        }

        .footer ul {
            list-style: none;
            padding: 0;
        }

        .footer ul li {
            margin-bottom: 0.5rem;
        }

        .footer ul li a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer ul li a:hover {
            color: white;
        }

        .footer-bottom {
            border-top: 1px solid rgba(255,255,255,0.1);
            margin-top: 2rem;
            padding-top: 2rem;
            text-align: center;
            color: rgba(255,255,255,0.6);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero p {
                font-size: 1.1rem;
            }
            
            .section-title {
                font-size: 2rem;
            }
            
            .content-section {
                padding: 60px 0;
            }
        }

        /* Utilities */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            padding: 10px 20px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background: #1d4ed8;
            border-color: #1d4ed8;
            transform: translateY(-1px);
        }

        .text-primary {
            color: var(--primary-color) !important;
        }

        .bg-light {
            background-color: var(--light-color) !important;
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Scroll to top button */
        .scroll-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .scroll-top.show {
            opacity: 1;
            visibility: visible;
        }

        .scroll-top:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }
    </style>
    
    {% block styles %}{% endblock %}
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar navbar-expand-lg">
            <div class="container">
                <a class="navbar-brand" href="{{ url('/') }}">
                    <i class="fas fa-cube me-2"></i>{{ site_settings.site_name }}
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link {{ current_page == 'home' ? 'active' : '' }}" href="{{ url('/') }}">Ana Sayfa</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ current_page == 'about' ? 'active' : '' }}" href="{{ url('/hakkimizda') }}">Hakkımızda</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ current_page == 'services' ? 'active' : '' }}" href="{{ url('/hizmetler') }}">Hizmetler</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ current_page == 'blog' ? 'active' : '' }}" href="{{ url('/blog') }}">Blog</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ current_page == 'contact' ? 'active' : '' }}" href="{{ url('/iletisim') }}">İletişim</a>
                        </li>
                    </ul>
                    
                    <div class="d-flex align-items-center">
                        <!-- Search -->
                        <form class="d-flex me-3" action="{{ url('/arama') }}" method="GET">
                            <div class="input-group">
                                <input class="form-control" type="search" name="q" placeholder="Ara..." value="{{ app.request.query.get('q', '') }}">
                                <button class="btn btn-outline-secondary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                        
                        <!-- Social Links -->
                        <div class="social-links">
                            {% if site_settings.facebook_url %}
                            <a href="{{ site_settings.facebook_url }}" class="text-decoration-none me-2" target="_blank">
                                <i class="fab fa-facebook"></i>
                            </a>
                            {% endif %}
                            {% if site_settings.twitter_url %}
                            <a href="{{ site_settings.twitter_url }}" class="text-decoration-none me-2" target="_blank">
                                <i class="fab fa-twitter"></i>
                            </a>
                            {% endif %}
                            {% if site_settings.instagram_url %}
                            <a href="{{ site_settings.instagram_url }}" class="text-decoration-none me-2" target="_blank">
                                <i class="fab fa-instagram"></i>
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5>{{ site_settings.site_name }}</h5>
                    <p>{{ site_settings.site_description }}</p>
                    <div class="social-links">
                        {% if site_settings.facebook_url %}
                        <a href="{{ site_settings.facebook_url }}" class="me-3" target="_blank">
                            <i class="fab fa-facebook fa-lg"></i>
                        </a>
                        {% endif %}
                        {% if site_settings.twitter_url %}
                        <a href="{{ site_settings.twitter_url }}" class="me-3" target="_blank">
                            <i class="fab fa-twitter fa-lg"></i>
                        </a>
                        {% endif %}
                        {% if site_settings.instagram_url %}
                        <a href="{{ site_settings.instagram_url }}" class="me-3" target="_blank">
                            <i class="fab fa-instagram fa-lg"></i>
                        </a>
                        {% endif %}
                        {% if site_settings.linkedin_url %}
                        <a href="{{ site_settings.linkedin_url }}" class="me-3" target="_blank">
                            <i class="fab fa-linkedin fa-lg"></i>
                        </a>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5>Hızlı Linkler</h5>
                    <ul>
                        <li><a href="{{ url('/') }}">Ana Sayfa</a></li>
                        <li><a href="{{ url('/hakkimizda') }}">Hakkımızda</a></li>
                        <li><a href="{{ url('/hizmetler') }}">Hizmetler</a></li>
                        <li><a href="{{ url('/blog') }}">Blog</a></li>
                        <li><a href="{{ url('/iletisim') }}">İletişim</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5>Kategoriler</h5>
                    <ul>
                        {% for category in footer_categories %}
                        <li><a href="{{ url('/kategori/' ~ category.slug) }}">{{ category.name }}</a></li>
                        {% endfor %}
                    </ul>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <h5>İletişim</h5>
                    <ul>
                        {% if site_settings.address %}
                        <li><i class="fas fa-map-marker-alt me-2"></i>{{ site_settings.address }}</li>
                        {% endif %}
                        {% if site_settings.phone %}
                        <li><i class="fas fa-phone me-2"></i>{{ site_settings.phone }}</li>
                        {% endif %}
                        {% if site_settings.email %}
                        <li><i class="fas fa-envelope me-2"></i>{{ site_settings.email }}</li>
                        {% endif %}
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; {{ "now"|date("Y") }} {{ site_settings.site_name }}. Tüm hakları saklıdır.</p>
            </div>
        </div>
    </footer>

    <!-- Scroll to Top Button -->
    <button class="scroll-top" onclick="scrollToTop()">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Scroll to top functionality
        window.addEventListener('scroll', function() {
            const scrollTop = document.querySelector('.scroll-top');
            if (window.pageYOffset > 300) {
                scrollTop.classList.add('show');
            } else {
                scrollTop.classList.remove('show');
            }
        });

        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Loading states for forms
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function() {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.innerHTML = '<span class="loading"></span> Gönderiliyor...';
                    submitBtn.disabled = true;
                }
            });
        });
    </script>
    
    <!-- Google Analytics -->
    {% if site_settings.google_analytics_id %}
    <script async src="https://www.googletagmanager.com/gtag/js?id={{ site_settings.google_analytics_id }}"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '{{ site_settings.google_analytics_id }}');
    </script>
    {% endif %}

    {% block scripts %}{% endblock %}
</body>
</html>
