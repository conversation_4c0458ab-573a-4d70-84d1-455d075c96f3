{# WYSIWYG Editor Form Field Template - Security Enhanced #}

<div class="form-group wysiwyg-field-wrapper mb-3" data-field-type="wysiwyg">
    <label for="{{ field.name }}" class="form-label">
        {{ field.label }}
        {% if field.required %}
            <span class="text-danger">*</span>
        {% endif %}
    </label>
    
    {% if field.description %}
        <small class="form-text text-muted mb-2 d-block">{{ field.description }}</small>
    {% endif %}

    <div class="wysiwyg-editor-container">
        <textarea 
            id="{{ field.name }}"
            name="{{ field.name }}"
            class="form-control wysiwyg-editor"
            data-editor="wysiwyg"
            data-field-type="{{ field.editor_type|default('advanced') }}"
            data-height="{{ field.height|default(400) }}"
            {% if field.toolbar %}data-toolbar="{{ field.toolbar }}"{% endif %}
            {% if field.plugins %}data-plugins="{{ field.plugins }}"{% endif %}
            {% if field.readonly %}readonly{% endif %}
            {% if field.required %}required{% endif %}
            data-csrf-token="{{ csrf_token() }}"
            data-upload-url="{{ url('admin.editor.upload') }}"
            data-media-library-url="{{ url('admin.media.library') }}"
            data-sanitize-url="{{ url('admin.editor.sanitize') }}"
            placeholder="{{ field.placeholder|default('Enter content...') }}"
        >{{ field.value|e }}</textarea>
        
        {# Security notice #}
        <div class="wysiwyg-security-notice">
            <small class="text-muted">
                <i class="fas fa-shield-alt text-success"></i>
                Content is automatically sanitized for security. Suspicious content will be cleaned.
            </small>
        </div>
    </div>

    {# Validation errors #}
    {% if errors[field.name] is defined %}
        <div class="invalid-feedback d-block">
            {{ errors[field.name] }}
        </div>
    {% endif %}
</div>

{# Security JavaScript #}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize secure WYSIWYG editor
    if (typeof WysiwygEditor !== 'undefined') {
        const editor = new WysiwygEditor();
        
        // Add security event listeners
        document.addEventListener('editor-save', function(e) {
            // Validate content before saving
            const content = e.detail.content;
            const sanitizedContent = sanitizeEditorContent(content);
            
            if (content !== sanitizedContent) {
                showSecurityWarning('Content was automatically sanitized for security.');
                e.detail.textarea.value = sanitizedContent;
            }
        });
        
        // Content sanitization function
        function sanitizeEditorContent(content) {
            // Remove dangerous elements and attributes
            const temp = document.createElement('div');
            temp.innerHTML = content;
            
            // Remove script tags and dangerous elements
            const dangerousElements = temp.querySelectorAll('script, object, embed, iframe, form, input, button, textarea, select, option, meta, link, style, base');
            dangerousElements.forEach(el => el.remove());
            
            const allElements = temp.querySelectorAll('*');
            allElements.forEach(el => {
                // Remove event handler attributes
                const attributes = [...el.attributes];
                attributes.forEach(attr => {
                    if (attr.name.startsWith('on') || attr.name === 'style') {
                        el.removeAttribute(attr.name);
                    }
                });
                
                // Sanitize href and src attributes
                if (el.hasAttribute('href')) {
                    const href = el.getAttribute('href');
                    if (href.match(/^(javascript|data|vbscript):/i)) {
                        el.removeAttribute('href');
                    }
                }
                
                if (el.hasAttribute('src')) {
                    const src = el.getAttribute('src');
                    if (src.match(/^(javascript|data|vbscript):/i)) {
                        el.removeAttribute('src');
                    }
                }
            });
            
            return temp.innerHTML;
        }
        
        // Security warning function
        function showSecurityWarning(message) {
            const notification = document.createElement('div');
            notification.className = 'alert alert-warning alert-dismissible fade show mt-2';
            notification.innerHTML = `
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Security Notice:</strong> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.wysiwyg-field-wrapper');
            const editorContainer = container.querySelector('.wysiwyg-editor-container');
            container.insertBefore(notification, editorContainer.nextSibling);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
        
        // Form submission validation
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                const wysiwygFields = form.querySelectorAll('.wysiwyg-editor');
                
                wysiwygFields.forEach(field => {
                    const content = field.value;
                    const sanitizedContent = sanitizeEditorContent(content);
                    
                    if (content !== sanitizedContent) {
                        field.value = sanitizedContent;
                        showSecurityWarning('Content was sanitized before submission.');
                    }
                });
            });
        }
    }
});
</script>

<style>
/* WYSIWYG Editor Security Styles */
.wysiwyg-field-wrapper {
    position: relative;
}

.wysiwyg-editor-container {
    border: 1px solid var(--bs-border-color);
    border-radius: var(--bs-border-radius);
    overflow: hidden;
    background: var(--bs-body-bg);
}

.wysiwyg-security-notice {
    background-color: var(--bs-light);
    border-top: 1px solid var(--bs-border-color);
    padding: 8px 12px;
    font-size: 0.875rem;
}

.wysiwyg-security-notice i {
    margin-right: 5px;
}

/* Editor focused state */
.editor-focused {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
}

/* Security warning styles */
.editor-security-warning {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--bs-warning-bg-subtle);
    color: var(--bs-warning-text-emphasis);
    padding: 12px 16px;
    border: 1px solid var(--bs-warning-border-subtle);
    border-radius: var(--bs-border-radius);
    z-index: 1050;
    max-width: 300px;
    box-shadow: var(--bs-box-shadow);
}

/* Loading state */
.wysiwyg-loading {
    position: relative;
}

.wysiwyg-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(var(--bs-body-bg-rgb), 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.wysiwyg-loading::before {
    content: 'Loading editor...';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1001;
    color: var(--bs-secondary);
    font-size: 0.875rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .wysiwyg-editor-container {
        font-size: 0.875rem;
    }
    
    .editor-security-warning {
        position: fixed;
        top: 10px;
        left: 10px;
        right: 10px;
        max-width: none;
    }
}

/* Dark mode support */
[data-bs-theme="dark"] .wysiwyg-security-notice {
    background-color: var(--bs-dark);
    border-color: var(--bs-border-color);
    color: var(--bs-body-color);
}

[data-bs-theme="dark"] .wysiwyg-editor-container {
    border-color: var(--bs-border-color);
    background: var(--bs-body-bg);
}

/* TinyMCE specific styles */
.tox-tinymce {
    border: none !important;
}

.tox .tox-editor-header {
    border-bottom: 1px solid var(--bs-border-color) !important;
}

.tox .tox-statusbar {
    border-top: 1px solid var(--bs-border-color) !important;
}

/* Validation styles */
.is-invalid .wysiwyg-editor-container {
    border-color: var(--bs-danger);
}

.is-invalid .tox-tinymce {
    border-color: var(--bs-danger) !important;
}
</style>
