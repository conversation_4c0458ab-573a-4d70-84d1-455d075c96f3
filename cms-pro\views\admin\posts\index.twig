{% extends "admin/layout.twig" %}

{% block title %}Blog Yönetimi - CMS Pro{% endblock %}

{% block breadcrumb %}
{{ parent() }}
<li class="breadcrumb-item active">Blog Yazıları</li>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="page-title">Blog Yazı Yönetimi</h1>
        <p class="page-subtitle">Blog yazılarınızı yönetin ve düzenleyin</p>
    </div>
    <div>
        <button class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Yeni Yazı
        </button>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon primary">
                <i class="fas fa-blog"></i>
            </div>
            <div class="stats-value">{{ posts|length }}</div>
            <div class="stats-label">Toplam Yazı</div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card success">
            <div class="stats-icon success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-value">{{ posts|filter(p => p.status == 'published')|length }}</div>
            <div class="stats-label">Yayınlanan</div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card warning">
            <div class="stats-icon warning">
                <i class="fas fa-heart"></i>
            </div>
            <div class="stats-value">{{ posts|map(p => p.likes)|reduce((a, b) => a + b, 0) }}</div>
            <div class="stats-label">Toplam Beğeni</div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card danger">
            <div class="stats-icon danger">
                <i class="fas fa-eye"></i>
            </div>
            <div class="stats-value">{{ posts|map(p => p.views)|reduce((a, b) => a + b, 0) }}</div>
            <div class="stats-label">Toplam Görüntülenme</div>
        </div>
    </div>
</div>

<!-- Posts Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Blog Yazıları</h5>
        <div class="dropdown">
            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-cog me-1"></i>İşlemler
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#"><i class="fas fa-download me-2"></i>Dışa Aktar</a></li>
                <li><a class="dropdown-item" href="#"><i class="fas fa-upload me-2"></i>İçe Aktar</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-trash me-2"></i>Toplu Sil</a></li>
            </ul>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th style="width: 40px;">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="selectAll">
                            </div>
                        </th>
                        <th>Başlık</th>
                        <th>Kategori</th>
                        <th>Yazar</th>
                        <th>Durum</th>
                        <th>Görüntülenme</th>
                        <th>Beğeni</th>
                        <th>Tarih</th>
                        <th style="width: 120px;">İşlemler</th>
                    </tr>
                </thead>
                <tbody>
                    {% for post in posts %}
                    <tr>
                        <td>
                            <div class="form-check">
                                <input class="form-check-input row-checkbox" type="checkbox" value="{{ post.id }}">
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="post-icon me-3">
                                    <i class="fas fa-blog text-success"></i>
                                </div>
                                <div>
                                    <div class="fw-semibold">{{ post.title }}</div>
                                    <small class="text-muted">{{ post.slug }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            {% if post.category_name %}
                                <span class="badge bg-info">{{ post.category_name }}</span>
                            {% else %}
                                <span class="badge bg-light text-dark">Kategorisiz</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-2" style="width: 30px; height: 30px; font-size: 12px;">
                                    {{ post.author_name|first }}
                                </div>
                                {{ post.author_name }}
                            </div>
                        </td>
                        <td>
                            {% if post.status == 'published' %}
                                <span class="badge bg-success">Yayınlanan</span>
                            {% elseif post.status == 'draft' %}
                                <span class="badge bg-warning">Taslak</span>
                            {% elseif post.status == 'private' %}
                                <span class="badge bg-secondary">Özel</span>
                            {% else %}
                                <span class="badge bg-light text-dark">{{ post.status|title }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-eye text-muted me-1"></i>
                                {{ post.views ?? 0 }}
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-heart text-danger me-1"></i>
                                {{ post.likes ?? 0 }}
                            </div>
                        </td>
                        <td>
                            <small class="text-muted">{{ post.created_at|date('d.m.Y') }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" title="Düzenle">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-info" title="Görüntüle">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#"><i class="fas fa-copy me-2"></i>Kopyala</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="fas fa-share me-2"></i>Paylaş</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-trash me-2"></i>Sil</a></li>
                                    </ul>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="9" class="text-center py-5">
                            <div class="empty-state">
                                <i class="fas fa-blog fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Henüz blog yazısı bulunmuyor</h5>
                                <p class="text-muted">İlk blog yazınızı oluşturmak için "Yeni Yazı" butonuna tıklayın.</p>
                                <button class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>İlk Yazıyı Oluştur
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.row-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Row animations
const rows = document.querySelectorAll('tbody tr');
rows.forEach((row, index) => {
    row.style.opacity = '0';
    row.style.transform = 'translateY(20px)';
    row.style.transition = 'all 0.3s ease';
    
    setTimeout(() => {
        row.style.opacity = '1';
        row.style.transform = 'translateY(0)';
    }, index * 50);
});
</script>
{% endblock %}
