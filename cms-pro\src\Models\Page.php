<?php

namespace CmsPro\Models;

use CmsPro\Core\Model;
use CmsPro\Services\SlugService;
use CmsPro\Services\SeoService;

/**
 * Page Model
 * 
 * @package CmsPro\Models
 */
class Page extends Model
{
    protected $table = 'pages';
    
    protected $fillable = [
        'title',
        'slug',
        'content',
        'excerpt',
        'status',
        'template',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'featured_image',
        'author_id',
        'parent_id',
        'sort_order',
        'published_at',
        'scheduled_at',
        'settings',
        'custom_fields'
    ];

    protected $casts = [
        'published_at' => 'datetime',
        'scheduled_at' => 'datetime',
        'settings' => 'json',
        'custom_fields' => 'json',
        'sort_order' => 'integer',
        'parent_id' => 'integer',
        'author_id' => 'integer'
    ];

    protected $dates = [
        'published_at',
        'scheduled_at',
        'created_at',
        'updated_at'
    ];

    // Status constants
    const STATUS_DRAFT = 'draft';
    const STATUS_PUBLISHED = 'published';
    const STATUS_SCHEDULED = 'scheduled';
    const STATUS_PRIVATE = 'private';
    const STATUS_TRASH = 'trash';

    /**
     * Get all available statuses
     */
    public static function getStatuses()
    {
        return [
            self::STATUS_DRAFT => __('Draft'),
            self::STATUS_PUBLISHED => __('Published'),
            self::STATUS_SCHEDULED => __('Scheduled'),
            self::STATUS_PRIVATE => __('Private'),
            self::STATUS_TRASH => __('Trash')
        ];
    }

    /**
     * Get author relationship
     */
    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    /**
     * Get parent page relationship
     */
    public function parent()
    {
        return $this->belongsTo(Page::class, 'parent_id');
    }

    /**
     * Get child pages relationship
     */
    public function children()
    {
        return $this->hasMany(Page::class, 'parent_id')->orderBy('sort_order');
    }

    /**
     * Get page revisions
     */
    public function revisions()
    {
        return $this->hasMany(PageRevision::class)->orderBy('created_at', 'desc');
    }

    /**
     * Get page views
     */
    public function views()
    {
        return $this->hasMany(PageView::class);
    }

    /**
     * Scope for published pages
     */
    public function scopePublished($query)
    {
        return $query->where('status', self::STATUS_PUBLISHED)
                    ->where(function($q) {
                        $q->whereNull('published_at')
                          ->orWhere('published_at', '<=', now());
                    });
    }

    /**
     * Scope for draft pages
     */
    public function scopeDraft($query)
    {
        return $query->where('status', self::STATUS_DRAFT);
    }

    /**
     * Scope for scheduled pages
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', self::STATUS_SCHEDULED)
                    ->where('scheduled_at', '>', now());
    }

    /**
     * Scope for pages by template
     */
    public function scopeByTemplate($query, $template)
    {
        return $query->where('template', $template);
    }

    /**
     * Scope for root pages (no parent)
     */
    public function scopeRoot($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Get page URL
     */
    public function getUrl()
    {
        if ($this->parent_id) {
            $parent = $this->parent;
            return $parent->getUrl() . '/' . $this->slug;
        }
        
        return '/' . $this->slug;
    }

    /**
     * Get full URL with domain
     */
    public function getFullUrl()
    {
        return url($this->getUrl());
    }

    /**
     * Get page excerpt
     */
    public function getExcerpt($length = 150)
    {
        if ($this->excerpt) {
            return $this->excerpt;
        }

        // Generate excerpt from content
        $content = strip_tags($this->content);
        if (strlen($content) <= $length) {
            return $content;
        }

        return substr($content, 0, $length) . '...';
    }

    /**
     * Get reading time estimate
     */
    public function getReadingTime()
    {
        $wordCount = str_word_count(strip_tags($this->content));
        $minutes = ceil($wordCount / 200); // Average reading speed
        
        return $minutes . ' min read';
    }

    /**
     * Check if page is published
     */
    public function isPublished()
    {
        return $this->status === self::STATUS_PUBLISHED && 
               ($this->published_at === null || $this->published_at <= now());
    }

    /**
     * Check if page is scheduled
     */
    public function isScheduled()
    {
        return $this->status === self::STATUS_SCHEDULED && 
               $this->scheduled_at > now();
    }

    /**
     * Check if page is draft
     */
    public function isDraft()
    {
        return $this->status === self::STATUS_DRAFT;
    }

    /**
     * Publish page
     */
    public function publish()
    {
        $this->status = self::STATUS_PUBLISHED;
        $this->published_at = now();
        return $this->save();
    }

    /**
     * Unpublish page (set to draft)
     */
    public function unpublish()
    {
        $this->status = self::STATUS_DRAFT;
        return $this->save();
    }

    /**
     * Schedule page for publishing
     */
    public function schedule($datetime)
    {
        $this->status = self::STATUS_SCHEDULED;
        $this->scheduled_at = $datetime;
        return $this->save();
    }

    /**
     * Move to trash
     */
    public function trash()
    {
        $this->status = self::STATUS_TRASH;
        return $this->save();
    }

    /**
     * Restore from trash
     */
    public function restore()
    {
        $this->status = self::STATUS_DRAFT;
        return $this->save();
    }

    /**
     * Generate unique slug
     */
    public function generateSlug($title = null)
    {
        $slugService = new SlugService();
        $title = $title ?: $this->title;
        
        $baseSlug = $slugService->generate($title);
        $slug = $baseSlug;
        $counter = 1;

        // Check for existing slugs
        while ($this->slugExists($slug)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Check if slug exists
     */
    private function slugExists($slug)
    {
        $query = static::where('slug', $slug);
        
        if ($this->exists) {
            $query->where('id', '!=', $this->id);
        }

        return $query->exists();
    }

    /**
     * Get SEO data
     */
    public function getSeoData()
    {
        return [
            'title' => $this->meta_title ?: $this->title,
            'description' => $this->meta_description ?: $this->getExcerpt(),
            'keywords' => $this->meta_keywords,
            'canonical' => $this->getFullUrl(),
            'og_title' => $this->meta_title ?: $this->title,
            'og_description' => $this->meta_description ?: $this->getExcerpt(),
            'og_image' => $this->featured_image ? asset($this->featured_image) : null,
            'og_url' => $this->getFullUrl()
        ];
    }

    /**
     * Get breadcrumb trail
     */
    public function getBreadcrumbs()
    {
        $breadcrumbs = [];
        $page = $this;

        while ($page) {
            array_unshift($breadcrumbs, [
                'title' => $page->title,
                'url' => $page->getUrl()
            ]);
            $page = $page->parent;
        }

        return $breadcrumbs;
    }

    /**
     * Get page hierarchy level
     */
    public function getLevel()
    {
        $level = 0;
        $page = $this->parent;

        while ($page) {
            $level++;
            $page = $page->parent;
        }

        return $level;
    }

    /**
     * Boot method for model events
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-generate slug before creating
        static::creating(function ($page) {
            if (empty($page->slug)) {
                $page->slug = $page->generateSlug();
            }
        });

        // Update slug if title changed
        static::updating(function ($page) {
            if ($page->isDirty('title') && empty($page->slug)) {
                $page->slug = $page->generateSlug();
            }
        });

        // Create revision before updating
        static::updating(function ($page) {
            if ($page->isDirty('content') || $page->isDirty('title')) {
                $page->createRevision();
            }
        });
    }

    /**
     * Create page revision
     */
    public function createRevision()
    {
        if (class_exists('CmsPro\Models\PageRevision')) {
            PageRevision::create([
                'page_id' => $this->id,
                'title' => $this->getOriginal('title'),
                'content' => $this->getOriginal('content'),
                'user_id' => auth()->id(),
                'created_at' => now()
            ]);
        }
    }

    /**
     * Find page by slug
     */
    public static function findBySlug($slug)
    {
        $db = app()->getDatabase();
        $page = $db->selectOne(
            "SELECT * FROM pages WHERE slug = ? AND status = 'published'",
            [$slug]
        );

        if (!$page) {
            return null;
        }

        $instance = new static();
        $instance->fill($page);
        return $instance;
    }

    /**
     * Find homepage
     */
    public static function findHomepage()
    {
        $db = app()->getDatabase();
        $page = $db->selectOne(
            "SELECT * FROM pages WHERE page_type = 'homepage' AND status = 'published' LIMIT 1"
        );

        if (!$page) {
            return null;
        }

        $instance = new static();
        $instance->fill($page);
        return $instance;
    }

    /**
     * Get published pages
     */
    public static function getPublished($limit = null)
    {
        $db = app()->getDatabase();
        $query = "SELECT * FROM pages WHERE status = 'published' ORDER BY created_at DESC";

        if ($limit) {
            $query .= " LIMIT " . (int)$limit;
        }

        $pages = $db->select($query);
        $instances = [];

        foreach ($pages as $page) {
            $instance = new static();
            $instance->fill($page);
            $instances[] = $instance;
        }

        return $instances;
    }

    /**
     * Increment page views
     */
    public function incrementViews()
    {
        $db = app()->getDatabase();
        $db->update(
            "UPDATE pages SET views = views + 1 WHERE id = ?",
            [$this->getId()]
        );

        // Update current instance
        $this->attributes['views'] = ($this->attributes['views'] ?? 0) + 1;
    }

    /**
     * Check if page is password protected
     */
    public function isPasswordProtected()
    {
        $settings = $this->getSettings();
        return isset($settings['password_protected']) && $settings['password_protected'];
    }

    /**
     * Get SEO title
     */
    public function getSeoTitle()
    {
        return $this->getAttribute('meta_title') ?: $this->getTitle();
    }

    /**
     * Get SEO description
     */
    public function getSeoDescription()
    {
        return $this->getAttribute('meta_description') ?: $this->getExcerpt();
    }

    /**
     * Get SEO keywords
     */
    public function getSeoKeywords()
    {
        return $this->getAttribute('meta_keywords');
    }

    /**
     * Get featured image
     */
    public function getFeaturedImage()
    {
        return $this->getAttribute('featured_image');
    }

    /**
     * Get author
     */
    public function getAuthor()
    {
        $authorId = $this->getAttribute('author_id');
        if (!$authorId) {
            return null;
        }

        $db = app()->getDatabase();
        $author = $db->selectOne(
            "SELECT first_name, last_name FROM users WHERE id = ?",
            [$authorId]
        );

        if (!$author) {
            return null;
        }

        return trim($author['first_name'] . ' ' . $author['last_name']);
    }

    /**
     * Get published at date
     */
    public function getPublishedAt()
    {
        return $this->getAttribute('published_at');
    }

    /**
     * Get updated at date
     */
    public function getUpdatedAt()
    {
        return $this->getAttribute('updated_at');
    }

    /**
     * Get views count
     */
    public function getViews()
    {
        return (int) $this->getAttribute('views', 0);
    }

    /**
     * Get tags
     */
    public function getTags()
    {
        $customFields = $this->getCustomFields();
        if (isset($customFields['tags'])) {
            return is_array($customFields['tags']) ? $customFields['tags'] : explode(',', $customFields['tags']);
        }
        return [];
    }
}
