<?xml version="1.0"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
    <file source-language="en" datatype="plaintext" original="file.ext">
        <body>
            <trans-unit id="1">
                <source>This value should be false.</source>
                <target>This value should be false.</target>
            </trans-unit>
            <trans-unit id="2">
                <source>This value should be true.</source>
                <target>This value should be true.</target>
            </trans-unit>
            <trans-unit id="3">
                <source>This value should be of type {{ type }}.</source>
                <target>This value should be of type {{ type }}.</target>
            </trans-unit>
            <trans-unit id="4">
                <source>This value should be blank.</source>
                <target>This value should be blank.</target>
            </trans-unit>
            <trans-unit id="5">
                <source>The value you selected is not a valid choice.</source>
                <target>The value you selected is not a valid choice.</target>
            </trans-unit>
            <trans-unit id="6">
                <source>You must select at least {{ limit }} choice.|You must select at least {{ limit }} choices.</source>
                <target>You must select at least {{ limit }} choice.|You must select at least {{ limit }} choices.</target>
            </trans-unit>
            <trans-unit id="7">
                <source>You must select at most {{ limit }} choice.|You must select at most {{ limit }} choices.</source>
                <target>You must select at most {{ limit }} choice.|You must select at most {{ limit }} choices.</target>
            </trans-unit>
            <trans-unit id="8">
                <source>One or more of the given values is invalid.</source>
                <target>One or more of the given values is invalid.</target>
            </trans-unit>
            <trans-unit id="9">
                <source>This field was not expected.</source>
                <target>This field was not expected.</target>
            </trans-unit>
            <trans-unit id="10">
                <source>This field is missing.</source>
                <target>This field is missing.</target>
            </trans-unit>
            <trans-unit id="11">
                <source>This value is not a valid date.</source>
                <target>This value is not a valid date.</target>
            </trans-unit>
            <trans-unit id="12">
                <source>This value is not a valid datetime.</source>
                <target>This value is not a valid datetime.</target>
            </trans-unit>
            <trans-unit id="13">
                <source>This value is not a valid email address.</source>
                <target>This value is not a valid email address.</target>
            </trans-unit>
            <trans-unit id="14">
                <source>The file could not be found.</source>
                <target>The file could not be found.</target>
            </trans-unit>
            <trans-unit id="15">
                <source>The file is not readable.</source>
                <target>The file is not readable.</target>
            </trans-unit>
            <trans-unit id="16">
                <source>The file is too large ({{ size }} {{ suffix }}). Allowed maximum size is {{ limit }} {{ suffix }}.</source>
                <target>The file is too large ({{ size }} {{ suffix }}). Allowed maximum size is {{ limit }} {{ suffix }}.</target>
            </trans-unit>
            <trans-unit id="17">
                <source>The mime type of the file is invalid ({{ type }}). Allowed mime types are {{ types }}.</source>
                <target>The mime type of the file is invalid ({{ type }}). Allowed mime types are {{ types }}.</target>
            </trans-unit>
            <trans-unit id="18">
                <source>This value should be {{ limit }} or less.</source>
                <target>This value should be {{ limit }} or less.</target>
            </trans-unit>
            <trans-unit id="19">
                <source>This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.</source>
                <target>This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.</target>
            </trans-unit>
            <trans-unit id="20">
                <source>This value should be {{ limit }} or more.</source>
                <target>This value should be {{ limit }} or more.</target>
            </trans-unit>
            <trans-unit id="21">
                <source>This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.</source>
                <target>This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.</target>
            </trans-unit>
            <trans-unit id="22">
                <source>This value should not be blank.</source>
                <target>This value should not be blank.</target>
            </trans-unit>
            <trans-unit id="23">
                <source>This value should not be null.</source>
                <target>This value should not be null.</target>
            </trans-unit>
            <trans-unit id="24">
                <source>This value should be null.</source>
                <target>This value should be null.</target>
            </trans-unit>
            <trans-unit id="25">
                <source>This value is not valid.</source>
                <target>This value is not valid.</target>
            </trans-unit>
            <trans-unit id="26">
                <source>This value is not a valid time.</source>
                <target>This value is not a valid time.</target>
            </trans-unit>
            <trans-unit id="27">
                <source>This value is not a valid URL.</source>
                <target>This value is not a valid URL.</target>
            </trans-unit>
            <trans-unit id="31">
                <source>The two values should be equal.</source>
                <target>The two values should be equal.</target>
            </trans-unit>
            <trans-unit id="32">
                <source>The file is too large. Allowed maximum size is {{ limit }} {{ suffix }}.</source>
                <target>The file is too large. Allowed maximum size is {{ limit }} {{ suffix }}.</target>
            </trans-unit>
            <trans-unit id="33">
                <source>The file is too large.</source>
                <target>The file is too large.</target>
            </trans-unit>
            <trans-unit id="34">
                <source>The file could not be uploaded.</source>
                <target>The file could not be uploaded.</target>
            </trans-unit>
            <trans-unit id="35">
                <source>This value should be a valid number.</source>
                <target>This value should be a valid number.</target>
            </trans-unit>
            <trans-unit id="36">
                <source>This file is not a valid image.</source>
                <target>This file is not a valid image.</target>
            </trans-unit>
            <trans-unit id="37">
                <source>This is not a valid IP address.</source>
                <target>This is not a valid IP address.</target>
            </trans-unit>
            <trans-unit id="38">
                <source>This value is not a valid language.</source>
                <target>This value is not a valid language.</target>
            </trans-unit>
            <trans-unit id="39">
                <source>This value is not a valid locale.</source>
                <target>This value is not a valid locale.</target>
            </trans-unit>
            <trans-unit id="40">
                <source>This value is not a valid country.</source>
                <target>This value is not a valid country.</target>
            </trans-unit>
            <trans-unit id="41">
                <source>This value is already used.</source>
                <target>This value is already used.</target>
            </trans-unit>
            <trans-unit id="42">
                <source>The size of the image could not be detected.</source>
                <target>The size of the image could not be detected.</target>
            </trans-unit>
            <trans-unit id="43">
                <source>The image width is too big ({{ width }}px). Allowed maximum width is {{ max_width }}px.</source>
                <target>The image width is too big ({{ width }}px). Allowed maximum width is {{ max_width }}px.</target>
            </trans-unit>
            <trans-unit id="44">
                <source>The image width is too small ({{ width }}px). Minimum width expected is {{ min_width }}px.</source>
                <target>The image width is too small ({{ width }}px). Minimum width expected is {{ min_width }}px.</target>
            </trans-unit>
            <trans-unit id="45">
                <source>The image height is too big ({{ height }}px). Allowed maximum height is {{ max_height }}px.</source>
                <target>The image height is too big ({{ height }}px). Allowed maximum height is {{ max_height }}px.</target>
            </trans-unit>
            <trans-unit id="46">
                <source>The image height is too small ({{ height }}px). Minimum height expected is {{ min_height }}px.</source>
                <target>The image height is too small ({{ height }}px). Minimum height expected is {{ min_height }}px.</target>
            </trans-unit>
            <trans-unit id="47">
                <source>This value should be the user's current password.</source>
                <target>This value should be the user's current password.</target>
            </trans-unit>
            <trans-unit id="48">
                <source>This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.</source>
                <target>This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.</target>
            </trans-unit>
            <trans-unit id="49">
                <source>The file was only partially uploaded.</source>
                <target>The file was only partially uploaded.</target>
            </trans-unit>
            <trans-unit id="50">
                <source>No file was uploaded.</source>
                <target>No file was uploaded.</target>
            </trans-unit>
            <trans-unit id="51">
                <source>No temporary folder was configured in php.ini.</source>
                <target>No temporary folder was configured in php.ini, or the configured folder does not exist.</target>
            </trans-unit>
            <trans-unit id="52">
                <source>Cannot write temporary file to disk.</source>
                <target>Cannot write temporary file to disk.</target>
            </trans-unit>
            <trans-unit id="53">
                <source>A PHP extension caused the upload to fail.</source>
                <target>A PHP extension caused the upload to fail.</target>
            </trans-unit>
            <trans-unit id="54">
                <source>This collection should contain {{ limit }} element or more.|This collection should contain {{ limit }} elements or more.</source>
                <target>This collection should contain {{ limit }} element or more.|This collection should contain {{ limit }} elements or more.</target>
            </trans-unit>
            <trans-unit id="55">
                <source>This collection should contain {{ limit }} element or less.|This collection should contain {{ limit }} elements or less.</source>
                <target>This collection should contain {{ limit }} element or less.|This collection should contain {{ limit }} elements or less.</target>
            </trans-unit>
            <trans-unit id="56">
                <source>This collection should contain exactly {{ limit }} element.|This collection should contain exactly {{ limit }} elements.</source>
                <target>This collection should contain exactly {{ limit }} element.|This collection should contain exactly {{ limit }} elements.</target>
            </trans-unit>
            <trans-unit id="57">
                <source>Invalid card number.</source>
                <target>Invalid card number.</target>
            </trans-unit>
            <trans-unit id="58">
                <source>Unsupported card type or invalid card number.</source>
                <target>Unsupported card type or invalid card number.</target>
            </trans-unit>
            <trans-unit id="59">
                <source>This is not a valid International Bank Account Number (IBAN).</source>
                <target>This is not a valid International Bank Account Number (IBAN).</target>
            </trans-unit>
            <trans-unit id="60">
                <source>This value is not a valid ISBN-10.</source>
                <target>This value is not a valid ISBN-10.</target>
            </trans-unit>
            <trans-unit id="61">
                <source>This value is not a valid ISBN-13.</source>
                <target>This value is not a valid ISBN-13.</target>
            </trans-unit>
            <trans-unit id="62">
                <source>This value is neither a valid ISBN-10 nor a valid ISBN-13.</source>
                <target>This value is neither a valid ISBN-10 nor a valid ISBN-13.</target>
            </trans-unit>
            <trans-unit id="63">
                <source>This value is not a valid ISSN.</source>
                <target>This value is not a valid ISSN.</target>
            </trans-unit>
            <trans-unit id="64">
                <source>This value is not a valid currency.</source>
                <target>This value is not a valid currency.</target>
            </trans-unit>
            <trans-unit id="65">
                <source>This value should be equal to {{ compared_value }}.</source>
                <target>This value should be equal to {{ compared_value }}.</target>
            </trans-unit>
            <trans-unit id="66">
                <source>This value should be greater than {{ compared_value }}.</source>
                <target>This value should be greater than {{ compared_value }}.</target>
            </trans-unit>
            <trans-unit id="67">
                <source>This value should be greater than or equal to {{ compared_value }}.</source>
                <target>This value should be greater than or equal to {{ compared_value }}.</target>
            </trans-unit>
            <trans-unit id="68">
                <source>This value should be identical to {{ compared_value_type }} {{ compared_value }}.</source>
                <target>This value should be identical to {{ compared_value_type }} {{ compared_value }}.</target>
            </trans-unit>
            <trans-unit id="69">
                <source>This value should be less than {{ compared_value }}.</source>
                <target>This value should be less than {{ compared_value }}.</target>
            </trans-unit>
            <trans-unit id="70">
                <source>This value should be less than or equal to {{ compared_value }}.</source>
                <target>This value should be less than or equal to {{ compared_value }}.</target>
            </trans-unit>
            <trans-unit id="71">
                <source>This value should not be equal to {{ compared_value }}.</source>
                <target>This value should not be equal to {{ compared_value }}.</target>
            </trans-unit>
            <trans-unit id="72">
                <source>This value should not be identical to {{ compared_value_type }} {{ compared_value }}.</source>
                <target>This value should not be identical to {{ compared_value_type }} {{ compared_value }}.</target>
            </trans-unit>
            <trans-unit id="73">
                <source>The image ratio is too big ({{ ratio }}). Allowed maximum ratio is {{ max_ratio }}.</source>
                <target>The image ratio is too big ({{ ratio }}). Allowed maximum ratio is {{ max_ratio }}.</target>
            </trans-unit>
            <trans-unit id="74">
                <source>The image ratio is too small ({{ ratio }}). Minimum ratio expected is {{ min_ratio }}.</source>
                <target>The image ratio is too small ({{ ratio }}). Minimum ratio expected is {{ min_ratio }}.</target>
            </trans-unit>
            <trans-unit id="75">
                <source>The image is square ({{ width }}x{{ height }}px). Square images are not allowed.</source>
                <target>The image is square ({{ width }}x{{ height }}px). Square images are not allowed.</target>
            </trans-unit>
            <trans-unit id="76">
                <source>The image is landscape oriented ({{ width }}x{{ height }}px). Landscape oriented images are not allowed.</source>
                <target>The image is landscape oriented ({{ width }}x{{ height }}px). Landscape oriented images are not allowed.</target>
            </trans-unit>
            <trans-unit id="77">
                <source>The image is portrait oriented ({{ width }}x{{ height }}px). Portrait oriented images are not allowed.</source>
                <target>The image is portrait oriented ({{ width }}x{{ height }}px). Portrait oriented images are not allowed.</target>
            </trans-unit>
            <trans-unit id="78">
                <source>An empty file is not allowed.</source>
                <target>An empty file is not allowed.</target>
            </trans-unit>
            <trans-unit id="79">
                <source>The host could not be resolved.</source>
                <target>The host could not be resolved.</target>
            </trans-unit>
            <trans-unit id="80">
                <source>This value does not match the expected {{ charset }} charset.</source>
                <target>This value does not match the expected {{ charset }} charset.</target>
            </trans-unit>
            <trans-unit id="81">
                <source>This is not a valid Business Identifier Code (BIC).</source>
                <target>This is not a valid Business Identifier Code (BIC).</target>
            </trans-unit>
            <trans-unit id="82">
                <source>Error</source>
                <target>Error</target>
            </trans-unit>
            <trans-unit id="83">
                <source>This is not a valid UUID.</source>
                <target>This is not a valid UUID.</target>
            </trans-unit>
            <trans-unit id="84">
                <source>This value should be a multiple of {{ compared_value }}.</source>
                <target>This value should be a multiple of {{ compared_value }}.</target>
            </trans-unit>
            <trans-unit id="85">
                <source>This Business Identifier Code (BIC) is not associated with IBAN {{ iban }}.</source>
                <target>This Business Identifier Code (BIC) is not associated with IBAN {{ iban }}.</target>
            </trans-unit>
            <trans-unit id="86">
                <source>This value should be valid JSON.</source>
                <target>This value should be valid JSON.</target>
            </trans-unit>
            <trans-unit id="87">
                <source>This collection should contain only unique elements.</source>
                <target>This collection should contain only unique elements.</target>
            </trans-unit>
            <trans-unit id="88">
                <source>This value should be positive.</source>
                <target>This value should be positive.</target>
            </trans-unit>
            <trans-unit id="89">
                <source>This value should be either positive or zero.</source>
                <target>This value should be either positive or zero.</target>
            </trans-unit>
            <trans-unit id="90">
                <source>This value should be negative.</source>
                <target>This value should be negative.</target>
            </trans-unit>
            <trans-unit id="91">
                <source>This value should be either negative or zero.</source>
                <target>This value should be either negative or zero.</target>
            </trans-unit>
            <trans-unit id="92">
                <source>This value is not a valid timezone.</source>
                <target>This value is not a valid timezone.</target>
            </trans-unit>
            <trans-unit id="93">
                <source>This password has been leaked in a data breach, it must not be used. Please use another password.</source>
                <target>This password has been leaked in a data breach, it must not be used. Please use another password.</target>
            </trans-unit>
            <trans-unit id="94">
                <source>This value should be between {{ min }} and {{ max }}.</source>
                <target>This value should be between {{ min }} and {{ max }}.</target>
            </trans-unit>
            <trans-unit id="95">
                <source>This value is not a valid hostname.</source>
                <target>This value is not a valid hostname.</target>
            </trans-unit>
            <trans-unit id="96">
                <source>The number of elements in this collection should be a multiple of {{ compared_value }}.</source>
                <target>The number of elements in this collection should be a multiple of {{ compared_value }}.</target>
            </trans-unit>
            <trans-unit id="97">
                <source>This value should satisfy at least one of the following constraints:</source>
                <target>This value should satisfy at least one of the following constraints:</target>
            </trans-unit>
            <trans-unit id="98">
                <source>Each element of this collection should satisfy its own set of constraints.</source>
                <target>Each element of this collection should satisfy its own set of constraints.</target>
            </trans-unit>
            <trans-unit id="99">
                <source>This value is not a valid International Securities Identification Number (ISIN).</source>
                <target>This value is not a valid International Securities Identification Number (ISIN).</target>
            </trans-unit>
            <trans-unit id="100">
                <source>This value should be a valid expression.</source>
                <target>This value should be a valid expression.</target>
            </trans-unit>
            <trans-unit id="101">
                <source>This value is not a valid CSS color.</source>
                <target>This value is not a valid CSS color.</target>
            </trans-unit>
            <trans-unit id="102">
                <source>This value is not a valid CIDR notation.</source>
                <target>This value is not a valid CIDR notation.</target>
            </trans-unit>
            <trans-unit id="103">
                <source>The value of the netmask should be between {{ min }} and {{ max }}.</source>
                <target>The value of the netmask should be between {{ min }} and {{ max }}.</target>
            </trans-unit>
        </body>
    </file>
</xliff>
