{% extends "layouts/frontend.twig" %}

{% block title %}{{ title ?? 'Page Not Found' }}{% endblock %}

{% block content %}
<div class="error-page-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="error-content text-center py-5">
                    <!-- Error Code -->
                    <div class="error-code mb-4">
                        <h1 class="display-1 fw-bold text-primary">404</h1>
                    </div>
                    
                    <!-- Error Message -->
                    <div class="error-message mb-4">
                        <h2 class="h3 mb-3">Sayfa Bulunamadı</h2>
                        <p class="lead text-muted mb-4">
                            {{ message ?? 'Aradığınız sayfa kaldırılmış, adı değiştirilmiş veya geçici olarak kullanılamıyor olabilir.' }}
                        </p>
                    </div>
                    
                    <!-- Search Box -->
                    <div class="error-search mb-4">
                        <form action="{{ url('/search') }}" method="GET" class="d-flex justify-content-center">
                            <div class="input-group" style="max-width: 400px;">
                                <input type="text" name="q" class="form-control form-control-lg"
                                       placeholder="İçerikte ara..."
                                       value="{{ app.request.query.get('q', '') }}">
                                <button class="btn btn-primary btn-lg" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="error-actions mb-5">
                        <a href="{{ url('/') }}" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-home me-2"></i>Ana Sayfaya Git
                        </a>
                        <button type="button" class="btn btn-outline-secondary btn-lg" onclick="history.back()">
                            <i class="fas fa-arrow-left me-2"></i>Geri Git
                        </button>
                    </div>
                    
                    <!-- Helpful Links -->
                    <div class="helpful-links">
                        <h5 class="mb-3">Aradığınız şunlar olabilir:</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <a href="{{ url('/') }}" class="text-decoration-none">
                                            <i class="fas fa-home me-2 text-primary"></i>Ana Sayfa
                                        </a>
                                    </li>
                                    <li class="mb-2">
                                        <a href="{{ url('/blog') }}" class="text-decoration-none">
                                            <i class="fas fa-blog me-2 text-primary"></i>Blog
                                        </a>
                                    </li>
                                    <li class="mb-2">
                                        <a href="{{ url('/iletisim') }}" class="text-decoration-none">
                                            <i class="fas fa-envelope me-2 text-primary"></i>İletişim
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <a href="{{ url('/arama') }}" class="text-decoration-none">
                                            <i class="fas fa-search me-2 text-primary"></i>Arama
                                        </a>
                                    </li>
                                    <li class="mb-2">
                                        <a href="{{ url('/hakkimizda') }}" class="text-decoration-none">
                                            <i class="fas fa-info-circle me-2 text-primary"></i>Hakkımızda
                                        </a>
                                    </li>
                                    <li class="mb-2">
                                        <a href="{{ url('/hizmetler') }}" class="text-decoration-none">
                                            <i class="fas fa-cogs me-2 text-primary"></i>Hizmetler
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
// Auto-focus search input
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[name="q"]');
    if (searchInput) {
        searchInput.focus();
    }
});

// Report broken link function
function reportBrokenLink() {
    const currentUrl = window.location.href;
    const referrer = document.referrer || 'Direct access';
    
    // You can implement this to send to your analytics or support system
    if (confirm('{{ __("Would you like to report this broken link?") }}')) {
        // Simple implementation - you can enhance this
        const subject = encodeURIComponent('Broken Link Report');
        const body = encodeURIComponent(`Broken URL: ${currentUrl}\nReferrer: ${referrer}\nUser Agent: ${navigator.userAgent}`);
        const mailtoLink = `mailto:{{ site_settings.admin_email ?? '<EMAIL>' }}?subject=${subject}&body=${body}`;
        
        window.location.href = mailtoLink;
    }
}

// Track 404 errors (if analytics is available)
if (typeof gtag !== 'undefined') {
    gtag('event', 'page_not_found', {
        'page_location': window.location.href,
        'page_referrer': document.referrer
    });
}
</script>

<style>
/* 404 Page Styles */
.error-page-container {
    min-height: 70vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.error-code h1 {
    font-size: 8rem;
    line-height: 1;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.error-message h2 {
    color: #495057;
}

.helpful-links {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.helpful-links a {
    color: #6c757d;
    transition: color 0.3s ease;
}

.helpful-links a:hover {
    color: #007bff;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .error-code h1 {
        font-size: 5rem;
    }
    
    .error-actions .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .error-actions .btn:last-child {
        margin-bottom: 0;
    }
}

/* Animation */
.error-content {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Dark mode support */
[data-bs-theme="dark"] .error-page-container {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
}

[data-bs-theme="dark"] .helpful-links {
    background: #374151;
    color: #e5e7eb;
}

[data-bs-theme="dark"] .error-message h2 {
    color: #e5e7eb;
}
</style>
{% endblock %}
