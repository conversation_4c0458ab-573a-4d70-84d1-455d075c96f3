{% extends "layouts/frontend.twig" %}

{% block title %}Blog - {{ site_settings.site_name }}{% endblock %}

{% block content %}
<!-- Page Header -->
<section class="page-header bg-primary text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="display-4 fw-bold mb-3">Blog</h1>
                <p class="lead">Teknoloji dünyasından son haberler ve faydalı içerikler</p>
            </div>
        </div>
    </div>
</section>

<!-- Blog Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                {% if posts %}
                    {% for post in posts %}
                    <article class="blog-post mb-5">
                        <!-- Featured Image -->
                        <div class="post-image mb-4">
                            {% if post.featured_image %}
                                <img src="{{ post.featured_image }}" alt="{{ post.title }}" class="img-fluid rounded">
                            {% else %}
                                <div class="placeholder-image bg-light d-flex align-items-center justify-content-center rounded" style="height: 300px;">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Post Meta -->
                        <div class="post-meta mb-3">
                            <div class="d-flex flex-wrap align-items-center gap-3">
                                {% if post.category_name %}
                                <span class="badge bg-primary">{{ post.category_name }}</span>
                                {% endif %}
                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i>{{ post.author_name ?? 'Admin' }}
                                </small>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>{{ post.created_at|date('d.m.Y') }}
                                </small>
                                <small class="text-muted">
                                    <i class="fas fa-eye me-1"></i>{{ post.views ?? 0 }} görüntülenme
                                </small>
                                {% if post.likes %}
                                <small class="text-muted">
                                    <i class="fas fa-heart me-1"></i>{{ post.likes }} beğeni
                                </small>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Post Title -->
                        <h2 class="post-title mb-3">
                            <a href="{{ url('/blog/' ~ post.slug) }}" class="text-decoration-none text-dark">
                                {{ post.title }}
                            </a>
                        </h2>
                        
                        <!-- Post Excerpt -->
                        <div class="post-excerpt mb-4">
                            <p class="text-muted">
                                {{ post.content|striptags|slice(0, 200) }}...
                            </p>
                        </div>
                        
                        <!-- Read More -->
                        <div class="post-actions">
                            <a href="{{ url('/blog/' ~ post.slug) }}" class="btn btn-primary">
                                Devamını Oku <i class="fas fa-arrow-right ms-1"></i>
                            </a>
                        </div>
                    </article>
                    
                    {% if not loop.last %}
                    <hr class="my-5">
                    {% endif %}
                    {% endfor %}
                    
                    <!-- Pagination -->
                    <nav class="mt-5">
                        <ul class="pagination justify-content-center">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">Önceki</a>
                            </li>
                            <li class="page-item active">
                                <a class="page-link" href="#">1</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">3</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">Sonraki</a>
                            </li>
                        </ul>
                    </nav>
                {% else %}
                    <!-- Empty State -->
                    <div class="text-center py-5">
                        <i class="fas fa-blog fa-5x text-muted mb-4"></i>
                        <h3 class="text-muted mb-3">Henüz blog yazısı bulunmuyor</h3>
                        <p class="text-muted">Yakında ilginç içeriklerle burada olacağız!</p>
                        <a href="{{ url('/') }}" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>Ana Sayfaya Dön
                        </a>
                    </div>
                {% endif %}
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Search Widget -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Arama</h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ url('/arama') }}" method="GET">
                            <div class="input-group">
                                <input type="text" name="q" class="form-control" placeholder="Blog yazılarında ara...">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Categories Widget -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Kategoriler</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <a href="#" class="text-decoration-none d-flex justify-content-between align-items-center">
                                    <span>Web Tasarım</span>
                                    <span class="badge bg-primary">5</span>
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="#" class="text-decoration-none d-flex justify-content-between align-items-center">
                                    <span>Programlama</span>
                                    <span class="badge bg-primary">8</span>
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="#" class="text-decoration-none d-flex justify-content-between align-items-center">
                                    <span>SEO</span>
                                    <span class="badge bg-primary">3</span>
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="#" class="text-decoration-none d-flex justify-content-between align-items-center">
                                    <span>Teknoloji</span>
                                    <span class="badge bg-primary">12</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="text-decoration-none d-flex justify-content-between align-items-center">
                                    <span>Genel</span>
                                    <span class="badge bg-primary">4</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <!-- Recent Posts Widget -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Son Yazılar</h5>
                    </div>
                    <div class="card-body">
                        {% if posts|slice(0, 3) %}
                            {% for post in posts|slice(0, 3) %}
                            <div class="d-flex mb-3">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary rounded d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                        <i class="fas fa-blog text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1">
                                        <a href="{{ url('/blog/' ~ post.slug) }}" class="text-decoration-none">
                                            {{ post.title|slice(0, 50) }}{% if post.title|length > 50 %}...{% endif %}
                                        </a>
                                    </h6>
                                    <small class="text-muted">{{ post.created_at|date('d.m.Y') }}</small>
                                </div>
                            </div>
                            {% if not loop.last %}<hr>{% endif %}
                            {% endfor %}
                        {% else %}
                            <p class="text-muted mb-0">Henüz yazı bulunmuyor.</p>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Tags Widget -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Etiketler</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex flex-wrap gap-2">
                            <a href="#" class="badge bg-light text-dark text-decoration-none">HTML</a>
                            <a href="#" class="badge bg-light text-dark text-decoration-none">CSS</a>
                            <a href="#" class="badge bg-light text-dark text-decoration-none">JavaScript</a>
                            <a href="#" class="badge bg-light text-dark text-decoration-none">PHP</a>
                            <a href="#" class="badge bg-light text-dark text-decoration-none">MySQL</a>
                            <a href="#" class="badge bg-light text-dark text-decoration-none">Bootstrap</a>
                            <a href="#" class="badge bg-light text-dark text-decoration-none">Responsive</a>
                            <a href="#" class="badge bg-light text-dark text-decoration-none">SEO</a>
                            <a href="#" class="badge bg-light text-dark text-decoration-none">CMS</a>
                            <a href="#" class="badge bg-light text-dark text-decoration-none">Blog</a>
                        </div>
                    </div>
                </div>
                
                <!-- Newsletter Widget -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Bülten</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">Son yazılarımızdan haberdar olmak için bültenimize abone olun.</p>
                        <form>
                            <div class="mb-3">
                                <input type="email" class="form-control" placeholder="E-posta adresiniz" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-envelope me-2"></i>Abone Ol
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block scripts %}
<script>
// Smooth animations for blog posts
const posts = document.querySelectorAll('.blog-post');
posts.forEach((post, index) => {
    post.style.opacity = '0';
    post.style.transform = 'translateY(30px)';
    post.style.transition = 'all 0.6s ease';
    
    setTimeout(() => {
        post.style.opacity = '1';
        post.style.transform = 'translateY(0)';
    }, index * 200);
});

// Newsletter form submission
document.querySelector('form').addEventListener('submit', function(e) {
    e.preventDefault();
    const email = this.querySelector('input[type="email"]').value;
    if (email) {
        alert('Teşekkürler! Bültenimize abone oldunuz.');
        this.reset();
    }
});
</script>
{% endblock %}
