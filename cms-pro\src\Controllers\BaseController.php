<?php

namespace CmsPro\Controllers;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;

/**
 * Base Controller
 * 
 * @package CmsPro\Controllers
 */
abstract class BaseController
{
    protected $app;
    protected $request;

    public function __construct()
    {
        $this->app = app();
    }

    /**
     * Render view
     */
    protected function view($template, $data = [])
    {
        $content = view($template, $data);
        return new Response($content);
    }

    /**
     * Return JSON response
     */
    protected function json($data, $status = 200)
    {
        return new JsonResponse($data, $status);
    }

    /**
     * Return redirect response
     */
    protected function redirect($url, $status = 302)
    {
        return new RedirectResponse($url, $status);
    }

    /**
     * Redirect to route
     */
    protected function redirectToRoute($name, $parameters = [], $status = 302)
    {
        $url = route($name, $parameters);
        return $this->redirect($url, $status);
    }

    /**
     * Redirect back
     */
    protected function back($fallback = '/')
    {
        $referer = $this->request->headers->get('referer', $fallback);
        return $this->redirect($referer);
    }

    /**
     * Validate request data
     */
    protected function validate(Request $request, array $rules)
    {
        // This will be implemented when we create the Validator
        // For now, return the request data
        return $request->request->all();
    }

    /**
     * Get authenticated user
     */
    protected function user()
    {
        return user();
    }

    /**
     * Check if user is authenticated
     */
    protected function auth()
    {
        return auth();
    }

    /**
     * Flash message to session
     */
    protected function flash($type, $message)
    {
        session()->flash($type, $message);
    }

    /**
     * Flash success message
     */
    protected function flashSuccess($message)
    {
        $this->flash('success', $message);
    }

    /**
     * Flash error message
     */
    protected function flashError($message)
    {
        $this->flash('error', $message);
    }

    /**
     * Flash warning message
     */
    protected function flashWarning($message)
    {
        $this->flash('warning', $message);
    }

    /**
     * Flash info message
     */
    protected function flashInfo($message)
    {
        $this->flash('info', $message);
    }

    /**
     * Get request input
     */
    protected function input($key = null, $default = null)
    {
        if ($key === null) {
            return $this->request->request->all();
        }
        
        return $this->request->request->get($key, $default);
    }

    /**
     * Get query parameter
     */
    protected function query($key = null, $default = null)
    {
        if ($key === null) {
            return $this->request->query->all();
        }
        
        return $this->request->query->get($key, $default);
    }

    /**
     * Check if request has input
     */
    protected function has($key)
    {
        return $this->request->request->has($key);
    }

    /**
     * Get uploaded file
     */
    protected function file($key)
    {
        return $this->request->files->get($key);
    }

    /**
     * Abort with error
     */
    protected function abort($code, $message = '')
    {
        throw new \RuntimeException($message, $code);
    }

    /**
     * Return 404 response
     */
    protected function notFound($message = 'Not Found')
    {
        return new Response($message, 404);
    }

    /**
     * Return 403 response
     */
    protected function forbidden($message = 'Forbidden')
    {
        return new Response($message, 403);
    }

    /**
     * Return 500 response
     */
    protected function serverError($message = 'Internal Server Error')
    {
        return new Response($message, 500);
    }
}
