{% extends "layouts/admin.twig" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Security Alerts -->
    {% if security_alerts and security_alerts|length > 0 %}
    <div class="security-alerts mb-4">
        {% for alert in security_alerts %}
        <div class="alert alert-{{ alert.type }} alert-dismissible fade show" role="alert">
            <i class="fas fa-shield-alt me-2"></i>
            <strong>Security Alert:</strong> {{ alert.message }}
            {% if alert.action %}
                <br><small><strong>Action:</strong> {{ alert.action }}</small>
            {% endif %}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Welcome Section -->
    <div class="welcome-section mb-4">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="dashboard-title">Welcome back, {{ current_user.first_name|default('Admin') }}!</h1>
                <p class="dashboard-subtitle text-muted">Here's what's happening with your site today.</p>
            </div>
            <div class="col-md-3">
                <div class="dashboard-date">
                    <i class="fas fa-calendar-alt me-2"></i>
                    {{ "now"|date("F j, Y") }}
                </div>
            </div>
            <div class="col-md-3 text-md-end">
                {% if performance %}
                <div class="performance-info">
                    <small class="text-muted">
                        <i class="fas fa-tachometer-alt me-1"></i>
                        Load: {{ performance.load_time }}ms
                        <br>
                        <i class="fas fa-memory me-1"></i>
                        Memory: {{ performance.memory_usage }}
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <!-- Users Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100 stat-card" data-widget="users">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                {{ __('Total Users') }}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.users|number_format }}
                            </div>
                            {% if quick_stats.today_logins is defined %}
                            <small class="text-muted">
                                <i class="fas fa-sign-in-alt me-1"></i>
                                {{ quick_stats.today_logins }} logins today
                            </small>
                            {% endif %}
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url('/admin/users') }}" class="btn btn-sm btn-outline-primary">
                        {{ __('Manage Users') }} <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Content Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100 stat-card" data-widget="content">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                {{ __('Content Items') }}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.content|number_format }}
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-file-alt me-1"></i>
                                {{ stats.pages|number_format }} pages, {{ stats.blog_posts|number_format }} posts
                            </small>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-alt fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url('/admin/content') }}" class="btn btn-sm btn-outline-success">
                        {{ __('Manage Content') }} <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Security Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100 stat-card" data-widget="security">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                {{ __('Security Status') }}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if quick_stats.failed_logins is defined and quick_stats.failed_logins > 0 %}
                                    <span class="text-warning">{{ quick_stats.failed_logins }}</span>
                                {% else %}
                                    <span class="text-success">Secure</span>
                                {% endif %}
                            </div>
                            {% if quick_stats.active_sessions is defined %}
                            <small class="text-muted">
                                <i class="fas fa-users me-1"></i>
                                {{ quick_stats.active_sessions }} active sessions
                            </small>
                            {% endif %}
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shield-alt fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url('/admin/security') }}" class="btn btn-sm btn-outline-warning">
                        {{ __('Security Logs') }} <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- System Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100 stat-card" data-widget="system">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                {{ __('System Health') }}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if system_info.server_load != 'N/A' and system_info.server_load < 2 %}
                                    <span class="text-success">Good</span>
                                {% elseif system_info.server_load != 'N/A' and system_info.server_load < 5 %}
                                    <span class="text-warning">Fair</span>
                                {% else %}
                                    <span class="text-danger">High Load</span>
                                {% endif %}
                            </div>
                            {% if quick_stats.storage_usage is defined %}
                            <small class="text-muted">
                                <i class="fas fa-hdd me-1"></i>
                                {{ quick_stats.storage_usage }} used
                            </small>
                            {% endif %}
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-server fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url('/admin/system') }}" class="btn btn-sm btn-outline-info">
                        {{ __('System Info') }} <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                {{ __('Content') }}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.content|number_format }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-alt fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url('/admin/content') }}" class="btn btn-sm btn-outline-success">
                        {{ __('View All') }} <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                {{ __('Pages') }}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.pages|number_format }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-copy fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url('/admin/pages') }}" class="btn btn-sm btn-outline-info">
                        {{ __('View All') }} <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                {{ __('Blog Posts') }}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.blog_posts|number_format }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-blog fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url('/admin/blog') }}" class="btn btn-sm btn-outline-warning">
                        {{ __('View All') }} <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Activity -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history me-2"></i>
                        {{ __('Recent Activity') }}
                    </h6>
                </div>
                <div class="card-body">
                    {% if recent_activity %}
                    <div class="activity-list">
                        {% for activity in recent_activity %}
                        <div class="activity-item d-flex align-items-start mb-3">
                            <div class="activity-icon me-3">
                                <i class="fas fa-{{ activity.action == 'login' ? 'sign-in-alt' : (activity.action == 'logout' ? 'sign-out-alt' : 'edit') }} text-muted"></i>
                            </div>
                            <div class="activity-content flex-grow-1">
                                <div class="activity-description">
                                    <strong>{{ activity.first_name ~ ' ' ~ activity.last_name }}</strong>
                                    {{ activity.description }}
                                </div>
                                <div class="activity-time text-muted small">
                                    <i class="fas fa-clock me-1"></i>
                                    {{ activity.created_at|date_format('d M Y H:i') }}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-history fa-3x mb-3"></i>
                        <p>{{ __('No recent activity found.') }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- System Information -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-server me-2"></i>
                        {{ __('System Information') }}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="system-info">
                        <div class="info-item d-flex justify-content-between mb-2">
                            <span class="text-muted">{{ __('CMS Version') }}:</span>
                            <span class="fw-bold">{{ system_info.cms_version }}</span>
                        </div>
                        <div class="info-item d-flex justify-content-between mb-2">
                            <span class="text-muted">{{ __('PHP Version') }}:</span>
                            <span class="fw-bold">{{ system_info.php_version }}</span>
                        </div>
                        <div class="info-item d-flex justify-content-between mb-2">
                            <span class="text-muted">{{ __('Database') }}:</span>
                            <span class="fw-bold">{{ system_info.database_type }}</span>
                        </div>
                        <div class="info-item d-flex justify-content-between mb-2">
                            <span class="text-muted">{{ __('Memory Limit') }}:</span>
                            <span class="fw-bold">{{ system_info.memory_limit }}</span>
                        </div>
                        <div class="info-item d-flex justify-content-between mb-2">
                            <span class="text-muted">{{ __('Upload Limit') }}:</span>
                            <span class="fw-bold">{{ system_info.upload_max_filesize }}</span>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url('/admin/system') }}" class="btn btn-sm btn-outline-primary w-100">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ __('View Full System Info') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>
                        {{ __('Quick Actions') }}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if user.hasPermission('content.create') %}
                        <div class="col-md-3 mb-3">
                            <a href="{{ url('/admin/content/create') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-plus me-2"></i>
                                {{ __('New Content') }}
                            </a>
                        </div>
                        {% endif %}
                        
                        {% if user.hasPermission('pages.create') %}
                        <div class="col-md-3 mb-3">
                            <a href="{{ url('/admin/pages/create') }}" class="btn btn-outline-success w-100">
                                <i class="fas fa-plus me-2"></i>
                                {{ __('New Page') }}
                            </a>
                        </div>
                        {% endif %}
                        
                        {% if user.hasPermission('blog.create') %}
                        <div class="col-md-3 mb-3">
                            <a href="{{ url('/admin/blog/create') }}" class="btn btn-outline-info w-100">
                                <i class="fas fa-plus me-2"></i>
                                {{ __('New Blog Post') }}
                            </a>
                        </div>
                        {% endif %}
                        
                        {% if user.hasPermission('users.create') %}
                        <div class="col-md-3 mb-3">
                            <a href="{{ url('/admin/users/create') }}" class="btn btn-outline-warning w-100">
                                <i class="fas fa-user-plus me-2"></i>
                                {{ __('New User') }}
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
/**
 * Secure Admin Dashboard
 * Enhanced with security monitoring and performance optimization
 */
class AdminDashboard {
    constructor() {
        this.refreshInterval = 30000; // 30 seconds
        this.securityCheckInterval = 60000; // 1 minute
        this.performanceMonitor = new PerformanceMonitor();
        this.securityMonitor = new SecurityMonitor();

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.startAutoRefresh();
        this.startSecurityMonitoring();
        this.initializeWidgets();
        this.setupRealTimeUpdates();
    }

    setupEventListeners() {
        // Refresh button
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="refresh"]')) {
                e.preventDefault();
                this.refreshDashboard();
            }
        });

        // Widget refresh
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-widget-refresh]')) {
                e.preventDefault();
                const widget = e.target.getAttribute('data-widget-refresh');
                this.refreshWidget(widget);
            }
        });

        // Security alert dismiss
        document.addEventListener('click', (e) => {
            if (e.target.matches('.security-alert .btn-close')) {
                this.dismissSecurityAlert(e.target.closest('.security-alert'));
            }
        });
    }

    async refreshDashboard() {
        try {
            this.showLoadingState();

            const response = await fetch(window.location.href, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': this.getCsrfToken()
                }
            });

            if (!response.ok) {
                throw new Error('Failed to refresh dashboard');
            }

            const data = await response.json();
            this.updateDashboardData(data);
            this.hideLoadingState();

            this.showNotification('Dashboard refreshed successfully', 'success');

        } catch (error) {
            this.hideLoadingState();
            this.showNotification('Failed to refresh dashboard', 'error');
            console.error('Dashboard refresh error:', error);
        }
    }

    async refreshWidget(widgetName) {
        try {
            const widget = document.querySelector(`[data-widget="${widgetName}"]`);
            if (!widget) return;

            widget.classList.add('loading');

            const response = await fetch(`${window.location.href}?widget=${widgetName}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': this.getCsrfToken()
                }
            });

            if (!response.ok) {
                throw new Error(`Failed to refresh ${widgetName} widget`);
            }

            const data = await response.json();
            this.updateWidget(widgetName, data);

            widget.classList.remove('loading');

        } catch (error) {
            console.error(`Widget refresh error (${widgetName}):`, error);
            const widget = document.querySelector(`[data-widget="${widgetName}"]`);
            if (widget) {
                widget.classList.remove('loading');
                widget.classList.add('error');
            }
        }
    }

    updateDashboardData(data) {
        // Update statistics
        if (data.stats) {
            this.updateStatistics(data.stats);
        }

        // Update recent activity
        if (data.recent_activity) {
            this.updateRecentActivity(data.recent_activity);
        }

        // Update security alerts
        if (data.security_alerts) {
            this.updateSecurityAlerts(data.security_alerts);
        }

        // Update system info
        if (data.system_info) {
            this.updateSystemInfo(data.system_info);
        }
    }

    updateStatistics(stats) {
        Object.keys(stats).forEach(key => {
            const element = document.querySelector(`[data-stat="${key}"]`);
            if (element) {
                element.textContent = this.formatNumber(stats[key]);
                this.animateNumber(element);
            }
        });
    }

    updateRecentActivity(activities) {
        const container = document.querySelector('.recent-activity-list');
        if (!container) return;

        container.innerHTML = '';

        activities.forEach(activity => {
            const item = this.createActivityItem(activity);
            container.appendChild(item);
        });
    }

    updateSecurityAlerts(alerts) {
        const container = document.querySelector('.security-alerts');
        if (!container) return;

        container.innerHTML = '';

        alerts.forEach(alert => {
            const alertElement = this.createSecurityAlert(alert);
            container.appendChild(alertElement);
        });
    }

    createSecurityAlert(alert) {
        const div = document.createElement('div');
        div.className = `alert alert-${alert.type} alert-dismissible fade show security-alert`;
        div.innerHTML = `
            <i class="fas fa-shield-alt me-2"></i>
            <strong>Security Alert:</strong> ${this.escapeHtml(alert.message)}
            ${alert.action ? `<br><small><strong>Action:</strong> ${this.escapeHtml(alert.action)}</small>` : ''}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        return div;
    }

    startAutoRefresh() {
        setInterval(() => {
            this.refreshDashboard();
        }, this.refreshInterval);
    }

    startSecurityMonitoring() {
        setInterval(() => {
            this.performSecurityCheck();
        }, this.securityCheckInterval);
    }

    async performSecurityCheck() {
        try {
            const response = await fetch('/admin/api/security/check', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': this.getCsrfToken()
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.alerts && data.alerts.length > 0) {
                    this.updateSecurityAlerts(data.alerts);
                }
            }

        } catch (error) {
            console.error('Security check error:', error);
        }
    }

    initializeWidgets() {
        // Initialize chart widgets
        this.initializeCharts();

        // Initialize real-time counters
        this.initializeCounters();

        // Initialize tooltips
        this.initializeTooltips();
    }

    initializeCharts() {
        // Placeholder for chart initialization
        // This would integrate with Chart.js or similar library
    }

    initializeCounters() {
        const counters = document.querySelectorAll('[data-counter]');
        counters.forEach(counter => {
            this.animateCounter(counter);
        });
    }

    initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    setupRealTimeUpdates() {
        // WebSocket connection for real-time updates
        if (typeof WebSocket !== 'undefined') {
            this.setupWebSocket();
        }
    }

    setupWebSocket() {
        // Placeholder for WebSocket implementation
        // This would connect to a WebSocket server for real-time updates
    }

    animateNumber(element) {
        element.classList.add('number-updated');
        setTimeout(() => {
            element.classList.remove('number-updated');
        }, 1000);
    }

    animateCounter(element) {
        const target = parseInt(element.textContent.replace(/,/g, ''));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = this.formatNumber(Math.floor(current));
        }, 16);
    }

    formatNumber(num) {
        return new Intl.NumberFormat().format(num);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    getCsrfToken() {
        return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    }

    showLoadingState() {
        document.body.classList.add('dashboard-loading');
    }

    hideLoadingState() {
        document.body.classList.remove('dashboard-loading');
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

/**
 * Performance Monitor
 */
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            loadTime: 0,
            memoryUsage: 0,
            networkRequests: 0
        };

        this.startMonitoring();
    }

    startMonitoring() {
        // Monitor page load time
        window.addEventListener('load', () => {
            this.metrics.loadTime = performance.now();
            this.updatePerformanceDisplay();
        });

        // Monitor memory usage (if available)
        if ('memory' in performance) {
            setInterval(() => {
                this.metrics.memoryUsage = performance.memory.usedJSHeapSize;
                this.updatePerformanceDisplay();
            }, 5000);
        }
    }

    updatePerformanceDisplay() {
        const perfElement = document.querySelector('.performance-info');
        if (perfElement && this.metrics.loadTime > 0) {
            perfElement.innerHTML = `
                <small class="text-muted">
                    <i class="fas fa-tachometer-alt me-1"></i>
                    Load: ${Math.round(this.metrics.loadTime)}ms
                    ${this.metrics.memoryUsage > 0 ? `<br><i class="fas fa-memory me-1"></i>Memory: ${this.formatBytes(this.metrics.memoryUsage)}` : ''}
                </small>
            `;
        }
    }

    formatBytes(bytes) {
        const sizes = ['B', 'KB', 'MB', 'GB'];
        if (bytes === 0) return '0 B';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }
}

/**
 * Security Monitor
 */
class SecurityMonitor {
    constructor() {
        this.suspiciousActivity = [];
        this.startMonitoring();
    }

    startMonitoring() {
        // Monitor for suspicious activity
        this.monitorClicks();
        this.monitorKeystrokes();
        this.monitorNetworkRequests();
    }

    monitorClicks() {
        let clickCount = 0;
        document.addEventListener('click', () => {
            clickCount++;
            if (clickCount > 100) { // Unusual click activity
                this.reportSuspiciousActivity('excessive_clicks', { count: clickCount });
                clickCount = 0;
            }
        });
    }

    monitorKeystrokes() {
        let keystrokeCount = 0;
        document.addEventListener('keydown', () => {
            keystrokeCount++;
            if (keystrokeCount > 1000) { // Unusual keyboard activity
                this.reportSuspiciousActivity('excessive_keystrokes', { count: keystrokeCount });
                keystrokeCount = 0;
            }
        });
    }

    monitorNetworkRequests() {
        const originalFetch = window.fetch;
        let requestCount = 0;

        window.fetch = function(...args) {
            requestCount++;
            if (requestCount > 50) { // Unusual network activity
                this.reportSuspiciousActivity('excessive_requests', { count: requestCount });
                requestCount = 0;
            }
            return originalFetch.apply(this, args);
        }.bind(this);
    }

    reportSuspiciousActivity(type, data) {
        fetch('/admin/api/security/report', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
            },
            body: JSON.stringify({
                type: type,
                data: data,
                timestamp: new Date().toISOString(),
                url: window.location.href
            })
        }).catch(() => {}); // Fail silently
    }
}

// Initialize dashboard when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    new AdminDashboard();
});
</script>

<style>
/* Dashboard Security & Performance Styles */
.dashboard-loading {
    cursor: wait;
}

.dashboard-loading .stat-card {
    opacity: 0.7;
    pointer-events: none;
}

.stat-card.loading {
    position: relative;
}

.stat-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.stat-card.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 11;
}

.stat-card.error {
    border-left: 4px solid #dc3545;
}

.number-updated {
    animation: numberPulse 1s ease-in-out;
}

.security-alert {
    animation: slideInRight 0.5s ease-out;
}

.performance-info {
    font-size: 0.75rem;
    line-height: 1.2;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes numberPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); color: #007bff; }
    100% { transform: scale(1); }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive improvements */
@media (max-width: 768px) {
    .performance-info {
        display: none;
    }

    .stat-card .h5 {
        font-size: 1.1rem;
    }

    .security-alert {
        margin: 0 -15px;
        border-radius: 0;
    }
}

/* Dark mode support */
[data-bs-theme="dark"] .stat-card {
    background-color: #2d3748;
    border-color: #4a5568;
}

[data-bs-theme="dark"] .stat-card.loading::after {
    background: rgba(45, 55, 72, 0.8);
}

[data-bs-theme="dark"] .performance-info {
    color: #a0aec0;
}
</style>
{% endblock %}
