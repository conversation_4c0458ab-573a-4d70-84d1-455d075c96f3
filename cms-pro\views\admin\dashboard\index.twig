{% extends "layouts/admin.twig" %}

{% block title %}{{ __('Dashboard') }} - {{ app.name }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0">{{ __('Dashboard') }}</h1>
            <p class="text-muted">{{ __('Welcome back, :name!', {name: user.firstName}) }}</p>
        </div>
        <div class="col-auto">
            <span class="badge bg-success">{{ __('Online') }}</span>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                {{ __('Users') }}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.users|number_format }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url('/admin/users') }}" class="btn btn-sm btn-outline-primary">
                        {{ __('View All') }} <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                {{ __('Content') }}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.content|number_format }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-alt fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url('/admin/content') }}" class="btn btn-sm btn-outline-success">
                        {{ __('View All') }} <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                {{ __('Pages') }}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.pages|number_format }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-copy fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url('/admin/pages') }}" class="btn btn-sm btn-outline-info">
                        {{ __('View All') }} <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                {{ __('Blog Posts') }}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.blog_posts|number_format }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-blog fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url('/admin/blog') }}" class="btn btn-sm btn-outline-warning">
                        {{ __('View All') }} <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Activity -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history me-2"></i>
                        {{ __('Recent Activity') }}
                    </h6>
                </div>
                <div class="card-body">
                    {% if recent_activity %}
                    <div class="activity-list">
                        {% for activity in recent_activity %}
                        <div class="activity-item d-flex align-items-start mb-3">
                            <div class="activity-icon me-3">
                                <i class="fas fa-{{ activity.action == 'login' ? 'sign-in-alt' : (activity.action == 'logout' ? 'sign-out-alt' : 'edit') }} text-muted"></i>
                            </div>
                            <div class="activity-content flex-grow-1">
                                <div class="activity-description">
                                    <strong>{{ activity.first_name ~ ' ' ~ activity.last_name }}</strong>
                                    {{ activity.description }}
                                </div>
                                <div class="activity-time text-muted small">
                                    <i class="fas fa-clock me-1"></i>
                                    {{ activity.created_at|date_format('d M Y H:i') }}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-history fa-3x mb-3"></i>
                        <p>{{ __('No recent activity found.') }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- System Information -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-server me-2"></i>
                        {{ __('System Information') }}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="system-info">
                        <div class="info-item d-flex justify-content-between mb-2">
                            <span class="text-muted">{{ __('CMS Version') }}:</span>
                            <span class="fw-bold">{{ system_info.cms_version }}</span>
                        </div>
                        <div class="info-item d-flex justify-content-between mb-2">
                            <span class="text-muted">{{ __('PHP Version') }}:</span>
                            <span class="fw-bold">{{ system_info.php_version }}</span>
                        </div>
                        <div class="info-item d-flex justify-content-between mb-2">
                            <span class="text-muted">{{ __('Database') }}:</span>
                            <span class="fw-bold">{{ system_info.database_type }}</span>
                        </div>
                        <div class="info-item d-flex justify-content-between mb-2">
                            <span class="text-muted">{{ __('Memory Limit') }}:</span>
                            <span class="fw-bold">{{ system_info.memory_limit }}</span>
                        </div>
                        <div class="info-item d-flex justify-content-between mb-2">
                            <span class="text-muted">{{ __('Upload Limit') }}:</span>
                            <span class="fw-bold">{{ system_info.upload_max_filesize }}</span>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ url('/admin/system') }}" class="btn btn-sm btn-outline-primary w-100">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ __('View Full System Info') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>
                        {{ __('Quick Actions') }}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if user.hasPermission('content.create') %}
                        <div class="col-md-3 mb-3">
                            <a href="{{ url('/admin/content/create') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-plus me-2"></i>
                                {{ __('New Content') }}
                            </a>
                        </div>
                        {% endif %}
                        
                        {% if user.hasPermission('pages.create') %}
                        <div class="col-md-3 mb-3">
                            <a href="{{ url('/admin/pages/create') }}" class="btn btn-outline-success w-100">
                                <i class="fas fa-plus me-2"></i>
                                {{ __('New Page') }}
                            </a>
                        </div>
                        {% endif %}
                        
                        {% if user.hasPermission('blog.create') %}
                        <div class="col-md-3 mb-3">
                            <a href="{{ url('/admin/blog/create') }}" class="btn btn-outline-info w-100">
                                <i class="fas fa-plus me-2"></i>
                                {{ __('New Blog Post') }}
                            </a>
                        </div>
                        {% endif %}
                        
                        {% if user.hasPermission('users.create') %}
                        <div class="col-md-3 mb-3">
                            <a href="{{ url('/admin/users/create') }}" class="btn btn-outline-warning w-100">
                                <i class="fas fa-user-plus me-2"></i>
                                {{ __('New User') }}
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
