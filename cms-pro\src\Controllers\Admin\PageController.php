<?php

namespace CmsPro\Controllers\Admin;

use CmsPro\Controllers\BaseController;
use CmsPro\Models\Page;
use CmsPro\Models\User;
use CmsPro\Services\ValidationService;
use CmsPro\Services\SanitizationService;
use CmsPro\Services\SeoService;
use CmsPro\Services\ActivityLogger;
use CmsPro\Services\FieldService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Page Controller for Admin Panel
 * 
 * @package CmsPro\Controllers\Admin
 */
class PageController extends BaseController
{
    private $validationService;
    private $sanitizationService;
    private $seoService;
    private $activityLogger;
    private $fieldService;

    public function __construct()
    {
        parent::__construct();
        
        $this->validationService = new ValidationService();
        $this->sanitizationService = new SanitizationService();
        $this->seoService = new SeoService();
        $this->activityLogger = new ActivityLogger();
        $this->fieldService = new FieldService();
    }

    /**
     * Display pages list
     */
    public function index(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('pages.view')) {
                return $this->forbidden('You do not have permission to view pages.');
            }

            // Get filters
            $status = $request->query->get('status', 'all');
            $search = $request->query->get('search', '');
            $template = $request->query->get('template', '');
            $author = $request->query->get('author', '');
            $perPage = (int) $request->query->get('per_page', 20);
            $page = (int) $request->query->get('page', 1);

            // Build query
            $query = Page::with(['author', 'parent'])
                        ->orderBy('created_at', 'desc');

            // Apply filters
            if ($status !== 'all') {
                $query->where('status', $status);
            }

            if ($search) {
                $search = $this->sanitizationService->sanitizeInput($search);
                $query->where(function($q) use ($search) {
                    $q->where('title', 'LIKE', "%{$search}%")
                      ->orWhere('content', 'LIKE', "%{$search}%")
                      ->orWhere('slug', 'LIKE', "%{$search}%");
                });
            }

            if ($template) {
                $query->byTemplate($template);
            }

            if ($author) {
                $query->where('author_id', $author);
            }

            // Get paginated results
            $pages = $query->paginate($perPage, $page);

            // Get additional data
            $templates = $this->getAvailableTemplates();
            $authors = User::select('id', 'first_name', 'last_name')
                          ->where('status', 'active')
                          ->orderBy('first_name')
                          ->get();

            // Log activity
            $this->activityLogger->log('pages_viewed', [
                'user_id' => auth()->id(),
                'filters' => compact('status', 'search', 'template', 'author')
            ]);

            $data = [
                'title' => __('Pages'),
                'pages' => $pages,
                'templates' => $templates,
                'authors' => $authors,
                'statuses' => Page::getStatuses(),
                'current_filters' => compact('status', 'search', 'template', 'author'),
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $pages['total'],
                    'pages' => $pages['pages']
                ]
            ];

            return $this->view('admin/pages/index.twig', $data);

        } catch (\Exception $e) {
            $this->logError('pages_index_error', $e, $request);
            $this->flashError(__('An error occurred while loading pages.'));
            return $this->redirectToRoute('admin.dashboard');
        }
    }

    /**
     * Show create page form
     */
    public function create(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('pages.create')) {
                return $this->forbidden('You do not have permission to create pages.');
            }

            // Get parent page if specified
            $parentId = $request->query->get('parent');
            $parent = null;
            
            if ($parentId) {
                $parent = Page::find($parentId);
                if (!$parent) {
                    $this->flashError(__('Parent page not found.'));
                    return $this->redirectToRoute('admin.pages.index');
                }
            }

            // Get available data
            $templates = $this->getAvailableTemplates();
            $parentPages = $this->getParentPageOptions();
            $customFields = $this->fieldService->getFieldsForType('page');

            $data = [
                'title' => __('Create New Page'),
                'page' => new Page(), // Empty page for form
                'parent' => $parent,
                'templates' => $templates,
                'parent_pages' => $parentPages,
                'custom_fields' => $customFields,
                'statuses' => Page::getStatuses()
            ];

            return $this->view('admin/pages/create.twig', $data);

        } catch (\Exception $e) {
            $this->logError('page_create_form_error', $e, $request);
            $this->flashError(__('An error occurred while loading the create form.'));
            return $this->redirectToRoute('admin.pages.index');
        }
    }

    /**
     * Store new page
     */
    public function store(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('pages.create')) {
                return $this->forbidden('You do not have permission to create pages.');
            }

            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Invalid CSRF token'
                    ], 403);
                }
                
                $this->flashError(__('Security token mismatch. Please try again.'));
                return $this->back();
            }

            // Validate input
            $rules = [
                'title' => 'required|string|max:255',
                'slug' => 'nullable|string|max:255|unique:pages,slug',
                'content' => 'required|string',
                'excerpt' => 'nullable|string|max:500',
                'status' => 'required|in:' . implode(',', array_keys(Page::getStatuses())),
                'template' => 'nullable|string|max:100',
                'meta_title' => 'nullable|string|max:255',
                'meta_description' => 'nullable|string|max:500',
                'meta_keywords' => 'nullable|string|max:255',
                'parent_id' => 'nullable|integer|exists:pages,id',
                'published_at' => 'nullable|date',
                'scheduled_at' => 'nullable|date|after:now',
                'featured_image' => 'nullable|string|max:255'
            ];

            $data = $this->validationService->validate($request->request->all(), $rules);

            // Sanitize content
            $data['title'] = $this->sanitizationService->sanitizeInput($data['title']);
            $data['content'] = $this->sanitizationService->sanitizeRichText($data['content']);
            $data['excerpt'] = $this->sanitizationService->sanitizeInput($data['excerpt'] ?? '');

            // Generate slug if not provided
            if (empty($data['slug'])) {
                $page = new Page();
                $data['slug'] = $page->generateSlug($data['title']);
            }

            // Set author
            $data['author_id'] = auth()->id();

            // Handle scheduling
            if ($data['status'] === Page::STATUS_SCHEDULED && !empty($data['scheduled_at'])) {
                $data['published_at'] = null;
            } elseif ($data['status'] === Page::STATUS_PUBLISHED) {
                $data['published_at'] = $data['published_at'] ?? now();
                $data['scheduled_at'] = null;
            }

            // Handle custom fields
            $customFields = $request->request->get('custom_fields', []);
            if (!empty($customFields)) {
                $data['custom_fields'] = $this->fieldService->processFieldData($customFields, 'page');
            }

            // Create page
            $page = Page::create($data);

            // Log activity
            $this->activityLogger->log('page_created', [
                'user_id' => auth()->id(),
                'page_id' => $page->id,
                'page_title' => $page->title,
                'status' => $page->status
            ]);

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => true,
                    'message' => __('Page created successfully.'),
                    'redirect' => route('admin.pages.edit', ['id' => $page->id])
                ]);
            }

            $this->flashSuccess(__('Page created successfully.'));
            
            // Redirect based on action
            $action = $request->request->get('action', 'save');
            if ($action === 'save_and_continue') {
                return $this->redirectToRoute('admin.pages.edit', ['id' => $page->id]);
            } elseif ($action === 'save_and_new') {
                return $this->redirectToRoute('admin.pages.create');
            }
            
            return $this->redirectToRoute('admin.pages.index');

        } catch (\Exception $e) {
            $this->logError('page_store_error', $e, $request);
            
            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => __('An error occurred while creating the page.')
                ], 500);
            }
            
            $this->flashError(__('An error occurred while creating the page.'));
            return $this->back();
        }
    }

    /**
     * Get available templates
     */
    private function getAvailableTemplates()
    {
        $templatesPath = view_path('frontend/pages');
        $templates = ['default' => __('Default Template')];

        if (is_dir($templatesPath)) {
            $files = glob($templatesPath . '/*.twig');
            foreach ($files as $file) {
                $name = basename($file, '.twig');
                if ($name !== 'default') {
                    $templates[$name] = ucfirst(str_replace(['-', '_'], ' ', $name));
                }
            }
        }

        return $templates;
    }

    /**
     * Get parent page options
     */
    private function getParentPageOptions()
    {
        return Page::select('id', 'title', 'parent_id')
                  ->where('status', '!=', Page::STATUS_TRASH)
                  ->orderBy('title')
                  ->get()
                  ->map(function($page) {
                      return [
                          'id' => $page->id,
                          'title' => str_repeat('— ', $page->getLevel()) . $page->title
                      ];
                  });
    }

    /**
     * Validate CSRF token
     */
    private function validateCsrfToken(Request $request)
    {
        $token = $request->request->get('_token') ?: $request->headers->get('X-CSRF-TOKEN');
        
        if (!$token) {
            return false;
        }

        $sessionToken = session()->get('_token');
        return $sessionToken && hash_equals($sessionToken, $token);
    }

    /**
     * Show edit page form
     */
    public function edit(Request $request, $id)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('pages.edit')) {
                return $this->forbidden('You do not have permission to edit pages.');
            }

            // Find page
            $page = Page::with(['author', 'parent', 'revisions'])->find($id);
            if (!$page) {
                $this->flashError(__('Page not found.'));
                return $this->redirectToRoute('admin.pages.index');
            }

            // Get available data
            $templates = $this->getAvailableTemplates();
            $parentPages = $this->getParentPageOptions($page->id); // Exclude current page
            $customFields = $this->fieldService->getFieldsForType('page');

            $data = [
                'title' => __('Edit Page: :title', ['title' => $page->title]),
                'page' => $page,
                'templates' => $templates,
                'parent_pages' => $parentPages,
                'custom_fields' => $customFields,
                'statuses' => Page::getStatuses()
            ];

            return $this->view('admin/pages/edit.twig', $data);

        } catch (\Exception $e) {
            $this->logError('page_edit_form_error', $e, $request);
            $this->flashError(__('An error occurred while loading the edit form.'));
            return $this->redirectToRoute('admin.pages.index');
        }
    }

    /**
     * Update page
     */
    public function update(Request $request, $id)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('pages.edit')) {
                return $this->forbidden('You do not have permission to edit pages.');
            }

            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Invalid CSRF token'
                    ], 403);
                }

                $this->flashError(__('Security token mismatch. Please try again.'));
                return $this->back();
            }

            // Find page
            $page = Page::find($id);
            if (!$page) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Page not found'
                    ], 404);
                }

                $this->flashError(__('Page not found.'));
                return $this->redirectToRoute('admin.pages.index');
            }

            // Validate input
            $rules = [
                'title' => 'required|string|max:255',
                'slug' => 'nullable|string|max:255|unique:pages,slug,' . $page->id,
                'content' => 'required|string',
                'excerpt' => 'nullable|string|max:500',
                'status' => 'required|in:' . implode(',', array_keys(Page::getStatuses())),
                'template' => 'nullable|string|max:100',
                'meta_title' => 'nullable|string|max:255',
                'meta_description' => 'nullable|string|max:500',
                'meta_keywords' => 'nullable|string|max:255',
                'parent_id' => 'nullable|integer|exists:pages,id|not_in:' . $page->id,
                'published_at' => 'nullable|date',
                'scheduled_at' => 'nullable|date|after:now',
                'featured_image' => 'nullable|string|max:255'
            ];

            $data = $this->validationService->validate($request->request->all(), $rules);

            // Sanitize content
            $data['title'] = $this->sanitizationService->sanitizeInput($data['title']);
            $data['content'] = $this->sanitizationService->sanitizeRichText($data['content']);
            $data['excerpt'] = $this->sanitizationService->sanitizeInput($data['excerpt'] ?? '');

            // Generate slug if not provided
            if (empty($data['slug'])) {
                $data['slug'] = $page->generateSlug($data['title']);
            }

            // Handle scheduling
            if ($data['status'] === Page::STATUS_SCHEDULED && !empty($data['scheduled_at'])) {
                $data['published_at'] = null;
            } elseif ($data['status'] === Page::STATUS_PUBLISHED) {
                $data['published_at'] = $data['published_at'] ?? now();
                $data['scheduled_at'] = null;
            }

            // Handle custom fields
            $customFields = $request->request->get('custom_fields', []);
            if (!empty($customFields)) {
                $data['custom_fields'] = $this->fieldService->processFieldData($customFields, 'page');
            }

            // Update page
            $page->update($data);

            // Log activity
            $this->activityLogger->log('page_updated', [
                'user_id' => auth()->id(),
                'page_id' => $page->id,
                'page_title' => $page->title,
                'status' => $page->status
            ]);

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => true,
                    'message' => __('Page updated successfully.'),
                    'redirect' => route('admin.pages.edit', ['id' => $page->id])
                ]);
            }

            $this->flashSuccess(__('Page updated successfully.'));

            // Redirect based on action
            $action = $request->request->get('action', 'save');
            if ($action === 'save_and_continue') {
                return $this->redirectToRoute('admin.pages.edit', ['id' => $page->id]);
            } elseif ($action === 'save_and_new') {
                return $this->redirectToRoute('admin.pages.create');
            }

            return $this->redirectToRoute('admin.pages.index');

        } catch (\Exception $e) {
            $this->logError('page_update_error', $e, $request);

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => __('An error occurred while updating the page.')
                ], 500);
            }

            $this->flashError(__('An error occurred while updating the page.'));
            return $this->back();
        }
    }

    /**
     * Delete page
     */
    public function destroy(Request $request, $id)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('pages.delete')) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'You do not have permission to delete pages.'
                    ], 403);
                }

                return $this->forbidden('You do not have permission to delete pages.');
            }

            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Invalid CSRF token'
                    ], 403);
                }

                $this->flashError(__('Security token mismatch. Please try again.'));
                return $this->back();
            }

            // Find page
            $page = Page::find($id);
            if (!$page) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Page not found'
                    ], 404);
                }

                $this->flashError(__('Page not found.'));
                return $this->redirectToRoute('admin.pages.index');
            }

            // Check if page has children
            if ($page->children()->count() > 0) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Cannot delete page with child pages. Please delete or move child pages first.'
                    ], 400);
                }

                $this->flashError(__('Cannot delete page with child pages. Please delete or move child pages first.'));
                return $this->back();
            }

            $pageTitle = $page->title;

            // Move to trash instead of permanent delete
            $page->trash();

            // Log activity
            $this->activityLogger->log('page_deleted', [
                'user_id' => auth()->id(),
                'page_id' => $page->id,
                'page_title' => $pageTitle
            ]);

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => true,
                    'message' => __('Page moved to trash successfully.')
                ]);
            }

            $this->flashSuccess(__('Page moved to trash successfully.'));
            return $this->redirectToRoute('admin.pages.index');

        } catch (\Exception $e) {
            $this->logError('page_delete_error', $e, $request);

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => __('An error occurred while deleting the page.')
                ], 500);
            }

            $this->flashError(__('An error occurred while deleting the page.'));
            return $this->back();
        }
    }

    /**
     * Bulk actions
     */
    public function bulkAction(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('pages.edit')) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'You do not have permission to perform bulk actions.'
                ], 403);
            }

            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Invalid CSRF token'
                ], 403);
            }

            $action = $request->request->get('action');
            $pageIds = $request->request->get('page_ids', []);

            if (empty($pageIds) || !is_array($pageIds)) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'No pages selected'
                ], 400);
            }

            $pages = Page::whereIn('id', $pageIds)->get();
            $count = 0;

            foreach ($pages as $page) {
                switch ($action) {
                    case 'publish':
                        if (auth()->can('pages.publish')) {
                            $page->publish();
                            $count++;
                        }
                        break;

                    case 'unpublish':
                        $page->unpublish();
                        $count++;
                        break;

                    case 'trash':
                        if (auth()->can('pages.delete')) {
                            $page->trash();
                            $count++;
                        }
                        break;

                    case 'restore':
                        $page->restore();
                        $count++;
                        break;
                }
            }

            // Log activity
            $this->activityLogger->log('pages_bulk_action', [
                'user_id' => auth()->id(),
                'action' => $action,
                'page_count' => $count,
                'page_ids' => $pageIds
            ]);

            return new JsonResponse([
                'success' => true,
                'message' => __(':count pages processed successfully.', ['count' => $count])
            ]);

        } catch (\Exception $e) {
            $this->logError('pages_bulk_action_error', $e, $request);

            return new JsonResponse([
                'success' => false,
                'message' => __('An error occurred while processing pages.')
            ], 500);
        }
    }

    /**
     * Get parent page options (excluding specified page)
     */
    private function getParentPageOptions($excludeId = null)
    {
        $query = Page::select('id', 'title', 'parent_id')
                    ->where('status', '!=', Page::STATUS_TRASH);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->orderBy('title')
                    ->get()
                    ->map(function($page) {
                        return [
                            'id' => $page->id,
                            'title' => str_repeat('— ', $page->getLevel()) . $page->title
                        ];
                    });
    }

    /**
     * Log errors
     */
    private function logError($event, \Exception $e, Request $request)
    {
        $logData = [
            'event' => $event,
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'user_id' => auth()->id(),
            'ip_address' => $request->getClientIp(),
            'user_agent' => $request->headers->get('User-Agent'),
            'url' => $request->getUri()
        ];

        error_log(json_encode($logData), 3, storage_path('logs/pages.log'));

        if ($this->activityLogger) {
            $this->activityLogger->log($event, $logData);
        }
    }
}
