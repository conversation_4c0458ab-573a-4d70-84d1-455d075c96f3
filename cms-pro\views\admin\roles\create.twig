{% extends "layouts/admin.twig" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="role-form-container">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ title }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ url('/admin/roles') }}">{{ __('Roles') }}</a>
                    </li>
                    <li class="breadcrumb-item active">{{ __('Create') }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ url('/admin/roles') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>{{ __('Back to Roles') }}
            </a>
        </div>
    </div>

    <form method="POST" action="{{ url('/admin/roles') }}" class="role-form" id="role-form" novalidate>
        {{ csrf_field() | raw }}
        
        <div class="row">
            <!-- Main Content Column -->
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-shield-alt me-2"></i>{{ __('Role Information') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Name -->
                        <div class="mb-3">
                            <label for="name" class="form-label">
                                {{ __('Role Name') }} <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control {{ errors.name ? 'is-invalid' : '' }}" 
                                   id="name" 
                                   name="name" 
                                   value="{{ old('name', role.name) }}" 
                                   required
                                   maxlength="100"
                                   placeholder="{{ __('Enter role name...') }}">
                            {% if errors.name %}
                                <div class="invalid-feedback">{{ errors.name }}</div>
                            {% endif %}
                        </div>

                        <!-- Slug -->
                        <div class="mb-3">
                            <label for="slug" class="form-label">{{ __('Role Slug') }}</label>
                            <input type="text" 
                                   class="form-control {{ errors.slug ? 'is-invalid' : '' }}" 
                                   id="slug" 
                                   name="slug" 
                                   value="{{ old('slug', role.slug) }}"
                                   maxlength="100"
                                   placeholder="{{ __('auto-generated') }}">
                            {% if errors.slug %}
                                <div class="invalid-feedback">{{ errors.slug }}</div>
                            {% endif %}
                            <div class="form-text">
                                {{ __('Leave empty to auto-generate from name. Used for programmatic access.') }}
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label for="description" class="form-label">{{ __('Description') }}</label>
                            <textarea class="form-control {{ errors.description ? 'is-invalid' : '' }}" 
                                      id="description" 
                                      name="description" 
                                      rows="3"
                                      maxlength="500"
                                      placeholder="{{ __('Brief description of this role...') }}">{{ old('description', role.description) }}</textarea>
                            {% if errors.description %}
                                <div class="invalid-feedback">{{ errors.description }}</div>
                            {% endif %}
                            <div class="form-text">
                                {{ __('Help users understand what this role is for') }}
                            </div>
                        </div>

                        <!-- Level -->
                        <div class="mb-3">
                            <label for="level" class="form-label">
                                {{ __('Role Level') }} <span class="text-danger">*</span>
                            </label>
                            <input type="number" 
                                   class="form-control {{ errors.level ? 'is-invalid' : '' }}" 
                                   id="level" 
                                   name="level" 
                                   value="{{ old('level', role.level ?? 50) }}" 
                                   required
                                   min="1"
                                   max="99"
                                   placeholder="50">
                            {% if errors.level %}
                                <div class="invalid-feedback">{{ errors.level }}</div>
                            {% endif %}
                            <div class="form-text">
                                {{ __('Higher levels have more authority. System roles: Super Admin (100), Admin (80), Editor (60), Author (40), Subscriber (20)') }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Permissions -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-success">
                            <i class="fas fa-key me-2"></i>{{ __('Permissions') }}
                        </h6>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-primary" id="select-all-permissions">
                                {{ __('Select All') }}
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="deselect-all-permissions">
                                {{ __('Deselect All') }}
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="permissions-container">
                            {% for category, categoryPermissions in permissions %}
                            <div class="permission-category mb-4">
                                <div class="category-header d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="text-primary mb-0">
                                        <i class="fas fa-folder me-2"></i>{{ category|title }}
                                    </h6>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary btn-sm select-category" 
                                                data-category="{{ category }}">
                                            {{ __('Select All') }}
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm deselect-category" 
                                                data-category="{{ category }}">
                                            {{ __('Deselect All') }}
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    {% for permission in categoryPermissions %}
                                    <div class="col-md-6 mb-2">
                                        <div class="form-check permission-item">
                                            <input class="form-check-input permission-checkbox" 
                                                   type="checkbox" 
                                                   id="permission_{{ permission.id }}" 
                                                   name="permissions[]" 
                                                   value="{{ permission.id }}"
                                                   data-category="{{ category }}"
                                                   {{ old('permissions') and permission.id in old('permissions') ? 'checked' : '' }}>
                                            <label class="form-check-label" for="permission_{{ permission.id }}">
                                                <strong>{{ permission.name }}</strong>
                                                {% if permission.description and permission.description != permission.name %}
                                                <br><small class="text-muted">{{ permission.description }}</small>
                                                {% endif %}
                                                <br><code class="small">{{ permission.slug }}</code>
                                            </label>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar Column -->
            <div class="col-lg-4">
                <!-- Role Settings -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-warning">
                            <i class="fas fa-cog me-2"></i>{{ __('Role Settings') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Status -->
                        <div class="mb-3">
                            <label for="status" class="form-label">{{ __('Status') }}</label>
                            <select class="form-select {{ errors.status ? 'is-invalid' : '' }}" 
                                    id="status" 
                                    name="status" 
                                    required>
                                {% for key, label in statuses %}
                                <option value="{{ key }}" {{ old('status', 'active') == key ? 'selected' : '' }}>
                                    {{ label }}
                                </option>
                                {% endfor %}
                            </select>
                            {% if errors.status %}
                                <div class="invalid-feedback">{{ errors.status }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Permission Summary -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-info">
                            <i class="fas fa-chart-pie me-2"></i>{{ __('Permission Summary') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="permission-summary">
                            <div class="text-center text-muted">
                                <i class="fas fa-info-circle fa-2x mb-2"></i>
                                <p class="mb-0">{{ __('Select permissions to see summary') }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" name="action" value="save" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ __('Create Role') }}
                            </button>
                            
                            <div class="btn-group">
                                <button type="submit" name="action" value="save_and_continue" class="btn btn-outline-primary">
                                    <i class="fas fa-save me-2"></i>{{ __('Save & Continue') }}
                                </button>
                                <button type="submit" name="action" value="save_and_new" class="btn btn-outline-primary">
                                    <i class="fas fa-plus me-2"></i>{{ __('Save & New') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
/**
 * Role Form Management
 * Enhanced with permission management and validation
 */
class RoleForm {
    constructor() {
        this.form = document.getElementById('role-form');
        this.nameField = document.getElementById('name');
        this.slugField = document.getElementById('slug');
        this.permissionCheckboxes = document.querySelectorAll('.permission-checkbox');

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupSlugGeneration();
        this.setupPermissionManagement();
        this.setupFormValidation();
        this.updatePermissionSummary();
    }

    setupEventListeners() {
        // Form submission
        this.form.addEventListener('submit', (e) => {
            this.handleFormSubmit(e);
        });

        // Real-time validation
        this.form.addEventListener('input', (e) => {
            this.validateField(e.target);
        });

        this.form.addEventListener('change', (e) => {
            this.validateField(e.target);

            // Update summary when permissions change
            if (e.target.matches('.permission-checkbox')) {
                this.updatePermissionSummary();
            }
        });
    }

    setupSlugGeneration() {
        // Auto-generate slug when name changes
        this.nameField.addEventListener('input', () => {
            if (!this.slugField.value || this.slugField.dataset.autoGenerated === 'true') {
                this.generateSlug();
                this.slugField.dataset.autoGenerated = 'true';
            }
        });

        // Mark slug as manually edited
        this.slugField.addEventListener('input', () => {
            this.slugField.dataset.autoGenerated = 'false';
        });
    }

    setupPermissionManagement() {
        // Select all permissions
        document.getElementById('select-all-permissions').addEventListener('click', () => {
            this.permissionCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
            this.updatePermissionSummary();
        });

        // Deselect all permissions
        document.getElementById('deselect-all-permissions').addEventListener('click', () => {
            this.permissionCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            this.updatePermissionSummary();
        });

        // Category select/deselect buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.select-category')) {
                const category = e.target.dataset.category;
                this.selectCategoryPermissions(category, true);
            } else if (e.target.matches('.deselect-category')) {
                const category = e.target.dataset.category;
                this.selectCategoryPermissions(category, false);
            }
        });
    }

    setupFormValidation() {
        // Add validation classes to required fields
        const requiredFields = this.form.querySelectorAll('[required]');
        requiredFields.forEach(field => {
            field.addEventListener('blur', () => {
                this.validateField(field);
            });
        });
    }

    generateSlug() {
        const name = this.nameField.value.trim();
        if (!name) return;

        // Simple slug generation
        const slug = name
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
            .replace(/\s+/g, '_') // Replace spaces with underscores
            .replace(/_+/g, '_') // Remove multiple underscores
            .replace(/^_|_$/g, ''); // Remove leading/trailing underscores

        this.slugField.value = slug;
        this.slugField.dataset.autoGenerated = 'true';
    }

    selectCategoryPermissions(category, select) {
        const categoryCheckboxes = document.querySelectorAll(`[data-category="${category}"]`);
        categoryCheckboxes.forEach(checkbox => {
            checkbox.checked = select;
        });
        this.updatePermissionSummary();
    }

    updatePermissionSummary() {
        const checkedPermissions = document.querySelectorAll('.permission-checkbox:checked');
        const summaryContainer = document.getElementById('permission-summary');

        if (checkedPermissions.length === 0) {
            summaryContainer.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                    <p class="mb-0">{{ __('Select permissions to see summary') }}</p>
                </div>
            `;
            return;
        }

        // Group permissions by category
        const permissionsByCategory = {};
        checkedPermissions.forEach(checkbox => {
            const category = checkbox.dataset.category;
            if (!permissionsByCategory[category]) {
                permissionsByCategory[category] = 0;
            }
            permissionsByCategory[category]++;
        });

        // Generate summary HTML
        let summaryHtml = `
            <div class="permission-stats mb-3">
                <div class="stat-item">
                    <span class="stat-number">${checkedPermissions.length}</span>
                    <span class="stat-label">{{ __('Total Permissions') }}</span>
                </div>
            </div>
            <div class="permission-breakdown">
        `;

        Object.entries(permissionsByCategory).forEach(([category, count]) => {
            const totalInCategory = document.querySelectorAll(`[data-category="${category}"]`).length;
            const percentage = Math.round((count / totalInCategory) * 100);

            summaryHtml += `
                <div class="category-stat mb-2">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="category-name">${category.charAt(0).toUpperCase() + category.slice(1)}</span>
                        <span class="category-count">${count}/${totalInCategory}</span>
                    </div>
                    <div class="progress" style="height: 4px;">
                        <div class="progress-bar" style="width: ${percentage}%"></div>
                    </div>
                </div>
            `;
        });

        summaryHtml += '</div>';
        summaryContainer.innerHTML = summaryHtml;
    }

    validateField(field) {
        if (!field.checkValidity) return true;

        const isValid = field.checkValidity();

        if (isValid) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        } else if (field.value.length > 0 || field.hasAttribute('required')) {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
        }

        return isValid;
    }

    async handleFormSubmit(e) {
        e.preventDefault();

        // Validate form
        if (!this.form.checkValidity()) {
            e.stopPropagation();
            this.form.classList.add('was-validated');

            // Focus first invalid field
            const firstInvalid = this.form.querySelector(':invalid');
            if (firstInvalid) {
                firstInvalid.focus();
                firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }

            return;
        }

        // Show loading state
        const submitButton = e.submitter;
        const originalText = submitButton.innerHTML;
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';

        try {
            const formData = new FormData(this.form);

            const response = await fetch(this.form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');

                if (result.redirect) {
                    setTimeout(() => {
                        window.location.href = result.redirect;
                    }, 1000);
                }
            } else {
                this.showNotification(result.message || 'Save failed', 'error');
            }

        } catch (error) {
            console.error('Form submission error:', error);
            this.showNotification('An error occurred while saving', 'error');
        } finally {
            // Restore button state
            submitButton.disabled = false;
            submitButton.innerHTML = originalText;
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
        notification.innerHTML = `
            ${this.escapeHtml(message)}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    new RoleForm();
});
</script>

<style>
/* Role Form Styles */
.role-form-container .card-header h6 {
    font-size: 0.875rem;
    font-weight: 600;
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-text {
    font-size: 0.8125rem;
}

.permission-category {
    border-left: 3px solid #007bff;
    padding-left: 1rem;
    margin-left: 0.5rem;
}

.category-header {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.5rem;
}

.permission-item {
    padding: 0.5rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.permission-item:hover {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

.permission-item .form-check-input:checked + .form-check-label {
    color: #0d6efd;
}

.permission-stats {
    text-align: center;
    padding: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.5rem;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

.category-stat {
    font-size: 0.875rem;
}

.category-name {
    font-weight: 500;
}

.category-count {
    color: #6c757d;
    font-size: 0.8125rem;
}

.progress {
    background-color: #e9ecef;
}

.progress-bar {
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
}

/* Form validation styles */
.was-validated .form-control:valid,
.form-control.is-valid {
    border-color: #28a745;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.38 1.38 3.72-3.72.94.94-4.66 4.66z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:invalid,
.form-control.is-invalid {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 7.4 5.8 6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .role-form-container .col-lg-8,
    .role-form-container .col-lg-4 {
        margin-bottom: 1rem;
    }

    .btn-group {
        width: 100%;
    }

    .btn-group .btn {
        flex: 1;
    }

    .permission-category {
        margin-left: 0;
        padding-left: 0.5rem;
    }

    .category-header .btn-group {
        flex-direction: column;
        width: auto;
    }
}

/* Dark mode support */
[data-bs-theme="dark"] .card-header {
    background-color: #2d3748 !important;
    border-color: #4a5568;
}

[data-bs-theme="dark"] .form-control {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
}

[data-bs-theme="dark"] .form-control:focus {
    background-color: #2d3748;
    border-color: #0d6efd;
    color: #e2e8f0;
}

[data-bs-theme="dark"] .permission-item:hover {
    background-color: #374151;
    border-color: #4b5563;
}

[data-bs-theme="dark"] .permission-category {
    border-left-color: #0d6efd;
}

[data-bs-theme="dark"] .category-header {
    border-bottom-color: #4a5568;
}
</style>
{% endblock %}
