-- Create validation_rule_templates table
CREATE TABLE IF NOT EXISTS `validation_rule_templates` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `description` text DEFAULT NULL,
    `category` varchar(50) DEFAULT NULL,
    `rules` json NOT NULL,
    `is_public` tinyint(1) NOT NULL DEFAULT 0,
    `created_by` int(11) NOT NULL,
    `usage_count` int(11) NOT NULL DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `validation_rule_templates_created_by_foreign` (`created_by`),
    KEY `validation_rule_templates_category_index` (`category`),
    <PERSON>EY `validation_rule_templates_is_public_index` (`is_public`),
    CONSTRAINT `validation_rule_templates_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create security_logs table
CREATE TABLE IF NOT EXISTS `security_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `event_type` varchar(100) NOT NULL,
    `field_type` varchar(50) DEFAULT NULL,
    `field_id` int(11) DEFAULT NULL,
    `threats` json DEFAULT NULL,
    `severity` enum('low','medium','high','critical') NOT NULL DEFAULT 'medium',
    `user_id` int(11) DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text DEFAULT NULL,
    `context` json DEFAULT NULL,
    `resolved` tinyint(1) NOT NULL DEFAULT 0,
    `resolved_by` int(11) DEFAULT NULL,
    `resolved_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `security_logs_event_type_index` (`event_type`),
    KEY `security_logs_field_id_index` (`field_id`),
    KEY `security_logs_user_id_index` (`user_id`),
    KEY `security_logs_severity_index` (`severity`),
    KEY `security_logs_resolved_index` (`resolved`),
    KEY `security_logs_created_at_index` (`created_at`),
    CONSTRAINT `security_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
    CONSTRAINT `security_logs_field_id_foreign` FOREIGN KEY (`field_id`) REFERENCES `fields` (`id`) ON DELETE SET NULL,
    CONSTRAINT `security_logs_resolved_by_foreign` FOREIGN KEY (`resolved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create field_validation_errors table
CREATE TABLE IF NOT EXISTS `field_validation_errors` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `field_id` int(11) NOT NULL,
    `entity_type` varchar(50) NOT NULL,
    `entity_id` int(11) NOT NULL,
    `error_code` varchar(100) NOT NULL,
    `error_message` text NOT NULL,
    `field_value` text DEFAULT NULL,
    `validation_rules` json DEFAULT NULL,
    `user_id` int(11) DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `resolved` tinyint(1) NOT NULL DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `field_validation_errors_field_id_index` (`field_id`),
    KEY `field_validation_errors_entity_index` (`entity_type`, `entity_id`),
    KEY `field_validation_errors_error_code_index` (`error_code`),
    KEY `field_validation_errors_user_id_index` (`user_id`),
    KEY `field_validation_errors_resolved_index` (`resolved`),
    KEY `field_validation_errors_created_at_index` (`created_at`),
    CONSTRAINT `field_validation_errors_field_id_foreign` FOREIGN KEY (`field_id`) REFERENCES `fields` (`id`) ON DELETE CASCADE,
    CONSTRAINT `field_validation_errors_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create field_sanitization_logs table
CREATE TABLE IF NOT EXISTS `field_sanitization_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `field_id` int(11) NOT NULL,
    `field_type` varchar(50) NOT NULL,
    `original_value` text DEFAULT NULL,
    `sanitized_value` text DEFAULT NULL,
    `sanitization_rules` json DEFAULT NULL,
    `changes_made` json DEFAULT NULL,
    `user_id` int(11) DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `field_sanitization_logs_field_id_index` (`field_id`),
    KEY `field_sanitization_logs_field_type_index` (`field_type`),
    KEY `field_sanitization_logs_user_id_index` (`user_id`),
    KEY `field_sanitization_logs_created_at_index` (`created_at`),
    CONSTRAINT `field_sanitization_logs_field_id_foreign` FOREIGN KEY (`field_id`) REFERENCES `fields` (`id`) ON DELETE CASCADE,
    CONSTRAINT `field_sanitization_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create field_access_logs table
CREATE TABLE IF NOT EXISTS `field_access_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `field_id` int(11) NOT NULL,
    `entity_type` varchar(50) NOT NULL,
    `entity_id` int(11) NOT NULL,
    `action` enum('read','write','update','delete') NOT NULL,
    `user_id` int(11) DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text DEFAULT NULL,
    `success` tinyint(1) NOT NULL DEFAULT 1,
    `error_message` text DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `field_access_logs_field_id_index` (`field_id`),
    KEY `field_access_logs_entity_index` (`entity_type`, `entity_id`),
    KEY `field_access_logs_action_index` (`action`),
    KEY `field_access_logs_user_id_index` (`user_id`),
    KEY `field_access_logs_success_index` (`success`),
    KEY `field_access_logs_created_at_index` (`created_at`),
    CONSTRAINT `field_access_logs_field_id_foreign` FOREIGN KEY (`field_id`) REFERENCES `fields` (`id`) ON DELETE CASCADE,
    CONSTRAINT `field_access_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default validation rule templates
INSERT INTO `validation_rule_templates` (`name`, `description`, `category`, `rules`, `is_public`, `created_by`) VALUES
('Basic Text Validation', 'Standard text field validation rules', 'text', '{
    "min_length": 1,
    "max_length": 255,
    "trim": true,
    "pattern": null,
    "unique": false
}', 1, 1),

('Email Validation', 'Email field validation with domain checks', 'email', '{
    "unique": true,
    "domain_whitelist": null,
    "domain_blacklist": ["tempmail.com", "10minutemail.com", "guerrillamail.com"]
}', 1, 1),

('Strong Password', 'Strong password validation rules', 'password', '{
    "min_length": 8,
    "max_length": 128,
    "pattern": "/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/",
    "pattern_message": "Password must contain at least one uppercase letter, one lowercase letter, one number and one special character"
}', 1, 1),

('Phone Number', 'International phone number validation', 'phone', '{
    "pattern": "/^[\\+]?[1-9]\\d{1,14}$/",
    "pattern_message": "Please enter a valid phone number",
    "international": true,
    "unique": false
}', 1, 1),

('URL Validation', 'URL validation with protocol restrictions', 'url', '{
    "protocols": ["http", "https"],
    "domain_blacklist": ["bit.ly", "tinyurl.com"],
    "max_length": 2000
}', 1, 1),

('Number Range', 'Number validation with min/max constraints', 'number', '{
    "min": 0,
    "max": 999999,
    "step": 1,
    "decimal_places": 0,
    "unique": false
}', 1, 1),

('Date Range', 'Date validation with range constraints', 'date', '{
    "min_date": "1900-01-01",
    "max_date": "2100-12-31",
    "exclude_weekends": false,
    "exclude_holidays": false,
    "future_only": false,
    "past_only": false
}', 1, 1),

('Image Upload', 'Image file validation rules', 'image', '{
    "max_file_size": 5,
    "allowed_types": ["jpg", "jpeg", "png", "gif", "webp"],
    "min_width": 100,
    "max_width": 4000,
    "min_height": 100,
    "max_height": 4000,
    "aspect_ratio": null
}', 1, 1),

('Document Upload', 'Document file validation rules', 'file', '{
    "max_file_size": 10,
    "allowed_types": ["pdf", "doc", "docx", "xls", "xlsx", "txt"],
    "scan_viruses": true
}', 1, 1),

('Rich Text Content', 'WYSIWYG editor validation rules', 'wysiwyg', '{
    "min_length": 10,
    "max_length": 50000,
    "max_words": 5000,
    "allowed_tags": ["p", "br", "strong", "em", "u", "h1", "h2", "h3", "h4", "h5", "h6", "ul", "ol", "li", "a", "img", "blockquote"],
    "strip_scripts": true,
    "strip_styles": false
}', 1, 1),

('Gallery Validation', 'Image gallery validation rules', 'gallery', '{
    "min_items": 1,
    "max_items": 20,
    "max_file_size": 5,
    "allowed_types": ["jpg", "jpeg", "png", "gif", "webp"]
}', 1, 1),

('Repeater Content', 'Repeater field validation rules', 'repeater', '{
    "min_items": 0,
    "max_items": 10,
    "validate_sub_fields": true
}', 1, 1),

('Flexible Content', 'Flexible content validation rules', 'flexible', '{
    "min_items": 0,
    "max_items": 20,
    "required_layouts": [],
    "validate_sub_fields": true
}', 1, 1),

('Select Options', 'Select field validation rules', 'select', '{
    "validate_options": true
}', 1, 1),

('Multi-Select', 'Multi-select field validation rules', 'multiselect', '{
    "min_selections": 0,
    "max_selections": 10,
    "validate_options": true
}', 1, 1);

-- Create indexes for better performance
CREATE INDEX `validation_rule_templates_name_index` ON `validation_rule_templates` (`name`);
CREATE INDEX `security_logs_event_severity_index` ON `security_logs` (`event_type`, `severity`);
CREATE INDEX `field_validation_errors_field_entity_index` ON `field_validation_errors` (`field_id`, `entity_type`, `entity_id`);
CREATE INDEX `field_sanitization_logs_field_type_date_index` ON `field_sanitization_logs` (`field_type`, `created_at`);
CREATE INDEX `field_access_logs_user_action_index` ON `field_access_logs` (`user_id`, `action`);

-- Add validation rules column to fields table if not exists
ALTER TABLE `fields` 
ADD COLUMN IF NOT EXISTS `validation_rules` json DEFAULT NULL AFTER `settings`,
ADD COLUMN IF NOT EXISTS `sanitization_rules` json DEFAULT NULL AFTER `validation_rules`,
ADD COLUMN IF NOT EXISTS `security_level` enum('low','medium','high','critical') NOT NULL DEFAULT 'medium' AFTER `sanitization_rules`;

-- Add indexes for new columns
CREATE INDEX IF NOT EXISTS `fields_security_level_index` ON `fields` (`security_level`);

-- Update existing fields with default validation rules based on field type
UPDATE `fields` f 
INNER JOIN `field_types` ft ON f.field_type_id = ft.id 
SET f.validation_rules = CASE 
    WHEN ft.slug = 'text' THEN '{"min_length": 0, "max_length": 255, "trim": true}'
    WHEN ft.slug = 'textarea' THEN '{"min_length": 0, "max_length": 5000, "trim": true}'
    WHEN ft.slug = 'email' THEN '{"unique": false}'
    WHEN ft.slug = 'url' THEN '{"protocols": ["http", "https"]}'
    WHEN ft.slug = 'number' THEN '{"min": null, "max": null, "step": 1}'
    WHEN ft.slug = 'image' THEN '{"max_file_size": 5, "allowed_types": ["jpg", "jpeg", "png", "gif"]}'
    WHEN ft.slug = 'file' THEN '{"max_file_size": 10, "allowed_types": ["pdf", "doc", "docx"]}'
    ELSE '{}'
END
WHERE f.validation_rules IS NULL;
