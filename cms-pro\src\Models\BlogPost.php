<?php

namespace CmsPro\Models;

use CmsPro\Core\Model;
use CmsPro\Services\SeoService;

/**
 * Blog Post Model
 * 
 * @package CmsPro\Models
 */
class BlogPost extends Model
{
    protected $table = 'blog_posts';
    
    protected $fillable = [
        'title',
        'slug',
        'content',
        'excerpt',
        'status',
        'author_id',
        'category_id',
        'featured_image',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'published_at',
        'scheduled_at',
        'views',
        'likes',
        'comments_count',
        'reading_time',
        'settings'
    ];

    protected $casts = [
        'published_at' => 'datetime',
        'scheduled_at' => 'datetime',
        'settings' => 'json',
        'views' => 'integer',
        'likes' => 'integer',
        'comments_count' => 'integer',
        'reading_time' => 'integer',
        'author_id' => 'integer',
        'category_id' => 'integer'
    ];

    protected $dates = [
        'published_at',
        'scheduled_at',
        'created_at',
        'updated_at'
    ];

    /**
     * Get filtered blog posts
     */
    public static function getFiltered($filters = [])
    {
        $db = app()->getDatabase();
        
        $whereConditions = [];
        $params = [];
        
        // Status filter
        if (!empty($filters['status']) && $filters['status'] !== 'all') {
            $whereConditions[] = "bp.status = ?";
            $params[] = $filters['status'];
        }
        
        // Category filter
        if (!empty($filters['category'])) {
            $whereConditions[] = "bp.category_id = ?";
            $params[] = $filters['category'];
        }
        
        // Author filter
        if (!empty($filters['author'])) {
            $whereConditions[] = "bp.author_id = ?";
            $params[] = $filters['author'];
        }
        
        // Search filter
        if (!empty($filters['search'])) {
            $whereConditions[] = "(bp.title LIKE ? OR bp.content LIKE ? OR bp.excerpt LIKE ?)";
            $searchTerm = "%{$filters['search']}%";
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
        }
        
        // Date filters
        if (!empty($filters['date_from'])) {
            $whereConditions[] = "bp.created_at >= ?";
            $params[] = $filters['date_from'] . ' 00:00:00';
        }
        
        if (!empty($filters['date_to'])) {
            $whereConditions[] = "bp.created_at <= ?";
            $params[] = $filters['date_to'] . ' 23:59:59';
        }
        
        $whereClause = $whereConditions ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // Get total count
        $totalQuery = "
            SELECT COUNT(*) as total 
            FROM blog_posts bp 
            {$whereClause}
        ";
        $totalResult = $db->selectOne($totalQuery, $params);
        $total = $totalResult['total'];
        
        // Calculate pagination
        $page = $filters['page'] ?? 1;
        $perPage = $filters['per_page'] ?? 20;
        $offset = ($page - 1) * $perPage;
        $totalPages = ceil($total / $perPage);
        
        // Get posts
        $postsQuery = "
            SELECT bp.*, 
                   u.first_name, u.last_name, u.email,
                   c.name as category_name, c.slug as category_slug
            FROM blog_posts bp
            LEFT JOIN users u ON bp.author_id = u.id
            LEFT JOIN categories c ON bp.category_id = c.id
            {$whereClause}
            ORDER BY bp.created_at DESC
            LIMIT {$perPage} OFFSET {$offset}
        ";
        
        $posts = $db->select($postsQuery, $params);
        
        // Convert to model instances
        $postInstances = [];
        foreach ($posts as $post) {
            $instance = new static();
            $instance->fill($post);
            $instance->setAttribute('author_name', trim(($post['first_name'] ?? '') . ' ' . ($post['last_name'] ?? '')));
            $instance->setAttribute('category_name', $post['category_name']);
            $instance->setAttribute('category_slug', $post['category_slug']);
            $postInstances[] = $instance;
        }
        
        return [
            'posts' => $postInstances,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'pages' => $totalPages
            ]
        ];
    }

    /**
     * Get authors for filter
     */
    public static function getAuthors()
    {
        $db = app()->getDatabase();
        return $db->select(
            "SELECT DISTINCT u.id, u.first_name, u.last_name, u.email
             FROM users u
             INNER JOIN blog_posts bp ON u.id = bp.author_id
             ORDER BY u.first_name, u.last_name"
        );
    }

    /**
     * Create new blog post
     */
    public static function create($data)
    {
        $db = app()->getDatabase();
        
        // Calculate reading time
        $readingTime = static::calculateReadingTime($data['content']);
        $data['reading_time'] = $readingTime;
        
        // Set timestamps
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        // Handle published_at
        if ($data['status'] === 'published' && empty($data['published_at'])) {
            $data['published_at'] = date('Y-m-d H:i:s');
        }
        
        $id = $db->insert('blog_posts', $data);
        
        if ($id) {
            $instance = new static();
            $instance->setAttribute('id', $id);
            $instance->fill($data);
            return $instance;
        }
        
        return false;
    }

    /**
     * Find blog post by ID
     */
    public static function find($id)
    {
        $db = app()->getDatabase();
        $post = $db->selectOne(
            "SELECT bp.*, 
                    u.first_name, u.last_name, u.email,
                    c.name as category_name, c.slug as category_slug
             FROM blog_posts bp
             LEFT JOIN users u ON bp.author_id = u.id
             LEFT JOIN categories c ON bp.category_id = c.id
             WHERE bp.id = ?",
            [$id]
        );
        
        if (!$post) {
            return null;
        }
        
        $instance = new static();
        $instance->fill($post);
        $instance->setAttribute('author_name', trim(($post['first_name'] ?? '') . ' ' . ($post['last_name'] ?? '')));
        $instance->setAttribute('category_name', $post['category_name']);
        $instance->setAttribute('category_slug', $post['category_slug']);
        
        return $instance;
    }

    /**
     * Update blog post
     */
    public function update($data)
    {
        $db = app()->getDatabase();
        
        // Calculate reading time if content changed
        if (isset($data['content'])) {
            $data['reading_time'] = static::calculateReadingTime($data['content']);
        }
        
        // Set updated timestamp
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        // Handle published_at
        if (isset($data['status']) && $data['status'] === 'published' && 
            $this->getAttribute('status') !== 'published' && empty($data['published_at'])) {
            $data['published_at'] = date('Y-m-d H:i:s');
        }
        
        $updated = $db->update(
            "UPDATE blog_posts SET " . implode(', ', array_map(fn($key) => "{$key} = ?", array_keys($data))) . " WHERE id = ?",
            array_merge(array_values($data), [$this->getId()])
        );
        
        if ($updated) {
            foreach ($data as $key => $value) {
                $this->setAttribute($key, $value);
            }
            return true;
        }
        
        return false;
    }

    /**
     * Delete blog post
     */
    public function delete()
    {
        $db = app()->getDatabase();
        
        // Delete related data
        $db->delete("DELETE FROM blog_post_tags WHERE post_id = ?", [$this->getId()]);
        $db->delete("DELETE FROM comments WHERE post_id = ? AND post_type = 'blog_post'", [$this->getId()]);
        
        // Delete the post
        return $db->delete("DELETE FROM blog_posts WHERE id = ?", [$this->getId()]);
    }

    /**
     * Sync tags for this post
     */
    public function syncTags($tagIds)
    {
        $db = app()->getDatabase();
        
        // Remove existing tags
        $db->delete("DELETE FROM blog_post_tags WHERE post_id = ?", [$this->getId()]);
        
        // Add new tags
        foreach ($tagIds as $tagId) {
            $db->insert('blog_post_tags', [
                'post_id' => $this->getId(),
                'tag_id' => $tagId,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }
    }

    /**
     * Get tags for this post
     */
    public function getTags()
    {
        $db = app()->getDatabase();
        return $db->select(
            "SELECT t.* FROM tags t
             INNER JOIN blog_post_tags bpt ON t.id = bpt.tag_id
             WHERE bpt.post_id = ?
             ORDER BY t.name",
            [$this->getId()]
        );
    }

    /**
     * Calculate reading time in minutes
     */
    private static function calculateReadingTime($content)
    {
        $wordCount = str_word_count(strip_tags($content));
        $wordsPerMinute = 200; // Average reading speed
        return max(1, ceil($wordCount / $wordsPerMinute));
    }

    /**
     * Increment views
     */
    public function incrementViews()
    {
        $db = app()->getDatabase();
        $db->update(
            "UPDATE blog_posts SET views = views + 1 WHERE id = ?",
            [$this->getId()]
        );
        
        $this->setAttribute('views', $this->getAttribute('views', 0) + 1);
    }

    // Getters
    public function getId() { return $this->getAttribute('id'); }
    public function getTitle() { return $this->getAttribute('title'); }
    public function getSlug() { return $this->getAttribute('slug'); }
    public function getContent() { return $this->getAttribute('content'); }
    public function getExcerpt() { return $this->getAttribute('excerpt'); }
    public function getStatus() { return $this->getAttribute('status'); }
    public function getAuthorId() { return $this->getAttribute('author_id'); }
    public function getCategoryId() { return $this->getAttribute('category_id'); }
    public function getFeaturedImage() { return $this->getAttribute('featured_image'); }
    public function getMetaTitle() { return $this->getAttribute('meta_title'); }
    public function getMetaDescription() { return $this->getAttribute('meta_description'); }
    public function getMetaKeywords() { return $this->getAttribute('meta_keywords'); }
    public function getPublishedAt() { return $this->getAttribute('published_at'); }
    public function getViews() { return $this->getAttribute('views', 0); }
    public function getLikes() { return $this->getAttribute('likes', 0); }
    public function getCommentsCount() { return $this->getAttribute('comments_count', 0); }
    public function getReadingTime() { return $this->getAttribute('reading_time', 1); }
    public function getAuthorName() { return $this->getAttribute('author_name'); }
    public function getCategoryName() { return $this->getAttribute('category_name'); }
    public function getCategorySlug() { return $this->getAttribute('category_slug'); }
    public function getCreatedAt() { return $this->getAttribute('created_at'); }
    public function getUpdatedAt() { return $this->getAttribute('updated_at'); }
}
