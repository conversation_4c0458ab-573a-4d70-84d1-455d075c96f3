/**
 * CMS Pro - Main JavaScript File
 */

// Global CMS Pro object
window.CmsPro = {
    config: {
        baseUrl: window.location.origin,
        apiUrl: window.location.origin + '/api/v1',
        csrfToken: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        locale: document.documentElement.lang || 'en'
    },
    
    // Utility functions
    utils: {
        // Show loading spinner
        showLoading: function(element) {
            if (element) {
                element.innerHTML = '<span class="spinner me-2"></span>Loading...';
                element.disabled = true;
            }
        },
        
        // Hide loading spinner
        hideLoading: function(element, originalText) {
            if (element) {
                element.innerHTML = originalText;
                element.disabled = false;
            }
        },
        
        // Show alert
        showAlert: function(message, type = 'info') {
            const alertContainer = document.getElementById('alert-container') || document.body;
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            alertContainer.insertBefore(alertDiv, alertContainer.firstChild);
            
            // Auto dismiss after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        },
        
        // Format date
        formatDate: function(date, format = 'Y-m-d H:i:s') {
            const d = new Date(date);
            const year = d.getFullYear();
            const month = String(d.getMonth() + 1).padStart(2, '0');
            const day = String(d.getDate()).padStart(2, '0');
            const hours = String(d.getHours()).padStart(2, '0');
            const minutes = String(d.getMinutes()).padStart(2, '0');
            const seconds = String(d.getSeconds()).padStart(2, '0');
            
            return format
                .replace('Y', year)
                .replace('m', month)
                .replace('d', day)
                .replace('H', hours)
                .replace('i', minutes)
                .replace('s', seconds);
        },
        
        // Debounce function
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        // Throttle function
        throttle: function(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        }
    },
    
    // AJAX helper
    ajax: {
        request: function(url, options = {}) {
            const defaults = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': CmsPro.config.csrfToken
                }
            };
            
            const config = Object.assign(defaults, options);
            
            return fetch(url, config)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .catch(error => {
                    console.error('AJAX Error:', error);
                    CmsPro.utils.showAlert('An error occurred. Please try again.', 'danger');
                    throw error;
                });
        },
        
        get: function(url, params = {}) {
            const urlParams = new URLSearchParams(params);
            const fullUrl = url + (urlParams.toString() ? '?' + urlParams.toString() : '');
            return this.request(fullUrl);
        },
        
        post: function(url, data = {}) {
            return this.request(url, {
                method: 'POST',
                body: JSON.stringify(data)
            });
        },
        
        put: function(url, data = {}) {
            return this.request(url, {
                method: 'PUT',
                body: JSON.stringify(data)
            });
        },
        
        delete: function(url) {
            return this.request(url, {
                method: 'DELETE'
            });
        }
    },
    
    // Form helpers
    forms: {
        // Serialize form data
        serialize: function(form) {
            const formData = new FormData(form);
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            return data;
        },
        
        // Validate form
        validate: function(form) {
            const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
            let isValid = true;
            
            inputs.forEach(input => {
                if (!input.value.trim()) {
                    input.classList.add('is-invalid');
                    isValid = false;
                } else {
                    input.classList.remove('is-invalid');
                }
            });
            
            return isValid;
        },
        
        // Reset form validation
        resetValidation: function(form) {
            const inputs = form.querySelectorAll('.is-invalid, .is-valid');
            inputs.forEach(input => {
                input.classList.remove('is-invalid', 'is-valid');
            });
        }
    },
    
    // Initialize application
    init: function() {
        this.initEventListeners();
        this.initTooltips();
        this.initPopovers();
        this.initFormValidation();
        this.initAjaxForms();
    },
    
    // Initialize event listeners
    initEventListeners: function() {
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Auto-hide alerts
        document.querySelectorAll('.alert').forEach(alert => {
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.classList.remove('show');
                    setTimeout(() => alert.remove(), 150);
                }
            }, 5000);
        });
    },
    
    // Initialize Bootstrap tooltips
    initTooltips: function() {
        if (typeof bootstrap !== 'undefined') {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    },
    
    // Initialize Bootstrap popovers
    initPopovers: function() {
        if (typeof bootstrap !== 'undefined') {
            const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        }
    },
    
    // Initialize form validation
    initFormValidation: function() {
        const forms = document.querySelectorAll('.needs-validation');
        forms.forEach(form => {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    },
    
    // Initialize AJAX forms
    initAjaxForms: function() {
        const ajaxForms = document.querySelectorAll('.ajax-form');
        ajaxForms.forEach(form => {
            form.addEventListener('submit', function(event) {
                event.preventDefault();
                
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                
                CmsPro.utils.showLoading(submitBtn);
                CmsPro.forms.resetValidation(form);
                
                const formData = CmsPro.forms.serialize(form);
                const url = form.action || window.location.href;
                const method = form.method.toLowerCase() || 'post';
                
                CmsPro.ajax[method](url, formData)
                    .then(response => {
                        if (response.success) {
                            CmsPro.utils.showAlert(response.message || 'Success!', 'success');
                            if (response.redirect) {
                                setTimeout(() => {
                                    window.location.href = response.redirect;
                                }, 1000);
                            }
                        } else {
                            CmsPro.utils.showAlert(response.message || 'An error occurred.', 'danger');
                        }
                    })
                    .catch(error => {
                        CmsPro.utils.showAlert('An error occurred. Please try again.', 'danger');
                    })
                    .finally(() => {
                        CmsPro.utils.hideLoading(submitBtn, originalText);
                    });
            });
        });
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    CmsPro.init();
});

// Export for use in other scripts
window.CmsPro = CmsPro;
