{% extends "layouts/admin.twig" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="categories-management-container">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ title }}</h1>
            <p class="text-muted mb-0">{{ __('Organize your content with categories') }}</p>
        </div>
        <div class="btn-group">
            {% if auth().can('categories.create') %}
            <a href="{{ url('/admin/categories/create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>{{ __('New Category') }}
            </a>
            {% endif %}
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon bg-primary">
                                <i class="fas fa-folder"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="stat-value">{{ categories|length }}</div>
                            <div class="stat-label">{{ __('Total Categories') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon bg-success">
                                <i class="fas fa-sitemap"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="stat-value">{{ categories|filter(c => c.parent_id is null)|length }}</div>
                            <div class="stat-label">{{ __('Parent Categories') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon bg-info">
                                <i class="fas fa-file-alt"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="stat-value">{{ categories|map(c => c.posts_count)|reduce((a, b) => a + b, 0) }}</div>
                            <div class="stat-label">{{ __('Total Posts') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon bg-warning">
                                <i class="fas fa-folder-open"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="stat-value">{{ categories|filter(c => c.posts_count > 0)|length }}</div>
                            <div class="stat-label">{{ __('Used Categories') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Filters Sidebar -->
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-filter me-2"></i>{{ __('Filters') }}
                    </h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ url('/admin/categories') }}" id="filters-form">
                        <!-- Search -->
                        <div class="mb-3">
                            <label for="search" class="form-label">{{ __('Search') }}</label>
                            <input type="text" class="form-control form-control-sm" id="search" name="search" 
                                   value="{{ current_filters.search }}" 
                                   placeholder="{{ __('Search categories...') }}">
                        </div>
                        
                        <!-- Parent Filter -->
                        <div class="mb-3">
                            <label for="parent_id" class="form-label">{{ __('Parent Category') }}</label>
                            <select class="form-select form-select-sm" id="parent_id" name="parent_id">
                                <option value="" {{ current_filters.parent_id == '' ? 'selected' : '' }}>
                                    {{ __('All Categories') }}
                                </option>
                                <option value="0" {{ current_filters.parent_id == '0' ? 'selected' : '' }}>
                                    {{ __('Root Categories Only') }}
                                </option>
                                {% for parent in parent_categories %}
                                <option value="{{ parent.id }}" {{ current_filters.parent_id == parent.id ? 'selected' : '' }}>
                                    {{ parent.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-search me-1"></i>{{ __('Apply Filters') }}
                            </button>
                            <a href="{{ url('/admin/categories') }}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times me-1"></i>{{ __('Clear') }}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Categories List -->
        <div class="col-md-9">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-list me-2"></i>{{ __('Categories') }}
                    </h6>
                </div>
                
                <div class="card-body p-0">
                    {% if categories and categories|length > 0 %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>{{ __('Name') }}</th>
                                    <th width="150">{{ __('Parent') }}</th>
                                    <th width="100">{{ __('Posts') }}</th>
                                    <th width="100">{{ __('Order') }}</th>
                                    <th width="120">{{ __('Color') }}</th>
                                    <th width="120">{{ __('Actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for category in categories %}
                                <tr data-category-id="{{ category.id }}">
                                    <td>
                                        <div class="category-info">
                                            <div class="d-flex align-items-center">
                                                {% if category.icon %}
                                                <i class="{{ category.icon }} me-2" style="color: {{ category.color }}"></i>
                                                {% else %}
                                                <div class="category-color-indicator me-2" 
                                                     style="background-color: {{ category.color }}; width: 16px; height: 16px; border-radius: 3px;"></div>
                                                {% endif %}
                                                <div>
                                                    <h6 class="mb-1">{{ category.name }}</h6>
                                                    {% if category.description %}
                                                    <small class="text-muted">{{ category.description|slice(0, 60) }}...</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if category.parent_name %}
                                        <span class="badge bg-light text-dark">{{ category.parent_name }}</span>
                                        {% else %}
                                        <span class="text-muted">{{ __('Root') }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ category.posts_count }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ category.sort_order ?? 0 }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="color-preview me-2" 
                                                 style="background-color: {{ category.color }}; width: 20px; height: 20px; border-radius: 4px; border: 1px solid #dee2e6;"></div>
                                            <small class="text-muted">{{ category.color }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            {% if auth().can('categories.edit') %}
                                            <a href="{{ url('/admin/categories/' ~ category.id ~ '/edit') }}" 
                                               class="btn btn-outline-primary btn-sm" title="{{ __('Edit') }}">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}
                                            
                                            {% if auth().can('categories.delete') %}
                                            <button type="button" class="btn btn-outline-danger btn-sm delete-category-btn" 
                                                    data-category-id="{{ category.id }}" data-category-name="{{ category.name }}" 
                                                    title="{{ __('Delete') }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if pagination.pages > 1 %}
                    <div class="card-footer bg-white border-0">
                        <div class="d-flex justify-content-center">
                            <nav aria-label="Categories pagination">
                                <ul class="pagination mb-0">
                                    {% if pagination.current_page > 1 %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url('/admin/categories') }}?{{ http_build_query(current_filters|merge({page: pagination.current_page - 1})) }}">
                                            {{ __('Previous') }}
                                        </a>
                                    </li>
                                    {% endif %}
                                    
                                    {% for page in range(max(1, pagination.current_page - 2), min(pagination.pages, pagination.current_page + 2)) %}
                                    <li class="page-item {{ page == pagination.current_page ? 'active' : '' }}">
                                        <a class="page-link" href="{{ url('/admin/categories') }}?{{ http_build_query(current_filters|merge({page: page})) }}">
                                            {{ page }}
                                        </a>
                                    </li>
                                    {% endfor %}
                                    
                                    {% if pagination.current_page < pagination.pages %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url('/admin/categories') }}?{{ http_build_query(current_filters|merge({page: pagination.current_page + 1})) }}">
                                            {{ __('Next') }}
                                        </a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-folder fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">{{ __('No categories found') }}</h5>
                        <p class="text-muted">
                            {% if current_filters.search or current_filters.parent_id %}
                                {{ __('Try adjusting your filters or') }}
                                <a href="{{ url('/admin/categories') }}">{{ __('view all categories') }}</a>
                            {% else %}
                                {{ __('Get started by creating your first category.') }}
                            {% endif %}
                        </p>
                        {% if auth().can('categories.create') %}
                        <a href="{{ url('/admin/categories/create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>{{ __('Create First Category') }}
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
/**
 * Categories Management
 */
class CategoriesManager {
    constructor() {
        this.init();
    }
    
    init() {
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Delete category buttons
        document.querySelectorAll('.delete-category-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handleDeleteCategory(e);
            });
        });
        
        // Auto-submit filters on change
        const filtersForm = document.getElementById('filters-form');
        if (filtersForm) {
            const inputs = filtersForm.querySelectorAll('input, select');
            inputs.forEach(input => {
                if (input.type !== 'submit') {
                    input.addEventListener('change', () => {
                        filtersForm.submit();
                    });
                }
            });
        }
    }
    
    async handleDeleteCategory(e) {
        const btn = e.currentTarget;
        const categoryId = btn.dataset.categoryId;
        const categoryName = btn.dataset.categoryName;
        
        if (!confirm(`{{ __('Are you sure you want to delete the category') }} "${categoryName}"?`)) {
            return;
        }
        
        // Show loading state
        const originalHtml = btn.innerHTML;
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        
        try {
            const response = await fetch(`/admin/categories/${categoryId}`, {
                method: 'DELETE',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': this.getCsrfToken()
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Remove row from table
                const row = btn.closest('tr');
                row.style.transition = 'opacity 0.3s ease';
                row.style.opacity = '0';
                
                setTimeout(() => {
                    row.remove();
                    this.showNotification(result.message, 'success');
                }, 300);
            } else {
                this.showNotification(result.message, 'error');
            }
            
        } catch (error) {
            console.error('Delete category error:', error);
            this.showNotification('{{ __("An error occurred while deleting the category") }}', 'error');
        } finally {
            // Restore button state
            btn.disabled = false;
            btn.innerHTML = originalHtml;
        }
    }
    
    getCsrfToken() {
        return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
        notification.innerHTML = `
            ${this.escapeHtml(message)}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    new CategoriesManager();
});
</script>

<style>
/* Categories specific styles */
.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #495057;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.category-info h6 {
    font-size: 0.95rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.category-color-indicator {
    flex-shrink: 0;
}

.color-preview {
    flex-shrink: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .stat-value {
        font-size: 1.25rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

/* Dark mode support */
[data-bs-theme="dark"] .stat-value {
    color: #e2e8f0;
}

[data-bs-theme="dark"] .stat-label {
    color: #a0aec0;
}

[data-bs-theme="dark"] .category-info h6 {
    color: #e2e8f0;
}
</style>
{% endblock %}
