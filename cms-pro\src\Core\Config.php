<?php

namespace CmsPro\Core;

/**
 * Configuration Manager
 * 
 * @package CmsPro\Core
 */
class Config
{
    private $config = [];
    private $loaded = [];

    public function __construct()
    {
        $this->loadEnvironmentVariables();
    }

    /**
     * Load environment variables from .env file
     */
    private function loadEnvironmentVariables()
    {
        $envFile = ROOT_PATH . '/.env';
        
        if (!file_exists($envFile)) {
            return;
        }

        $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            if (strpos(trim($line), '#') === 0) {
                continue;
            }

            list($name, $value) = explode('=', $line, 2);
            $name = trim($name);
            $value = trim($value);

            if (!array_key_exists($name, $_SERVER) && !array_key_exists($name, $_ENV)) {
                putenv(sprintf('%s=%s', $name, $value));
                $_ENV[$name] = $value;
                $_SERVER[$name] = $value;
            }
        }
    }

    /**
     * Get configuration value
     */
    public function get($key, $default = null)
    {
        $keys = explode('.', $key);
        $file = array_shift($keys);

        if (!isset($this->loaded[$file])) {
            $this->load($file);
        }

        $config = $this->config[$file] ?? [];

        foreach ($keys as $segment) {
            if (!is_array($config) || !array_key_exists($segment, $config)) {
                return $default;
            }
            $config = $config[$segment];
        }

        return $config;
    }

    /**
     * Set configuration value
     */
    public function set($key, $value)
    {
        $keys = explode('.', $key);
        $file = array_shift($keys);

        if (!isset($this->loaded[$file])) {
            $this->load($file);
        }

        $config = &$this->config[$file];

        foreach ($keys as $segment) {
            if (!is_array($config)) {
                $config = [];
            }
            $config = &$config[$segment];
        }

        $config = $value;
    }

    /**
     * Load configuration file
     */
    private function load($file)
    {
        $path = CONFIG_PATH . '/' . $file . '.php';

        if (file_exists($path)) {
            $this->config[$file] = require $path;
        } else {
            $this->config[$file] = [];
        }

        $this->loaded[$file] = true;
    }

    /**
     * Check if configuration exists
     */
    public function has($key)
    {
        return $this->get($key) !== null;
    }

    /**
     * Get all configuration
     */
    public function all()
    {
        return $this->config;
    }
}
