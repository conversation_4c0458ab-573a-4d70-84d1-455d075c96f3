-- Insert default roles
INSERT INTO `roles` (`id`, `name`, `slug`, `description`, `is_default`) VALUES
(1, 'Super Admin', 'super-admin', 'Full system access with all permissions', 0),
(2, 'Admin', 'admin', 'Administrative access with most permissions', 0),
(3, 'Editor', 'editor', 'Content management and editing permissions', 1),
(4, 'Contributor', 'contributor', 'Basic content creation permissions', 0),
(5, 'User', 'user', 'Basic user permissions', 0);

-- Insert default permissions
INSERT INTO `permissions` (`name`, `slug`, `description`, `group`) VALUES
-- User Management
('View Users', 'users.view', 'View user list and profiles', 'users'),
('Create Users', 'users.create', 'Create new users', 'users'),
('Edit Users', 'users.edit', 'Edit user information', 'users'),
('Delete Users', 'users.delete', 'Delete users', 'users'),
('Manage User Roles', 'users.roles', 'Assign and manage user roles', 'users'),

-- Content Management
('View Content', 'content.view', 'View content list', 'content'),
('Create Content', 'content.create', 'Create new content', 'content'),
('Edit Content', 'content.edit', 'Edit existing content', 'content'),
('Delete Content', 'content.delete', 'Delete content', 'content'),
('Publish Content', 'content.publish', 'Publish and unpublish content', 'content'),
('Manage Content Categories', 'content.categories', 'Manage content categories', 'content'),

-- Page Management
('View Pages', 'pages.view', 'View page list', 'pages'),
('Create Pages', 'pages.create', 'Create new pages', 'pages'),
('Edit Pages', 'pages.edit', 'Edit existing pages', 'pages'),
('Delete Pages', 'pages.delete', 'Delete pages', 'pages'),
('Publish Pages', 'pages.publish', 'Publish and unpublish pages', 'pages'),

-- Blog Management
('View Blog', 'blog.view', 'View blog posts', 'blog'),
('Create Blog Posts', 'blog.create', 'Create new blog posts', 'blog'),
('Edit Blog Posts', 'blog.edit', 'Edit blog posts', 'blog'),
('Delete Blog Posts', 'blog.delete', 'Delete blog posts', 'blog'),
('Publish Blog Posts', 'blog.publish', 'Publish and unpublish blog posts', 'blog'),

-- Media Management
('View Media', 'media.view', 'View media library', 'media'),
('Upload Media', 'media.upload', 'Upload new media files', 'media'),
('Edit Media', 'media.edit', 'Edit media information', 'media'),
('Delete Media', 'media.delete', 'Delete media files', 'media'),

-- Dynamic Fields
('View Fields', 'fields.view', 'View dynamic fields', 'fields'),
('Create Fields', 'fields.create', 'Create new dynamic fields', 'fields'),
('Edit Fields', 'fields.edit', 'Edit dynamic fields', 'fields'),
('Delete Fields', 'fields.delete', 'Delete dynamic fields', 'fields'),
('Manage Field Groups', 'fields.groups', 'Manage field groups', 'fields'),

-- System Settings
('View Settings', 'settings.view', 'View system settings', 'settings'),
('Edit Settings', 'settings.edit', 'Edit system settings', 'settings'),
('Manage Themes', 'themes.manage', 'Manage themes and customization', 'settings'),
('Manage Languages', 'languages.manage', 'Manage languages and translations', 'settings'),

-- System Administration
('View System Info', 'system.view', 'View system information', 'system'),
('Manage Backups', 'backups.manage', 'Create and manage backups', 'system'),
('View Logs', 'logs.view', 'View system logs', 'system'),
('Clear Cache', 'cache.clear', 'Clear system cache', 'system'),

-- API Access
('API Access', 'api.access', 'Access to API endpoints', 'api'),
('API Admin', 'api.admin', 'Administrative API access', 'api');

-- Assign permissions to roles
-- Super Admin gets all permissions
INSERT INTO `role_permissions` (`role_id`, `permission_id`)
SELECT 1, `id` FROM `permissions`;

-- Admin gets most permissions (excluding some system-critical ones)
INSERT INTO `role_permissions` (`role_id`, `permission_id`)
SELECT 2, `id` FROM `permissions` 
WHERE `slug` NOT IN ('users.delete', 'system.view', 'logs.view', 'api.admin');

-- Editor gets content-related permissions
INSERT INTO `role_permissions` (`role_id`, `permission_id`)
SELECT 3, `id` FROM `permissions` 
WHERE `group` IN ('content', 'pages', 'blog', 'media', 'fields') 
AND `slug` NOT IN ('content.delete', 'pages.delete', 'blog.delete', 'media.delete', 'fields.delete');

-- Contributor gets basic content creation permissions
INSERT INTO `role_permissions` (`role_id`, `permission_id`)
SELECT 4, `id` FROM `permissions` 
WHERE `slug` IN (
    'content.view', 'content.create', 'content.edit',
    'blog.view', 'blog.create', 'blog.edit',
    'media.view', 'media.upload'
);

-- User gets basic view permissions
INSERT INTO `role_permissions` (`role_id`, `permission_id`)
SELECT 5, `id` FROM `permissions` 
WHERE `slug` IN ('content.view', 'pages.view', 'blog.view', 'media.view');

-- Create default admin user
INSERT INTO `users` (
    `uuid`, 
    `username`, 
    `email`, 
    `password`, 
    `first_name`, 
    `last_name`, 
    `role_id`, 
    `status`,
    `email_verified_at`
) VALUES (
    UUID(),
    'admin',
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'System',
    'Administrator',
    1,
    'active',
    NOW()
);

-- Create default editor user
INSERT INTO `users` (
    `uuid`, 
    `username`, 
    `email`, 
    `password`, 
    `first_name`, 
    `last_name`, 
    `role_id`, 
    `status`,
    `email_verified_at`
) VALUES (
    UUID(),
    'editor',
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'Content',
    'Editor',
    3,
    'active',
    NOW()
);
