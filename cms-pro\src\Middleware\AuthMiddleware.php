<?php

namespace CmsPro\Middleware;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\RedirectResponse;

/**
 * Authentication Middleware
 * 
 * @package CmsPro\Middleware
 */
class AuthMiddleware
{
    /**
     * Handle the request
     */
    public function handle(Request $request, callable $next)
    {
        // Check if route requires authentication
        if ($this->requiresAuth($request)) {
            if (!$this->isAuthenticated()) {
                return $this->redirectToLogin($request);
            }
            
            // Check user permissions
            if (!$this->hasPermission($request)) {
                return new Response('Forbidden', 403);
            }
        }
        
        return $next($request);
    }

    /**
     * Check if route requires authentication
     */
    private function requiresAuth(Request $request)
    {
        $path = $request->getPathInfo();
        
        // Admin routes require authentication
        if (strpos($path, '/admin') === 0 && $path !== '/admin/login') {
            return true;
        }
        
        // API routes require authentication (except public API)
        if (strpos($path, '/api/v1') === 0) {
            return true;
        }
        
        return false;
    }

    /**
     * Check if user is authenticated
     */
    private function isAuthenticated()
    {
        // This will be implemented when we create the Auth system
        // For now, return false to allow development
        return session()->has('user_id');
    }

    /**
     * Check if user has permission
     */
    private function hasPermission(Request $request)
    {
        // This will be implemented when we create the RBAC system
        // For now, return true if authenticated
        return $this->isAuthenticated();
    }

    /**
     * Redirect to login page
     */
    private function redirectToLogin(Request $request)
    {
        $path = $request->getPathInfo();
        
        if (strpos($path, '/admin') === 0) {
            return new RedirectResponse('/admin/login');
        }
        
        if (strpos($path, '/api') === 0) {
            return new Response('Unauthorized', 401);
        }
        
        return new RedirectResponse('/login');
    }
}
