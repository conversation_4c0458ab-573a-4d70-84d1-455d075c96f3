<?php

namespace CmsPro\Models;

use CmsPro\Core\Model;

/**
 * Comment Model
 * 
 * @package CmsPro\Models
 */
class Comment extends Model
{
    protected $table = 'comments';
    
    protected $fillable = [
        'post_id',
        'post_type',
        'parent_id',
        'author_name',
        'author_email',
        'author_url',
        'author_ip',
        'content',
        'status',
        'user_agent'
    ];

    protected $casts = [
        'post_id' => 'integer',
        'parent_id' => 'integer'
    ];

    /**
     * Get filtered comments
     */
    public static function getFiltered($filters = [])
    {
        $db = app()->getDatabase();
        
        $whereConditions = [];
        $params = [];
        
        // Status filter
        if (!empty($filters['status']) && $filters['status'] !== 'all') {
            $whereConditions[] = "c.status = ?";
            $params[] = $filters['status'];
        }
        
        // Post type filter
        if (!empty($filters['post_type'])) {
            $whereConditions[] = "c.post_type = ?";
            $params[] = $filters['post_type'];
        }
        
        // Post filter
        if (!empty($filters['post_id'])) {
            $whereConditions[] = "c.post_id = ?";
            $params[] = $filters['post_id'];
        }
        
        // Search filter
        if (!empty($filters['search'])) {
            $whereConditions[] = "(c.author_name LIKE ? OR c.author_email LIKE ? OR c.content LIKE ?)";
            $searchTerm = "%{$filters['search']}%";
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
        }
        
        // Date filters
        if (!empty($filters['date_from'])) {
            $whereConditions[] = "c.created_at >= ?";
            $params[] = $filters['date_from'] . ' 00:00:00';
        }
        
        if (!empty($filters['date_to'])) {
            $whereConditions[] = "c.created_at <= ?";
            $params[] = $filters['date_to'] . ' 23:59:59';
        }
        
        $whereClause = $whereConditions ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // Get total count
        $totalQuery = "SELECT COUNT(*) as total FROM comments c {$whereClause}";
        $totalResult = $db->selectOne($totalQuery, $params);
        $total = $totalResult['total'];
        
        // Calculate pagination
        $page = $filters['page'] ?? 1;
        $perPage = $filters['per_page'] ?? 20;
        $offset = ($page - 1) * $perPage;
        $totalPages = ceil($total / $perPage);
        
        // Get comments
        $commentsQuery = "
            SELECT c.*, 
                   CASE 
                       WHEN c.post_type = 'blog_post' THEN bp.title
                       WHEN c.post_type = 'page' THEN p.title
                   END as post_title,
                   CASE 
                       WHEN c.post_type = 'blog_post' THEN bp.slug
                       WHEN c.post_type = 'page' THEN p.slug
                   END as post_slug,
                   pc.author_name as parent_author
            FROM comments c
            LEFT JOIN blog_posts bp ON c.post_id = bp.id AND c.post_type = 'blog_post'
            LEFT JOIN pages p ON c.post_id = p.id AND c.post_type = 'page'
            LEFT JOIN comments pc ON c.parent_id = pc.id
            {$whereClause}
            ORDER BY c.created_at DESC
            LIMIT {$perPage} OFFSET {$offset}
        ";
        
        $comments = $db->select($commentsQuery, $params);
        
        // Convert to model instances
        $instances = [];
        foreach ($comments as $comment) {
            $instance = new static();
            $instance->fill($comment);
            $instance->setAttribute('post_title', $comment['post_title']);
            $instance->setAttribute('post_slug', $comment['post_slug']);
            $instance->setAttribute('parent_author', $comment['parent_author']);
            $instances[] = $instance;
        }
        
        return [
            'comments' => $instances,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'pages' => $totalPages
            ]
        ];
    }

    /**
     * Get comments for a post
     */
    public static function getForPost($postId, $postType = 'blog_post', $status = 'approved')
    {
        $db = app()->getDatabase();
        
        $comments = $db->select(
            "SELECT c.*, pc.author_name as parent_author
             FROM comments c
             LEFT JOIN comments pc ON c.parent_id = pc.id
             WHERE c.post_id = ? AND c.post_type = ? AND c.status = ?
             ORDER BY c.created_at ASC",
            [$postId, $postType, $status]
        );
        
        $instances = [];
        foreach ($comments as $comment) {
            $instance = new static();
            $instance->fill($comment);
            $instance->setAttribute('parent_author', $comment['parent_author']);
            $instances[] = $instance;
        }
        
        return static::buildCommentTree($instances);
    }

    /**
     * Create new comment
     */
    public static function create($data)
    {
        $db = app()->getDatabase();
        
        // Set timestamps
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        // Set default status
        if (!isset($data['status'])) {
            $data['status'] = 'pending'; // Default to pending for moderation
        }
        
        $id = $db->insert('comments', $data);
        
        if ($id) {
            // Update post comment count
            static::updatePostCommentCount($data['post_id'], $data['post_type']);
            
            $instance = new static();
            $instance->setAttribute('id', $id);
            $instance->fill($data);
            return $instance;
        }
        
        return false;
    }

    /**
     * Find comment by ID
     */
    public static function find($id)
    {
        $db = app()->getDatabase();
        $comment = $db->selectOne(
            "SELECT c.*, 
                    CASE 
                        WHEN c.post_type = 'blog_post' THEN bp.title
                        WHEN c.post_type = 'page' THEN p.title
                    END as post_title,
                    CASE 
                        WHEN c.post_type = 'blog_post' THEN bp.slug
                        WHEN c.post_type = 'page' THEN p.slug
                    END as post_slug,
                    pc.author_name as parent_author
             FROM comments c
             LEFT JOIN blog_posts bp ON c.post_id = bp.id AND c.post_type = 'blog_post'
             LEFT JOIN pages p ON c.post_id = p.id AND c.post_type = 'page'
             LEFT JOIN comments pc ON c.parent_id = pc.id
             WHERE c.id = ?",
            [$id]
        );
        
        if (!$comment) {
            return null;
        }
        
        $instance = new static();
        $instance->fill($comment);
        $instance->setAttribute('post_title', $comment['post_title']);
        $instance->setAttribute('post_slug', $comment['post_slug']);
        $instance->setAttribute('parent_author', $comment['parent_author']);
        
        return $instance;
    }

    /**
     * Update comment
     */
    public function update($data)
    {
        $db = app()->getDatabase();
        
        // Set updated timestamp
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $updated = $db->update(
            "UPDATE comments SET " . implode(', ', array_map(fn($key) => "{$key} = ?", array_keys($data))) . " WHERE id = ?",
            array_merge(array_values($data), [$this->getId()])
        );
        
        if ($updated) {
            foreach ($data as $key => $value) {
                $this->setAttribute($key, $value);
            }
            return true;
        }
        
        return false;
    }

    /**
     * Delete comment
     */
    public function delete()
    {
        $db = app()->getDatabase();
        
        // Delete child comments first
        $db->delete("DELETE FROM comments WHERE parent_id = ?", [$this->getId()]);
        
        // Delete the comment
        $deleted = $db->delete("DELETE FROM comments WHERE id = ?", [$this->getId()]);
        
        if ($deleted) {
            // Update post comment count
            static::updatePostCommentCount($this->getPostId(), $this->getPostType());
        }
        
        return $deleted;
    }

    /**
     * Approve comment
     */
    public function approve()
    {
        return $this->update(['status' => 'approved']);
    }

    /**
     * Mark comment as spam
     */
    public function markAsSpam()
    {
        return $this->update(['status' => 'spam']);
    }

    /**
     * Move comment to trash
     */
    public function trash()
    {
        return $this->update(['status' => 'trash']);
    }

    /**
     * Get comment statistics
     */
    public static function getStatistics()
    {
        $db = app()->getDatabase();
        
        $stats = $db->selectOne(
            "SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'spam' THEN 1 ELSE 0 END) as spam,
                SUM(CASE WHEN status = 'trash' THEN 1 ELSE 0 END) as trash
             FROM comments"
        );
        
        return [
            'total' => (int) $stats['total'],
            'approved' => (int) $stats['approved'],
            'pending' => (int) $stats['pending'],
            'spam' => (int) $stats['spam'],
            'trash' => (int) $stats['trash']
        ];
    }

    /**
     * Build comment tree structure
     */
    private static function buildCommentTree($comments, $parentId = null)
    {
        $tree = [];
        
        foreach ($comments as $comment) {
            if ($comment->getParentId() == $parentId) {
                $children = static::buildCommentTree($comments, $comment->getId());
                if ($children) {
                    $comment->setAttribute('children', $children);
                }
                $tree[] = $comment;
            }
        }
        
        return $tree;
    }

    /**
     * Update post comment count
     */
    private static function updatePostCommentCount($postId, $postType)
    {
        $db = app()->getDatabase();
        
        // Count approved comments
        $count = $db->selectOne(
            "SELECT COUNT(*) as count FROM comments WHERE post_id = ? AND post_type = ? AND status = 'approved'",
            [$postId, $postType]
        );
        
        // Update post table
        if ($postType === 'blog_post') {
            $db->update(
                "UPDATE blog_posts SET comments_count = ? WHERE id = ?",
                [$count['count'], $postId]
            );
        } elseif ($postType === 'page') {
            $db->update(
                "UPDATE pages SET comments_count = ? WHERE id = ?",
                [$count['count'], $postId]
            );
        }
    }

    /**
     * Check if comment is spam (basic spam detection)
     */
    public function isSpam()
    {
        $content = strtolower($this->getContent());
        $spamKeywords = ['viagra', 'casino', 'poker', 'loan', 'mortgage', 'insurance'];
        
        foreach ($spamKeywords as $keyword) {
            if (strpos($content, $keyword) !== false) {
                return true;
            }
        }
        
        // Check for excessive links
        $linkCount = substr_count($content, 'http');
        if ($linkCount > 3) {
            return true;
        }
        
        return false;
    }

    // Getters
    public function getId() { return $this->getAttribute('id'); }
    public function getPostId() { return $this->getAttribute('post_id'); }
    public function getPostType() { return $this->getAttribute('post_type'); }
    public function getParentId() { return $this->getAttribute('parent_id'); }
    public function getAuthorName() { return $this->getAttribute('author_name'); }
    public function getAuthorEmail() { return $this->getAttribute('author_email'); }
    public function getAuthorUrl() { return $this->getAttribute('author_url'); }
    public function getAuthorIp() { return $this->getAttribute('author_ip'); }
    public function getContent() { return $this->getAttribute('content'); }
    public function getStatus() { return $this->getAttribute('status'); }
    public function getUserAgent() { return $this->getAttribute('user_agent'); }
    public function getPostTitle() { return $this->getAttribute('post_title'); }
    public function getPostSlug() { return $this->getAttribute('post_slug'); }
    public function getParentAuthor() { return $this->getAttribute('parent_author'); }
    public function getChildren() { return $this->getAttribute('children', []); }
    public function getCreatedAt() { return $this->getAttribute('created_at'); }
    public function getUpdatedAt() { return $this->getAttribute('updated_at'); }
}
