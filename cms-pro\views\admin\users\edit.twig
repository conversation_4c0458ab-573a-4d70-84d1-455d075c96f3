{% extends "admin/users/create.twig" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="user-form-container">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ title }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ url('/admin/users') }}">{{ __('Users') }}</a>
                    </li>
                    <li class="breadcrumb-item active">{{ __('Edit') }}</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <a href="{{ url('/admin/users') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>{{ __('Back to Users') }}
            </a>
        </div>
    </div>

    <!-- User Info Bar -->
    <div class="alert alert-info d-flex align-items-center mb-4">
        <div class="flex-grow-1">
            <div class="d-flex align-items-center">
                <i class="fas fa-info-circle me-2"></i>
                <div>
                    <strong>{{ __('User ID') }}:</strong> {{ user.id }} |
                    <strong>{{ __('Joined') }}:</strong> {{ user.created_at|date('M j, Y H:i') }} |
                    <strong>{{ __('Last Login') }}:</strong> {{ user.last_login_at ? user.last_login_at|date('M j, Y H:i') : __('Never') }}
                </div>
            </div>
        </div>
        {% if activity_log and activity_log|length > 0 %}
        <div>
            <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#activityModal">
                <i class="fas fa-history me-1"></i>{{ __('Activity Log') }}
            </button>
        </div>
        {% endif %}
    </div>

    <form method="POST" action="{{ url('/admin/users/' ~ user.id) }}" class="user-form" id="user-form" novalidate>
        {{ csrf_field() | raw }}
        <input type="hidden" name="_method" value="PUT">
        
        <div class="row">
            <!-- Main Content Column -->
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-user me-2"></i>{{ __('User Information') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- First Name -->
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">
                                    {{ __('First Name') }} <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control {{ errors.first_name ? 'is-invalid' : '' }}" 
                                       id="first_name" 
                                       name="first_name" 
                                       value="{{ old('first_name', user.first_name) }}" 
                                       required
                                       maxlength="100"
                                       placeholder="{{ __('Enter first name...') }}">
                                {% if errors.first_name %}
                                    <div class="invalid-feedback">{{ errors.first_name }}</div>
                                {% endif %}
                            </div>

                            <!-- Last Name -->
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">
                                    {{ __('Last Name') }} <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control {{ errors.last_name ? 'is-invalid' : '' }}" 
                                       id="last_name" 
                                       name="last_name" 
                                       value="{{ old('last_name', user.last_name) }}" 
                                       required
                                       maxlength="100"
                                       placeholder="{{ __('Enter last name...') }}">
                                {% if errors.last_name %}
                                    <div class="invalid-feedback">{{ errors.last_name }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Email -->
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                {{ __('Email Address') }} <span class="text-danger">*</span>
                            </label>
                            <input type="email" 
                                   class="form-control {{ errors.email ? 'is-invalid' : '' }}" 
                                   id="email" 
                                   name="email" 
                                   value="{{ old('email', user.email) }}" 
                                   required
                                   maxlength="255"
                                   placeholder="{{ __('Enter email address...') }}">
                            {% if errors.email %}
                                <div class="invalid-feedback">{{ errors.email }}</div>
                            {% endif %}
                        </div>

                        <!-- Username -->
                        <div class="mb-3">
                            <label for="username" class="form-label">{{ __('Username') }}</label>
                            <input type="text" 
                                   class="form-control {{ errors.username ? 'is-invalid' : '' }}" 
                                   id="username" 
                                   name="username" 
                                   value="{{ old('username', user.username) }}"
                                   maxlength="50"
                                   placeholder="{{ __('Optional username...') }}">
                            {% if errors.username %}
                                <div class="invalid-feedback">{{ errors.username }}</div>
                            {% endif %}
                            <div class="form-text">
                                {{ __('Optional. If not provided, email will be used for login.') }}
                            </div>
                        </div>

                        <!-- Password -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">{{ __('New Password') }}</label>
                                <div class="input-group">
                                    <input type="password" 
                                           class="form-control {{ errors.password ? 'is-invalid' : '' }}" 
                                           id="password" 
                                           name="password" 
                                           minlength="8"
                                           maxlength="255"
                                           placeholder="{{ __('Leave empty to keep current...') }}">
                                    <button type="button" class="btn btn-outline-secondary" id="toggle-password">
                                        <i class="fas fa-eye" id="password-toggle-icon"></i>
                                    </button>
                                </div>
                                {% if errors.password %}
                                    <div class="invalid-feedback">{{ errors.password }}</div>
                                {% endif %}
                                <div class="form-text">
                                    {{ __('Leave empty to keep current password. Minimum 8 characters if changing.') }}
                                </div>
                            </div>

                            <!-- Password Confirmation -->
                            <div class="col-md-6 mb-3">
                                <label for="password_confirmation" class="form-label">{{ __('Confirm New Password') }}</label>
                                <div class="input-group">
                                    <input type="password" 
                                           class="form-control {{ errors.password_confirmation ? 'is-invalid' : '' }}" 
                                           id="password_confirmation" 
                                           name="password_confirmation" 
                                           minlength="8"
                                           maxlength="255"
                                           placeholder="{{ __('Confirm new password...') }}">
                                    <button type="button" class="btn btn-outline-secondary" id="toggle-password-confirmation">
                                        <i class="fas fa-eye" id="password-confirmation-toggle-icon"></i>
                                    </button>
                                </div>
                                {% if errors.password_confirmation %}
                                    <div class="invalid-feedback">{{ errors.password_confirmation }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Roles & Permissions -->
                {% if auth().can('users.manage_roles') or auth().can('users.manage_permissions') %}
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-success">
                            <i class="fas fa-shield-alt me-2"></i>{{ __('Roles & Permissions') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if auth().can('users.manage_roles') %}
                        <!-- Roles -->
                        <div class="mb-4">
                            <label class="form-label">{{ __('Roles') }}</label>
                            <div class="row">
                                {% for role in roles %}
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               id="role_{{ role.id }}" 
                                               name="roles[]" 
                                               value="{{ role.id }}"
                                               {{ (old('roles') and role.id in old('roles')) or (not old('roles') and role.id in user_roles) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="role_{{ role.id }}">
                                            <strong>{{ role.name }}</strong>
                                            {% if role.description %}
                                            <br><small class="text-muted">{{ role.description }}</small>
                                            {% endif %}
                                        </label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}

                        {% if auth().can('users.manage_permissions') %}
                        <!-- Direct Permissions -->
                        <div class="mb-3">
                            <label class="form-label">{{ __('Additional Permissions') }}</label>
                            <div class="form-text mb-3">
                                {{ __('Grant specific permissions in addition to role-based permissions') }}
                            </div>
                            
                            {% for category, categoryPermissions in permissions %}
                            <div class="permission-category mb-3">
                                <h6 class="text-primary mb-2">{{ category|title }}</h6>
                                <div class="row">
                                    {% for permission in categoryPermissions %}
                                    <div class="col-md-6 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" 
                                                   id="permission_{{ permission.id }}" 
                                                   name="permissions[]" 
                                                   value="{{ permission.id }}"
                                                   {{ (old('permissions') and permission.id in old('permissions')) or (not old('permissions') and permission.id in user_permissions) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="permission_{{ permission.id }}">
                                                {{ permission.name }}
                                            </label>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Sidebar Column -->
            <div class="col-lg-4">
                <!-- User Settings -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-warning">
                            <i class="fas fa-cog me-2"></i>{{ __('User Settings') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Status -->
                        <div class="mb-3">
                            <label for="status" class="form-label">{{ __('Status') }}</label>
                            <select class="form-select {{ errors.status ? 'is-invalid' : '' }}" 
                                    id="status" 
                                    name="status" 
                                    required>
                                {% for key, label in statuses %}
                                <option value="{{ key }}" {{ old('status', user.status) == key ? 'selected' : '' }}>
                                    {{ label }}
                                </option>
                                {% endfor %}
                            </select>
                            {% if errors.status %}
                                <div class="invalid-feedback">{{ errors.status }}</div>
                            {% endif %}
                        </div>

                        <!-- Avatar Upload -->
                        <div class="mb-3">
                            <label for="avatar" class="form-label">{{ __('Avatar') }}</label>
                            <div class="avatar-upload-container">
                                <div class="avatar-preview mb-2">
                                    {% if user.avatar %}
                                    <img src="{{ asset(user.avatar) }}" alt="Current Avatar" class="rounded-circle mx-auto d-block" style="width: 80px; height: 80px; object-fit: cover;">
                                    {% else %}
                                    <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center mx-auto">
                                        {{ user.first_name|first|upper }}{{ user.last_name|first|upper }}
                                    </div>
                                    {% endif %}
                                </div>
                                <input type="file" class="form-control" id="avatar" name="avatar" accept="image/*">
                                <div class="form-text">
                                    {{ __('Upload a new profile picture (optional)') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" name="action" value="save" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ __('Update User') }}
                            </button>
                            
                            <div class="btn-group">
                                <button type="submit" name="action" value="save_and_continue" class="btn btn-outline-primary">
                                    <i class="fas fa-save me-2"></i>{{ __('Save & Continue') }}
                                </button>
                                <button type="submit" name="action" value="save_and_new" class="btn btn-outline-primary">
                                    <i class="fas fa-plus me-2"></i>{{ __('Save & New') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Danger Zone -->
                {% if auth().can('users.delete') and user.id != auth().id() %}
                <div class="card border-danger shadow-sm">
                    <div class="card-header bg-danger text-white border-0">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-exclamation-triangle me-2"></i>{{ __('Danger Zone') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">{{ __('Permanently delete this user account and all associated data.') }}</p>
                        <button type="button" class="btn btn-outline-danger w-100" id="delete-user" 
                                data-user-id="{{ user.id }}" data-user-name="{{ user.first_name }} {{ user.last_name }}">
                            <i class="fas fa-trash me-2"></i>{{ __('Delete User') }}
                        </button>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </form>
</div>

<!-- Activity Log Modal -->
{% if activity_log and activity_log|length > 0 %}
<div class="modal fade" id="activityModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('User Activity Log') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>{{ __('Date') }}</th>
                                <th>{{ __('Action') }}</th>
                                <th>{{ __('IP Address') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for activity in activity_log %}
                            <tr>
                                <td>{{ activity.created_at|date('M j, Y H:i') }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ activity.action }}</span>
                                </td>
                                <td>{{ activity.ip_address }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Confirm Delete') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>{{ __('Are you sure you want to delete this user?') }}</p>
                <p><strong class="user-name-placeholder">{{ user.first_name }} {{ user.last_name }}</strong></p>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {{ __('This action will permanently delete the user account and cannot be undone.') }}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ __('Cancel') }}
                </button>
                <button type="button" class="btn btn-danger" id="confirmDelete">
                    <i class="fas fa-trash me-2"></i>{{ __('Delete User') }}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
// Extend UserForm for edit functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize base form functionality
    new UserForm();

    // Add edit-specific functionality
    const editEnhancements = {
        init() {
            this.setupDeleteButton();
            this.setupPasswordValidation();
        },

        setupDeleteButton() {
            const deleteButton = document.getElementById('delete-user');
            if (deleteButton) {
                deleteButton.addEventListener('click', () => {
                    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
                    modal.show();
                });
            }

            const confirmDelete = document.getElementById('confirmDelete');
            if (confirmDelete) {
                confirmDelete.addEventListener('click', async () => {
                    try {
                        const response = await fetch(`/admin/users/{{ user.id }}`, {
                            method: 'DELETE',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest',
                                'X-CSRF-TOKEN': this.getCsrfToken()
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            this.showNotification(result.message, 'success');
                            setTimeout(() => {
                                window.location.href = '/admin/users';
                            }, 1000);
                        } else {
                            this.showNotification(result.message, 'error');
                        }

                    } catch (error) {
                        console.error('Delete error:', error);
                        this.showNotification('An error occurred while deleting the user.', 'error');
                    }
                });
            }
        },

        setupPasswordValidation() {
            // Override password validation for edit form (password is optional)
            const passwordField = document.getElementById('password');
            const passwordConfirmField = document.getElementById('password_confirmation');

            if (passwordField && passwordConfirmField) {
                passwordField.addEventListener('input', () => {
                    this.validateEditPassword();
                });

                passwordConfirmField.addEventListener('input', () => {
                    this.validateEditPasswordConfirmation();
                });
            }
        },

        validateEditPassword() {
            const passwordField = document.getElementById('password');
            const password = passwordField.value;

            // Password is optional in edit mode
            if (password.length === 0) {
                passwordField.classList.remove('is-invalid', 'is-valid');
                return true;
            }

            const isValid = password.length >= 8;

            if (isValid) {
                passwordField.classList.remove('is-invalid');
                passwordField.classList.add('is-valid');
            } else {
                passwordField.classList.remove('is-valid');
                passwordField.classList.add('is-invalid');
            }

            // Also validate confirmation if it has a value
            const confirmField = document.getElementById('password_confirmation');
            if (confirmField.value) {
                this.validateEditPasswordConfirmation();
            }

            return isValid;
        },

        validateEditPasswordConfirmation() {
            const passwordField = document.getElementById('password');
            const confirmField = document.getElementById('password_confirmation');
            const password = passwordField.value;
            const confirmation = confirmField.value;

            // If password is empty, confirmation should also be empty
            if (password.length === 0) {
                if (confirmation.length === 0) {
                    confirmField.classList.remove('is-invalid', 'is-valid');
                    return true;
                } else {
                    confirmField.classList.remove('is-valid');
                    confirmField.classList.add('is-invalid');
                    return false;
                }
            }

            // If password has value, confirmation must match
            const isValid = password === confirmation;

            if (isValid) {
                confirmField.classList.remove('is-invalid');
                confirmField.classList.add('is-valid');
            } else {
                confirmField.classList.remove('is-valid');
                confirmField.classList.add('is-invalid');
            }

            return isValid;
        },

        getCsrfToken() {
            return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
        },

        showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
            notification.innerHTML = `
                ${this.escapeHtml(message)}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        },

        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    };

    // Initialize edit enhancements
    editEnhancements.init();
});
</script>

<style>
/* Edit-specific styles */
.user-form-container .alert-info {
    font-size: 0.875rem;
}

.user-form-container .breadcrumb {
    font-size: 0.875rem;
    margin-bottom: 0;
}

.user-form-container .breadcrumb-item + .breadcrumb-item::before {
    content: "›";
}

.card.border-danger {
    border-color: #dc3545 !important;
}

.card-header.bg-danger {
    background-color: #dc3545 !important;
}

/* Activity modal styles */
.modal-lg .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

/* Dark mode support */
[data-bs-theme="dark"] .alert-info {
    background-color: #1e3a5f;
    border-color: #2c5aa0;
    color: #9ec5fe;
}

[data-bs-theme="dark"] .card.border-danger {
    border-color: #dc3545 !important;
}

[data-bs-theme="dark"] .card-header.bg-danger {
    background-color: #dc3545 !important;
    color: white;
}
</style>
{% endblock %}
