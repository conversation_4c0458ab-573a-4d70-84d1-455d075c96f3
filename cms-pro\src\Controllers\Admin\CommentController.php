<?php

namespace CmsPro\Controllers\Admin;

use CmsPro\Controllers\BaseController;
use CmsPro\Models\Comment;
use CmsPro\Services\ValidationService;
use CmsPro\Services\SanitizationService;
use CmsPro\Services\ActivityLogger;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Comment Controller for Admin Panel
 * 
 * @package CmsPro\Controllers\Admin
 */
class CommentController extends BaseController
{
    private $validationService;
    private $sanitizationService;
    private $activityLogger;

    public function __construct()
    {
        parent::__construct();
        
        $this->validationService = new ValidationService();
        $this->sanitizationService = new SanitizationService();
        $this->activityLogger = new ActivityLogger();
    }

    /**
     * Display comments list
     */
    public function index(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('comments.view')) {
                return $this->forbidden('You do not have permission to view comments.');
            }

            // Get filters
            $filters = [
                'status' => $request->query->get('status', 'all'),
                'post_type' => $request->query->get('post_type', ''),
                'post_id' => $request->query->get('post_id', ''),
                'search' => $request->query->get('search', ''),
                'date_from' => $request->query->get('date_from', ''),
                'date_to' => $request->query->get('date_to', ''),
                'page' => max(1, (int) $request->query->get('page', 1)),
                'per_page' => 20
            ];

            // Get comments
            $result = Comment::getFiltered($filters);
            $comments = $result['comments'];
            $pagination = $result['pagination'];

            // Get statistics
            $statistics = Comment::getStatistics();

            // Log activity
            $this->activityLogger->log('comments_viewed', [
                'user_id' => auth()->id(),
                'filters' => $filters
            ]);

            $data = [
                'title' => __('Comments'),
                'comments' => $comments,
                'pagination' => $pagination,
                'statistics' => $statistics,
                'current_filters' => $filters
            ];

            return $this->view('admin/comments/index.twig', $data);

        } catch (\Exception $e) {
            $this->logError('comments_index_error', $e, $request);
            $this->flashError(__('An error occurred while loading comments.'));
            return $this->redirectToRoute('admin.dashboard');
        }
    }

    /**
     * Show comment details
     */
    public function show(Request $request, $id)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('comments.view')) {
                return $this->forbidden('You do not have permission to view comments.');
            }

            // Find comment
            $comment = Comment::find($id);
            if (!$comment) {
                $this->flashError(__('Comment not found.'));
                return $this->redirectToRoute('admin.comments.index');
            }

            $data = [
                'title' => __('Comment Details'),
                'comment' => $comment
            ];

            return $this->view('admin/comments/show.twig', $data);

        } catch (\Exception $e) {
            $this->logError('comments_show_error', $e, $request);
            $this->flashError(__('An error occurred while loading the comment.'));
            return $this->redirectToRoute('admin.comments.index');
        }
    }

    /**
     * Approve comment
     */
    public function approve(Request $request, $id)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('comments.moderate')) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'You do not have permission to moderate comments.'
                    ], 403);
                }
                
                return $this->forbidden('You do not have permission to moderate comments.');
            }

            // Find comment
            $comment = Comment::find($id);
            if (!$comment) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Comment not found.'
                    ], 404);
                }
                
                $this->flashError(__('Comment not found.'));
                return $this->redirectToRoute('admin.comments.index');
            }

            // Approve comment
            $approved = $comment->approve();

            if ($approved) {
                // Log activity
                $this->activityLogger->log('comment_approved', [
                    'user_id' => auth()->id(),
                    'comment_id' => $comment->getId(),
                    'comment_author' => $comment->getAuthorName()
                ]);

                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => true,
                        'message' => 'Comment approved successfully.',
                        'new_status' => 'approved'
                    ]);
                }

                $this->flashSuccess(__('Comment approved successfully.'));
                return $this->redirectToRoute('admin.comments.index');
            }

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Failed to approve comment.'
                ]);
            }

            $this->flashError(__('Failed to approve comment.'));
            return $this->redirectToRoute('admin.comments.index');

        } catch (\Exception $e) {
            $this->logError('comments_approve_error', $e, $request);
            
            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'An error occurred while approving the comment.'
                ], 500);
            }
            
            $this->flashError(__('An error occurred while approving the comment.'));
            return $this->redirectToRoute('admin.comments.index');
        }
    }

    /**
     * Mark comment as spam
     */
    public function spam(Request $request, $id)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('comments.moderate')) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'You do not have permission to moderate comments.'
                    ], 403);
                }
                
                return $this->forbidden('You do not have permission to moderate comments.');
            }

            // Find comment
            $comment = Comment::find($id);
            if (!$comment) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Comment not found.'
                    ], 404);
                }
                
                $this->flashError(__('Comment not found.'));
                return $this->redirectToRoute('admin.comments.index');
            }

            // Mark as spam
            $marked = $comment->markAsSpam();

            if ($marked) {
                // Log activity
                $this->activityLogger->log('comment_marked_spam', [
                    'user_id' => auth()->id(),
                    'comment_id' => $comment->getId(),
                    'comment_author' => $comment->getAuthorName()
                ]);

                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => true,
                        'message' => 'Comment marked as spam.',
                        'new_status' => 'spam'
                    ]);
                }

                $this->flashSuccess(__('Comment marked as spam.'));
                return $this->redirectToRoute('admin.comments.index');
            }

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Failed to mark comment as spam.'
                ]);
            }

            $this->flashError(__('Failed to mark comment as spam.'));
            return $this->redirectToRoute('admin.comments.index');

        } catch (\Exception $e) {
            $this->logError('comments_spam_error', $e, $request);
            
            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'An error occurred while marking the comment as spam.'
                ], 500);
            }
            
            $this->flashError(__('An error occurred while marking the comment as spam.'));
            return $this->redirectToRoute('admin.comments.index');
        }
    }

    /**
     * Move comment to trash
     */
    public function trash(Request $request, $id)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('comments.moderate')) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'You do not have permission to moderate comments.'
                    ], 403);
                }
                
                return $this->forbidden('You do not have permission to moderate comments.');
            }

            // Find comment
            $comment = Comment::find($id);
            if (!$comment) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Comment not found.'
                    ], 404);
                }
                
                $this->flashError(__('Comment not found.'));
                return $this->redirectToRoute('admin.comments.index');
            }

            // Move to trash
            $trashed = $comment->trash();

            if ($trashed) {
                // Log activity
                $this->activityLogger->log('comment_trashed', [
                    'user_id' => auth()->id(),
                    'comment_id' => $comment->getId(),
                    'comment_author' => $comment->getAuthorName()
                ]);

                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => true,
                        'message' => 'Comment moved to trash.',
                        'new_status' => 'trash'
                    ]);
                }

                $this->flashSuccess(__('Comment moved to trash.'));
                return $this->redirectToRoute('admin.comments.index');
            }

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Failed to move comment to trash.'
                ]);
            }

            $this->flashError(__('Failed to move comment to trash.'));
            return $this->redirectToRoute('admin.comments.index');

        } catch (\Exception $e) {
            $this->logError('comments_trash_error', $e, $request);
            
            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'An error occurred while moving the comment to trash.'
                ], 500);
            }
            
            $this->flashError(__('An error occurred while moving the comment to trash.'));
            return $this->redirectToRoute('admin.comments.index');
        }
    }

    /**
     * Delete comment permanently
     */
    public function destroy(Request $request, $id)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('comments.delete')) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'You do not have permission to delete comments.'
                    ], 403);
                }
                
                return $this->forbidden('You do not have permission to delete comments.');
            }

            // Find comment
            $comment = Comment::find($id);
            if (!$comment) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Comment not found.'
                    ], 404);
                }
                
                $this->flashError(__('Comment not found.'));
                return $this->redirectToRoute('admin.comments.index');
            }

            // Store comment info for logging
            $commentAuthor = $comment->getAuthorName();

            // Delete comment
            $deleted = $comment->delete();

            if ($deleted) {
                // Log activity
                $this->activityLogger->log('comment_deleted', [
                    'user_id' => auth()->id(),
                    'comment_id' => $id,
                    'comment_author' => $commentAuthor
                ]);

                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => true,
                        'message' => 'Comment deleted successfully.'
                    ]);
                }

                $this->flashSuccess(__('Comment deleted successfully.'));
                return $this->redirectToRoute('admin.comments.index');
            }

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Failed to delete comment.'
                ]);
            }

            $this->flashError(__('Failed to delete comment.'));
            return $this->redirectToRoute('admin.comments.index');

        } catch (\Exception $e) {
            $this->logError('comments_delete_error', $e, $request);
            
            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'An error occurred while deleting the comment.'
                ], 500);
            }
            
            $this->flashError(__('An error occurred while deleting the comment.'));
            return $this->redirectToRoute('admin.comments.index');
        }
    }

    /**
     * Bulk actions on comments
     */
    public function bulkAction(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('comments.moderate')) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'You do not have permission to moderate comments.'
                ], 403);
            }

            $action = $request->request->get('action');
            $commentIds = $request->request->get('comment_ids', []);

            if (empty($commentIds) || !is_array($commentIds)) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'No comments selected.'
                ]);
            }

            $processed = 0;
            $errors = [];

            foreach ($commentIds as $commentId) {
                $comment = Comment::find($commentId);
                if (!$comment) {
                    $errors[] = "Comment {$commentId} not found.";
                    continue;
                }

                $success = false;
                switch ($action) {
                    case 'approve':
                        $success = $comment->approve();
                        break;
                    case 'spam':
                        $success = $comment->markAsSpam();
                        break;
                    case 'trash':
                        $success = $comment->trash();
                        break;
                    case 'delete':
                        if (auth()->can('comments.delete')) {
                            $success = $comment->delete();
                        } else {
                            $errors[] = "No permission to delete comment {$commentId}.";
                            continue 2;
                        }
                        break;
                    default:
                        $errors[] = "Invalid action: {$action}";
                        continue 2;
                }

                if ($success) {
                    $processed++;
                } else {
                    $errors[] = "Failed to {$action} comment {$commentId}.";
                }
            }

            // Log activity
            $this->activityLogger->log('comments_bulk_action', [
                'user_id' => auth()->id(),
                'action' => $action,
                'processed' => $processed,
                'total' => count($commentIds)
            ]);

            $message = "{$processed} comment(s) processed successfully.";
            if (!empty($errors)) {
                $message .= " Errors: " . implode(', ', $errors);
            }

            return new JsonResponse([
                'success' => true,
                'message' => $message,
                'processed' => $processed,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            $this->logError('comments_bulk_action_error', $e, $request);
            
            return new JsonResponse([
                'success' => false,
                'message' => 'An error occurred while processing comments.'
            ], 500);
        }
    }

    /**
     * Log errors
     */
    private function logError($event, \Exception $e, Request $request)
    {
        $logData = [
            'event' => $event,
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'user_id' => auth()->id(),
            'ip_address' => $request->getClientIp(),
            'user_agent' => $request->headers->get('User-Agent'),
            'url' => $request->getUri()
        ];

        error_log(json_encode($logData), 3, storage_path('logs/comments.log'));
        
        if ($this->activityLogger) {
            $this->activityLogger->log($event, $logData);
        }
    }
}
