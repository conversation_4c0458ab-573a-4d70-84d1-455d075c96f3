{% extends "layouts/base.twig" %}

{% block title %}{{ title }} - {{ __('Home') }}{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-3">{{ content.hero_title }}</h1>
                <p class="lead mb-4">{{ content.hero_subtitle }}</p>
                <p class="mb-4">{{ content.hero_description }}</p>
                <div class="d-flex gap-3">
                    <a href="{{ url('/blog') }}" class="btn btn-light btn-lg">
                        <i class="fas fa-book me-2"></i>{{ __('Read Blog') }}
                    </a>
                    <a href="{{ url('/contact') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-envelope me-2"></i>{{ __('Contact Us') }}
                    </a>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <i class="fas fa-cube display-1 opacity-75"></i>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-lg-8 mx-auto">
                <h2 class="display-5 fw-bold mb-3">{{ __('Powerful Features') }}</h2>
                <p class="lead text-muted">{{ __('Everything you need to build amazing websites') }}</p>
            </div>
        </div>
        
        <div class="row g-4">
            {% for feature in content.features %}
            <div class="col-md-6 col-lg-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="{{ feature.icon }} fa-3x text-primary"></i>
                        </div>
                        <h5 class="card-title">{{ feature.title }}</h5>
                        <p class="card-text text-muted">{{ feature.description }}</p>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Recent Posts Section -->
{% if recent_posts %}
<section class="recent-posts-section py-5 bg-light">
    <div class="container">
        <div class="row mb-4">
            <div class="col-lg-8">
                <h2 class="display-6 fw-bold">{{ __('Latest Posts') }}</h2>
                <p class="text-muted">{{ __('Stay updated with our latest content') }}</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="{{ url('/blog') }}" class="btn btn-primary">
                    {{ __('View All Posts') }}
                    <i class="fas fa-arrow-right ms-2"></i>
                </a>
            </div>
        </div>
        
        <div class="row g-4">
            {% for post in recent_posts %}
            <div class="col-md-6 col-lg-4">
                <article class="card h-100 border-0 shadow-sm">
                    {% if post.featured_image %}
                    <img src="{{ post.featured_image }}" class="card-img-top" alt="{{ post.title }}">
                    {% endif %}
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-2">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                {{ post.created_at|date_format('d M Y') }}
                            </small>
                            {% if post.category %}
                            <span class="badge bg-primary ms-auto">{{ post.category.name }}</span>
                            {% endif %}
                        </div>
                        <h5 class="card-title">
                            <a href="{{ url('/blog/' ~ post.slug) }}" class="text-decoration-none">
                                {{ post.title }}
                            </a>
                        </h5>
                        <p class="card-text text-muted">{{ post.excerpt|truncate(120) }}</p>
                    </div>
                    <div class="card-footer bg-transparent border-0">
                        <a href="{{ url('/blog/' ~ post.slug) }}" class="btn btn-outline-primary btn-sm">
                            {{ __('Read More') }}
                            <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </article>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- CTA Section -->
<section class="cta-section py-5 bg-primary text-white">
    <div class="container text-center">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <h2 class="display-6 fw-bold mb-3">{{ __('Ready to Get Started?') }}</h2>
                <p class="lead mb-4">{{ __('Join thousands of users who trust our platform') }}</p>
                <div class="d-flex justify-content-center gap-3">
                    <a href="{{ url('/contact') }}" class="btn btn-light btn-lg">
                        <i class="fas fa-rocket me-2"></i>{{ __('Get Started') }}
                    </a>
                    <a href="{{ url('/about') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-info-circle me-2"></i>{{ __('Learn More') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block styles %}
{{ parent() }}
<style>
.hero-section {
    background: linear-gradient(135deg, var(--bs-primary) 0%, #0056b3 100%);
}

.feature-icon {
    transition: transform 0.3s ease;
}

.card:hover .feature-icon {
    transform: translateY(-5px);
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
</style>
{% endblock %}
