<?php

/**
 * API Routes
 * 
 * RESTful API routes for the CMS
 */

$router = app()->resolve('router');

// API prefix group
$router->group(['prefix' => 'api/v1'], function($router) {
    
    // Authentication
    $router->post('/auth/login', 'CmsPro\Controllers\Api\AuthController@login', 'api.auth.login');
    $router->post('/auth/logout', 'CmsPro\Controllers\Api\AuthController@logout', 'api.auth.logout');
    $router->post('/auth/refresh', 'CmsPro\Controllers\Api\AuthController@refresh', 'api.auth.refresh');
    $router->get('/auth/me', 'CmsPro\Controllers\Api\AuthController@me', 'api.auth.me');

    // Content API
    $router->get('/content', 'CmsPro\Controllers\Api\ContentController@index', 'api.content.index');
    $router->get('/content/{id}', 'CmsPro\Controllers\Api\ContentController@show', 'api.content.show');
    $router->post('/content', 'CmsPro\Controllers\Api\ContentController@store', 'api.content.store');
    $router->put('/content/{id}', 'CmsPro\Controllers\Api\ContentController@update', 'api.content.update');
    $router->delete('/content/{id}', 'CmsPro\Controllers\Api\ContentController@destroy', 'api.content.destroy');

    // Pages API
    $router->get('/pages', 'CmsPro\Controllers\Api\PageController@index', 'api.pages.index');
    $router->get('/pages/{slug}', 'CmsPro\Controllers\Api\PageController@show', 'api.pages.show');
    $router->post('/pages', 'CmsPro\Controllers\Api\PageController@store', 'api.pages.store');
    $router->put('/pages/{id}', 'CmsPro\Controllers\Api\PageController@update', 'api.pages.update');
    $router->delete('/pages/{id}', 'CmsPro\Controllers\Api\PageController@destroy', 'api.pages.destroy');

    // Blog API
    $router->get('/blog', 'CmsPro\Controllers\Api\BlogController@index', 'api.blog.index');
    $router->get('/blog/{slug}', 'CmsPro\Controllers\Api\BlogController@show', 'api.blog.show');
    $router->post('/blog', 'CmsPro\Controllers\Api\BlogController@store', 'api.blog.store');
    $router->put('/blog/{id}', 'CmsPro\Controllers\Api\BlogController@update', 'api.blog.update');
    $router->delete('/blog/{id}', 'CmsPro\Controllers\Api\BlogController@destroy', 'api.blog.destroy');

    // Categories API
    $router->get('/categories', 'CmsPro\Controllers\Api\CategoryController@index', 'api.categories.index');
    $router->get('/categories/{id}', 'CmsPro\Controllers\Api\CategoryController@show', 'api.categories.show');
    $router->post('/categories', 'CmsPro\Controllers\Api\CategoryController@store', 'api.categories.store');
    $router->put('/categories/{id}', 'CmsPro\Controllers\Api\CategoryController@update', 'api.categories.update');
    $router->delete('/categories/{id}', 'CmsPro\Controllers\Api\CategoryController@destroy', 'api.categories.destroy');

    // Dynamic Fields API
    $router->get('/fields', 'CmsPro\Controllers\Api\FieldController@index', 'api.fields.index');
    $router->get('/fields/{id}', 'CmsPro\Controllers\Api\FieldController@show', 'api.fields.show');
    $router->post('/fields', 'CmsPro\Controllers\Api\FieldController@store', 'api.fields.store');
    $router->put('/fields/{id}', 'CmsPro\Controllers\Api\FieldController@update', 'api.fields.update');
    $router->delete('/fields/{id}', 'CmsPro\Controllers\Api\FieldController@destroy', 'api.fields.destroy');

    // Media API
    $router->get('/media', 'CmsPro\Controllers\Api\MediaController@index', 'api.media.index');
    $router->post('/media/upload', 'CmsPro\Controllers\Api\MediaController@upload', 'api.media.upload');
    $router->get('/media/{id}', 'CmsPro\Controllers\Api\MediaController@show', 'api.media.show');
    $router->delete('/media/{id}', 'CmsPro\Controllers\Api\MediaController@destroy', 'api.media.destroy');

    // Users API
    $router->get('/users', 'CmsPro\Controllers\Api\UserController@index', 'api.users.index');
    $router->get('/users/{id}', 'CmsPro\Controllers\Api\UserController@show', 'api.users.show');
    $router->post('/users', 'CmsPro\Controllers\Api\UserController@store', 'api.users.store');
    $router->put('/users/{id}', 'CmsPro\Controllers\Api\UserController@update', 'api.users.update');
    $router->delete('/users/{id}', 'CmsPro\Controllers\Api\UserController@destroy', 'api.users.destroy');

    // Search API
    $router->get('/search', 'CmsPro\Controllers\Api\SearchController@search', 'api.search');
    $router->get('/search/suggestions', 'CmsPro\Controllers\Api\SearchController@suggestions', 'api.search.suggestions');

    // Settings API
    $router->get('/settings', 'CmsPro\Controllers\Api\SettingsController@index', 'api.settings.index');
    $router->put('/settings', 'CmsPro\Controllers\Api\SettingsController@update', 'api.settings.update');

    // Languages API
    $router->get('/languages', 'CmsPro\Controllers\Api\LanguageController@index', 'api.languages.index');
    $router->get('/languages/{code}/translations', 'CmsPro\Controllers\Api\LanguageController@translations', 'api.languages.translations');

    // System API
    $router->get('/system/info', 'CmsPro\Controllers\Api\SystemController@info', 'api.system.info');
    $router->get('/system/health', 'CmsPro\Controllers\Api\SystemController@health', 'api.system.health');

    // Webhooks
    $router->post('/webhooks/{name}', 'CmsPro\Controllers\Api\WebhookController@handle', 'api.webhooks.handle');

    // Form Submissions
    $router->post('/forms/{id}/submit', 'CmsPro\Controllers\Api\FormController@submit', 'api.forms.submit');

    // Analytics
    $router->get('/analytics/stats', 'CmsPro\Controllers\Api\AnalyticsController@stats', 'api.analytics.stats');
    $router->post('/analytics/track', 'CmsPro\Controllers\Api\AnalyticsController@track', 'api.analytics.track');
});

// Public API endpoints (no authentication required)
$router->group(['prefix' => 'api/public'], function($router) {
    
    // Public content
    $router->get('/pages', 'CmsPro\Controllers\Api\Public\PageController@index', 'api.public.pages.index');
    $router->get('/pages/{slug}', 'CmsPro\Controllers\Api\Public\PageController@show', 'api.public.pages.show');
    
    $router->get('/blog', 'CmsPro\Controllers\Api\Public\BlogController@index', 'api.public.blog.index');
    $router->get('/blog/{slug}', 'CmsPro\Controllers\Api\Public\BlogController@show', 'api.public.blog.show');
    
    // Contact form
    $router->post('/contact', 'CmsPro\Controllers\Api\Public\ContactController@submit', 'api.public.contact.submit');
    
    // Newsletter subscription
    $router->post('/newsletter/subscribe', 'CmsPro\Controllers\Api\Public\NewsletterController@subscribe', 'api.public.newsletter.subscribe');
    
    // Search
    $router->get('/search', 'CmsPro\Controllers\Api\Public\SearchController@search', 'api.public.search');
});
