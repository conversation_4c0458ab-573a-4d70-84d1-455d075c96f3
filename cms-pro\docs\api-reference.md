# CMS Pro API Reference

The CMS Pro API provides RESTful endpoints for managing content, users, and system settings. All API endpoints return JSON responses and use standard HTTP status codes.

## Base URL

```
https://your-domain.com/api/v1
```

## Authentication

CMS Pro uses token-based authentication for API access.

### Login

```http
POST /api/v1/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password"
}
```

**Response:**
```json
{
    "success": true,
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
        "id": 1,
        "first_name": "<PERSON>",
        "last_name": "Do<PERSON>",
        "email": "<EMAIL>",
        "role": "administrator"
    }
}
```

### Using the Token

Include the token in the Authorization header for authenticated requests:

```http
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## Error Handling

The API uses standard HTTP status codes and returns error details in JSON format:

```json
{
    "success": false,
    "error": {
        "code": 400,
        "message": "Validation failed",
        "details": {
            "title": ["The title field is required"],
            "email": ["The email format is invalid"]
        }
    }
}
```

### Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## Pagination

List endpoints support pagination with the following parameters:

- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 100)

**Response format:**
```json
{
    "success": true,
    "data": [...],
    "pagination": {
        "current_page": 1,
        "per_page": 20,
        "total": 150,
        "pages": 8
    }
}
```

## Content Management

### Pages

#### List Pages

```http
GET /api/v1/pages?page=1&limit=20&status=published
```

**Parameters:**
- `status` - Filter by status (published, draft, archived)
- `author` - Filter by author ID
- `search` - Search in title and content

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "title": "About Us",
            "slug": "about-us",
            "excerpt": "Learn more about our company...",
            "status": "published",
            "author": {
                "id": 1,
                "name": "John Doe"
            },
            "created_at": "2024-01-15T10:30:00Z",
            "updated_at": "2024-01-15T10:30:00Z"
        }
    ],
    "pagination": {...}
}
```

#### Get Page

```http
GET /api/v1/pages/{id}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 1,
        "title": "About Us",
        "slug": "about-us",
        "content": "<p>Full page content...</p>",
        "excerpt": "Learn more about our company...",
        "status": "published",
        "meta_title": "About Us - Company Name",
        "meta_description": "Learn more about our company and mission",
        "custom_fields": {
            "banner_image": "/uploads/banner.jpg",
            "show_sidebar": true
        },
        "author": {
            "id": 1,
            "name": "John Doe"
        },
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
    }
}
```

#### Create Page

```http
POST /api/v1/pages
Content-Type: application/json
Authorization: Bearer {token}

{
    "title": "New Page",
    "content": "<p>Page content here...</p>",
    "excerpt": "Short description",
    "status": "published",
    "meta_title": "New Page - Site Name",
    "meta_description": "Page description for SEO",
    "custom_fields": {
        "banner_image": "/uploads/new-banner.jpg"
    }
}
```

#### Update Page

```http
PUT /api/v1/pages/{id}
Content-Type: application/json
Authorization: Bearer {token}

{
    "title": "Updated Page Title",
    "content": "<p>Updated content...</p>",
    "status": "published"
}
```

#### Delete Page

```http
DELETE /api/v1/pages/{id}
Authorization: Bearer {token}
```

### Blog Posts

#### List Blog Posts

```http
GET /api/v1/blog?page=1&limit=20&status=published&category=1
```

**Parameters:**
- `status` - Filter by status
- `category` - Filter by category ID
- `tag` - Filter by tag ID
- `author` - Filter by author ID
- `search` - Search in title and content

#### Get Blog Post

```http
GET /api/v1/blog/{id}
```

#### Create Blog Post

```http
POST /api/v1/blog
Content-Type: application/json
Authorization: Bearer {token}

{
    "title": "New Blog Post",
    "content": "<p>Post content...</p>",
    "excerpt": "Post excerpt",
    "status": "published",
    "category_id": 1,
    "tags": [1, 2, 3],
    "featured_image": "/uploads/featured.jpg",
    "meta_title": "Blog Post Title",
    "meta_description": "Post description"
}
```

### Categories

#### List Categories

```http
GET /api/v1/categories
```

#### Create Category

```http
POST /api/v1/categories
Content-Type: application/json
Authorization: Bearer {token}

{
    "name": "Technology",
    "description": "Technology related posts",
    "parent_id": null,
    "color": "#007bff"
}
```

### Tags

#### List Tags

```http
GET /api/v1/tags
```

#### Create Tag

```http
POST /api/v1/tags
Content-Type: application/json
Authorization: Bearer {token}

{
    "name": "PHP",
    "description": "PHP programming language",
    "color": "#777bb4"
}
```

## User Management

### List Users

```http
GET /api/v1/admin/users
Authorization: Bearer {token}
```

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "role": {
                "id": 1,
                "name": "Administrator"
            },
            "status": "active",
            "last_login_at": "2024-01-15T09:30:00Z",
            "created_at": "2024-01-01T00:00:00Z"
        }
    ]
}
```

### Create User

```http
POST /api/v1/admin/users
Content-Type: application/json
Authorization: Bearer {token}

{
    "first_name": "Jane",
    "last_name": "Smith",
    "email": "<EMAIL>",
    "password": "secure_password",
    "role_id": 2,
    "status": "active"
}
```

## Media Management

### Upload File

```http
POST /api/v1/admin/media/upload
Content-Type: multipart/form-data
Authorization: Bearer {token}

file: [binary data]
folder: "images"
```

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 1,
        "filename": "image.jpg",
        "original_name": "my-image.jpg",
        "file_path": "/uploads/images/image.jpg",
        "file_url": "https://domain.com/uploads/images/image.jpg",
        "file_size": 1024000,
        "mime_type": "image/jpeg",
        "created_at": "2024-01-15T10:30:00Z"
    }
}
```

### List Media

```http
GET /api/v1/media?type=image&folder=images
Authorization: Bearer {token}
```

## Search

### Search Content

```http
GET /api/v1/search?q=keyword&type=all&limit=10
```

**Parameters:**
- `q` - Search query
- `type` - Content type (all, pages, blog, categories, tags)
- `limit` - Number of results

**Response:**
```json
{
    "success": true,
    "results": [
        {
            "id": 1,
            "type": "page",
            "title": "About Us",
            "excerpt": "Learn more about our company...",
            "url": "/about-us",
            "relevance": 0.95
        }
    ],
    "total": 15,
    "query": "keyword"
}
```

## Analytics

### Get Overview

```http
GET /api/v1/admin/analytics/overview
Authorization: Bearer {token}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "pages": {
            "total": 25,
            "published": 20,
            "draft": 5,
            "total_views": 15000
        },
        "blog_posts": {
            "total": 50,
            "published": 45,
            "draft": 5,
            "total_views": 25000,
            "total_comments": 150
        },
        "users": {
            "total": 10,
            "active": 8,
            "inactive": 2
        }
    }
}
```

## Settings

### Get Settings

```http
GET /api/v1/admin/settings
Authorization: Bearer {token}
```

### Update Settings

```http
PUT /api/v1/admin/settings
Content-Type: application/json
Authorization: Bearer {token}

{
    "site_name": "My Website",
    "site_description": "Website description",
    "admin_email": "<EMAIL>",
    "timezone": "UTC"
}
```

## Rate Limiting

API requests are rate limited to prevent abuse:

- **Authenticated users**: 1000 requests per hour
- **Unauthenticated users**: 100 requests per hour

Rate limit headers are included in responses:

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## Webhooks

CMS Pro supports webhooks for real-time notifications:

### Configure Webhook

```http
POST /api/v1/admin/webhooks
Content-Type: application/json
Authorization: Bearer {token}

{
    "url": "https://your-app.com/webhook",
    "events": ["page.created", "post.published"],
    "secret": "webhook_secret"
}
```

### Webhook Events

- `page.created`
- `page.updated`
- `page.deleted`
- `post.created`
- `post.published`
- `user.created`
- `comment.created`

## SDK Examples

### JavaScript/Node.js

```javascript
const CmsProAPI = require('cms-pro-sdk');

const api = new CmsProAPI({
    baseURL: 'https://your-domain.com/api/v1',
    token: 'your-api-token'
});

// Get pages
const pages = await api.pages.list({ status: 'published' });

// Create page
const newPage = await api.pages.create({
    title: 'New Page',
    content: '<p>Content here</p>',
    status: 'published'
});
```

### PHP

```php
use CmsPro\SDK\Client;

$client = new Client([
    'base_url' => 'https://your-domain.com/api/v1',
    'token' => 'your-api-token'
]);

// Get pages
$pages = $client->pages()->list(['status' => 'published']);

// Create page
$newPage = $client->pages()->create([
    'title' => 'New Page',
    'content' => '<p>Content here</p>',
    'status' => 'published'
]);
```

## Support

For API support and questions:

- **Documentation**: [docs.cms-pro.com/api](https://docs.cms-pro.com/api)
- **Email**: <EMAIL>
- **Discord**: [Join our Discord](https://discord.gg/cms-pro)
