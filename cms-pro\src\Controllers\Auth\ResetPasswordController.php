<?php

namespace CmsPro\Controllers\Auth;

use CmsPro\Controllers\BaseController;
use CmsPro\Models\User;
use Symfony\Component\HttpFoundation\Request;

/**
 * Reset Password Controller
 * 
 * @package CmsPro\Controllers\Auth
 */
class ResetPasswordController extends BaseController
{
    /**
     * Show reset password form
     */
    public function showResetForm(Request $request, $parameters)
    {
        $this->request = $request;
        $token = $parameters['token'] ?? null;

        // Redirect if already authenticated
        if (auth()->check()) {
            return $this->redirectToRoute('home');
        }

        if (!$token) {
            $this->flashError(__('Invalid reset token.'));
            return $this->redirectToRoute('password.request');
        }

        // Verify token exists and is not expired
        $user = $this->getUserByToken($token);
        
        if (!$user) {
            $this->flashError(__('Invalid or expired reset token.'));
            return $this->redirectToRoute('password.request');
        }

        $data = [
            'title' => __('Reset Password'),
            'meta_description' => __('Reset your password'),
            'token' => $token,
            'email' => $user->getEmail()
        ];

        return $this->view('auth/passwords/reset.twig', $data);
    }

    /**
     * Reset password
     */
    public function reset(Request $request)
    {
        $this->request = $request;

        // Redirect if already authenticated
        if (auth()->check()) {
            return $this->redirectToRoute('home');
        }

        $token = $request->request->get('token');
        $email = $request->request->get('email');
        $password = $request->request->get('password');
        $passwordConfirmation = $request->request->get('password_confirmation');

        // Validate input
        if (!$token || !$email || !$password || !$passwordConfirmation) {
            $this->flashError(__('All fields are required.'));
            return $this->back();
        }

        if ($password !== $passwordConfirmation) {
            $this->flashError(__('Password confirmation does not match.'));
            return $this->back();
        }

        if (strlen($password) < 8) {
            $this->flashError(__('Password must be at least 8 characters long.'));
            return $this->back();
        }

        // Verify token and get user
        $user = $this->getUserByToken($token);
        
        if (!$user || $user->getEmail() !== $email) {
            $this->flashError(__('Invalid or expired reset token.'));
            return $this->redirectToRoute('password.request');
        }

        try {
            // Update password and clear reset token
            $user->update([
                'password' => $password, // Will be hashed in User model
                'password_reset_token' => null,
                'password_reset_expires' => null
            ]);

            // Log activity
            app()->getDatabase()->insert('user_activity_log', [
                'user_id' => $user->getId(),
                'action' => 'password_reset',
                'description' => 'Password reset via email',
                'ip_address' => $request->getClientIp(),
                'user_agent' => $request->headers->get('User-Agent')
            ]);

            $this->flashSuccess(__('Your password has been reset successfully. You can now login with your new password.'));
            
            return $this->redirectToRoute('login');

        } catch (\Exception $e) {
            $this->flashError(__('An error occurred while resetting your password. Please try again.'));
            return $this->back();
        }
    }

    /**
     * Get user by reset token
     */
    private function getUserByToken($token)
    {
        $db = app()->getDatabase();
        
        $userData = $db->selectOne(
            "SELECT * FROM users 
             WHERE password_reset_token = ? 
             AND password_reset_expires > NOW() 
             AND deleted_at IS NULL",
            [$token]
        );

        if ($userData) {
            $user = new User();
            $user->data = $userData;
            return $user;
        }

        return null;
    }
}
