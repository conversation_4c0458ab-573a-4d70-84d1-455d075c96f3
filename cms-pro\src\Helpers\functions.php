<?php

/**
 * Global Helper Functions
 * 
 * Common utility functions used throughout the application
 */

if (!function_exists('app')) {
    /**
     * Get the application instance
     */
    function app($service = null)
    {
        $app = $GLOBALS['app'] ?? null;
        
        if (!$app) {
            throw new RuntimeException('Application not initialized');
        }
        
        return $service ? $app->resolve($service) : $app;
    }
}

if (!function_exists('config')) {
    /**
     * Get configuration value
     */
    function config($key, $default = null)
    {
        return app('config')->get($key, $default);
    }
}

if (!function_exists('auth')) {
    /**
     * Get the auth service
     */
    function auth()
    {
        return app('auth');
    }
}

if (!function_exists('session')) {
    /**
     * Get the session service
     */
    function session()
    {
        return app('session');
    }
}

if (!function_exists('cache')) {
    /**
     * Get the cache service
     */
    function cache()
    {
        return app('cache');
    }
}

if (!function_exists('settings')) {
    /**
     * Get settings value
     */
    function settings($key, $default = null)
    {
        return app('settings')->get($key, $default);
    }
}

if (!function_exists('url')) {
    /**
     * Generate URL
     */
    function url($path = '')
    {
        $baseUrl = rtrim(settings('site_url', 'http://localhost'), '/');
        $path = ltrim($path, '/');
        
        return $baseUrl . ($path ? '/' . $path : '');
    }
}

if (!function_exists('asset')) {
    /**
     * Generate asset URL
     */
    function asset($path)
    {
        return url('assets/' . ltrim($path, '/'));
    }
}

if (!function_exists('storage_path')) {
    /**
     * Get storage path
     */
    function storage_path($path = '')
    {
        $storagePath = app()->getBasePath() . '/storage';
        return $path ? $storagePath . '/' . ltrim($path, '/') : $storagePath;
    }
}

if (!function_exists('public_path')) {
    /**
     * Get public path
     */
    function public_path($path = '')
    {
        $publicPath = app()->getBasePath() . '/public';
        return $path ? $publicPath . '/' . ltrim($path, '/') : $publicPath;
    }
}

if (!function_exists('view_path')) {
    /**
     * Get views path
     */
    function view_path($path = '')
    {
        $viewsPath = app()->getBasePath() . '/views';
        return $path ? $viewsPath . '/' . ltrim($path, '/') : $viewsPath;
    }
}

if (!function_exists('__')) {
    /**
     * Translate text (simple implementation)
     */
    function __($text, $params = [])
    {
        // Simple translation - you can enhance this with proper i18n
        $translations = [
            // Common
            'Home' => 'Ana Sayfa',
            'Contact' => 'İletişim',
            'Search' => 'Ara',
            'Login' => 'Giriş',
            'Logout' => 'Çıkış',
            'Register' => 'Kayıt',
            'Dashboard' => 'Panel',
            'Settings' => 'Ayarlar',
            'Profile' => 'Profil',
            'Save' => 'Kaydet',
            'Cancel' => 'İptal',
            'Delete' => 'Sil',
            'Edit' => 'Düzenle',
            'View' => 'Görüntüle',
            'Create' => 'Oluştur',
            'Update' => 'Güncelle',
            'Back' => 'Geri',
            'Next' => 'İleri',
            'Previous' => 'Önceki',
            'Loading...' => 'Yükleniyor...',
            'Please wait...' => 'Lütfen bekleyin...',
            'Success' => 'Başarılı',
            'Error' => 'Hata',
            'Warning' => 'Uyarı',
            'Info' => 'Bilgi',
            
            // Pages
            'Pages' => 'Sayfalar',
            'New Page' => 'Yeni Sayfa',
            'Edit Page' => 'Sayfa Düzenle',
            'Page Title' => 'Sayfa Başlığı',
            'Page Content' => 'Sayfa İçeriği',
            
            // Blog
            'Blog' => 'Blog',
            'Posts' => 'Yazılar',
            'New Post' => 'Yeni Yazı',
            'Categories' => 'Kategoriler',
            'Tags' => 'Etiketler',
            'Comments' => 'Yorumlar',
            
            // Users
            'Users' => 'Kullanıcılar',
            'User Management' => 'Kullanıcı Yönetimi',
            'Roles' => 'Roller',
            'Permissions' => 'İzinler',
            
            // Media
            'Media' => 'Medya',
            'Upload' => 'Yükle',
            'Images' => 'Resimler',
            'Files' => 'Dosyalar',
            
            // Errors
            'Page Not Found' => 'Sayfa Bulunamadı',
            'Access Denied' => 'Erişim Reddedildi',
            'Server Error' => 'Sunucu Hatası',
        ];
        
        $language = settings('language', 'en');
        
        if ($language === 'tr' && isset($translations[$text])) {
            $text = $translations[$text];
        }
        
        // Replace parameters
        if (!empty($params)) {
            foreach ($params as $key => $value) {
                $text = str_replace(':' . $key, $value, $text);
            }
        }
        
        return $text;
    }
}

if (!function_exists('old')) {
    /**
     * Get old input value
     */
    function old($key, $default = null)
    {
        return session()->getFlashData('old_input')[$key] ?? $default;
    }
}

if (!function_exists('csrf_token')) {
    /**
     * Get CSRF token
     */
    function csrf_token()
    {
        return session()->get('_token');
    }
}

if (!function_exists('csrf_field')) {
    /**
     * Generate CSRF hidden field
     */
    function csrf_field()
    {
        return '<input type="hidden" name="_token" value="' . csrf_token() . '">';
    }
}

if (!function_exists('method_field')) {
    /**
     * Generate method hidden field
     */
    function method_field($method)
    {
        return '<input type="hidden" name="_method" value="' . strtoupper($method) . '">';
    }
}

if (!function_exists('flash')) {
    /**
     * Flash message to session
     */
    function flash($type, $message)
    {
        session()->setFlashData('flash_' . $type, $message);
    }
}

if (!function_exists('get_flash')) {
    /**
     * Get flash message
     */
    function get_flash($type)
    {
        return session()->getFlashData('flash_' . $type);
    }
}

if (!function_exists('dd')) {
    /**
     * Dump and die (for debugging)
     */
    function dd(...$vars)
    {
        foreach ($vars as $var) {
            echo '<pre>';
            var_dump($var);
            echo '</pre>';
        }
        die();
    }
}

if (!function_exists('dump')) {
    /**
     * Dump variable (for debugging)
     */
    function dump(...$vars)
    {
        foreach ($vars as $var) {
            echo '<pre>';
            var_dump($var);
            echo '</pre>';
        }
    }
}

if (!function_exists('str_slug')) {
    /**
     * Generate URL-friendly slug
     */
    function str_slug($string, $separator = '-')
    {
        // Convert to lowercase
        $string = strtolower($string);
        
        // Replace Turkish characters
        $turkish = ['ç', 'ğ', 'ı', 'ö', 'ş', 'ü'];
        $english = ['c', 'g', 'i', 'o', 's', 'u'];
        $string = str_replace($turkish, $english, $string);
        
        // Remove special characters
        $string = preg_replace('/[^a-z0-9\s-]/', '', $string);
        
        // Replace spaces and multiple dashes with single separator
        $string = preg_replace('/[\s-]+/', $separator, $string);
        
        // Trim separators from ends
        return trim($string, $separator);
    }
}

if (!function_exists('str_limit')) {
    /**
     * Limit string length
     */
    function str_limit($string, $limit = 100, $end = '...')
    {
        if (mb_strlen($string) <= $limit) {
            return $string;
        }
        
        return mb_substr($string, 0, $limit) . $end;
    }
}

if (!function_exists('format_bytes')) {
    /**
     * Format bytes to human readable format
     */
    function format_bytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

if (!function_exists('time_ago')) {
    /**
     * Get time ago string
     */
    function time_ago($datetime)
    {
        $time = time() - strtotime($datetime);
        
        if ($time < 60) return 'just now';
        if ($time < 3600) return floor($time/60) . ' minutes ago';
        if ($time < 86400) return floor($time/3600) . ' hours ago';
        if ($time < 2592000) return floor($time/86400) . ' days ago';
        if ($time < 31536000) return floor($time/2592000) . ' months ago';
        
        return floor($time/31536000) . ' years ago';
    }
}

if (!function_exists('is_active_route')) {
    /**
     * Check if route is active
     */
    function is_active_route($route, $class = 'active')
    {
        $currentRoute = $_SERVER['REQUEST_URI'] ?? '';
        return strpos($currentRoute, $route) !== false ? $class : '';
    }
}

if (!function_exists('sanitize_html')) {
    /**
     * Sanitize HTML content
     */
    function sanitize_html($html)
    {
        return app('sanitization')->sanitizeHtml($html);
    }
}

if (!function_exists('log_activity')) {
    /**
     * Log user activity
     */
    function log_activity($action, $data = [])
    {
        app('activity_logger')->log($action, $data);
    }
}
