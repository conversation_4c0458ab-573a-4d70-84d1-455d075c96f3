<?php

namespace CmsPro\Models;

use CmsPro\Core\Database;

/**
 * Field Model
 * 
 * @package CmsPro\Models
 */
class Field
{
    private $db;
    public $data = [];

    public function __construct(Database $db = null)
    {
        $this->db = $db ?: app()->getDatabase();
    }

    /**
     * Find field by ID
     */
    public static function find($id)
    {
        $instance = new static();
        $fieldData = $instance->db->selectOne(
            "SELECT f.*, ft.name as field_type_name, ft.slug as field_type_slug, ft.component 
             FROM fields f 
             INNER JOIN field_types ft ON f.field_type_id = ft.id 
             WHERE f.id = ?",
            [$id]
        );

        if ($fieldData) {
            $instance->data = $fieldData;
            return $instance;
        }

        return null;
    }

    /**
     * Find field by slug and group
     */
    public static function findBySlug($slug, $fieldGroupId)
    {
        $instance = new static();
        $fieldData = $instance->db->selectOne(
            "SELECT f.*, ft.name as field_type_name, ft.slug as field_type_slug, ft.component 
             FROM fields f 
             INNER JOIN field_types ft ON f.field_type_id = ft.id 
             WHERE f.slug = ? AND f.field_group_id = ?",
            [$slug, $fieldGroupId]
        );

        if ($fieldData) {
            $instance->data = $fieldData;
            return $instance;
        }

        return null;
    }

    /**
     * Get fields by group
     */
    public static function getByGroup($fieldGroupId)
    {
        $instance = new static();
        $fieldsData = $instance->db->select(
            "SELECT f.*, ft.name as field_type_name, ft.slug as field_type_slug, ft.component 
             FROM fields f 
             INNER JOIN field_types ft ON f.field_type_id = ft.id 
             WHERE f.field_group_id = ? AND f.is_active = 1 
             ORDER BY f.sort_order, f.name",
            [$fieldGroupId]
        );

        $fields = [];
        foreach ($fieldsData as $fieldData) {
            $field = new static();
            $field->data = $fieldData;
            $fields[] = $field;
        }

        return $fields;
    }

    /**
     * Create new field
     */
    public static function create($data)
    {
        $instance = new static();
        
        // Generate slug if not provided
        if (!isset($data['slug'])) {
            $data['slug'] = strtolower(str_replace(' ', '_', $data['name']));
        }

        // Encode JSON fields
        if (isset($data['validation_rules']) && is_array($data['validation_rules'])) {
            $data['validation_rules'] = json_encode($data['validation_rules']);
        }

        if (isset($data['settings']) && is_array($data['settings'])) {
            $data['settings'] = json_encode($data['settings']);
        }

        $instance->db->insert('fields', $data);
        $fieldId = $instance->db->lastInsertId();

        return static::find($fieldId);
    }

    /**
     * Update field
     */
    public function update($data)
    {
        // Encode JSON fields
        if (isset($data['validation_rules']) && is_array($data['validation_rules'])) {
            $data['validation_rules'] = json_encode($data['validation_rules']);
        }

        if (isset($data['settings']) && is_array($data['settings'])) {
            $data['settings'] = json_encode($data['settings']);
        }

        $this->db->update('fields', $data, 'id = ?', [$this->getId()]);
        
        // Refresh data
        $this->data = array_merge($this->data, $data);
        
        return $this;
    }

    /**
     * Delete field
     */
    public function delete()
    {
        // Delete field values first
        $this->db->delete('field_values', 'field_id = ?', [$this->getId()]);
        
        // Delete field options
        $this->db->delete('field_options', 'field_id = ?', [$this->getId()]);
        
        // Delete the field
        $this->db->delete('fields', 'id = ?', [$this->getId()]);
        
        return true;
    }

    /**
     * Get field options
     */
    public function getOptions()
    {
        $optionsData = $this->db->select(
            "SELECT * FROM field_options WHERE field_id = ? ORDER BY sort_order, label",
            [$this->getId()]
        );

        return $optionsData;
    }

    /**
     * Set field options
     */
    public function setOptions($options)
    {
        // Delete existing options
        $this->db->delete('field_options', 'field_id = ?', [$this->getId()]);

        // Insert new options
        foreach ($options as $index => $option) {
            $this->db->insert('field_options', [
                'field_id' => $this->getId(),
                'label' => $option['label'],
                'value' => $option['value'],
                'is_default' => $option['is_default'] ?? false,
                'sort_order' => $index
            ]);
        }

        return $this;
    }

    /**
     * Get field value for entity
     */
    public function getValue($entityType, $entityId)
    {
        $valueData = $this->db->selectOne(
            "SELECT * FROM field_values WHERE field_id = ? AND entity_type = ? AND entity_id = ?",
            [$this->getId(), $entityType, $entityId]
        );

        if (!$valueData) {
            return $this->getDefaultValue();
        }

        $value = $valueData['value'];
        $fieldType = $this->getFieldTypeSlug();

        // Process value based on field type
        switch ($fieldType) {
            case 'boolean':
                return (bool) $value;
            case 'number':
            case 'range':
                return is_numeric($value) ? (float) $value : $value;
            case 'multiselect':
            case 'checkbox':
            case 'gallery':
                return $value ? json_decode($value, true) : [];
            case 'repeater':
            case 'flexible':
                return $value ? json_decode($value, true) : [];
            default:
                return $value;
        }
    }

    /**
     * Set field value for entity
     */
    public function setValue($entityType, $entityId, $value, $meta = [])
    {
        $fieldType = $this->getFieldTypeSlug();

        // Process value based on field type
        switch ($fieldType) {
            case 'boolean':
                $value = $value ? '1' : '0';
                break;
            case 'multiselect':
            case 'checkbox':
            case 'gallery':
            case 'repeater':
            case 'flexible':
                $value = is_array($value) ? json_encode($value) : $value;
                break;
        }

        // Check if value exists
        $existing = $this->db->selectOne(
            "SELECT id FROM field_values WHERE field_id = ? AND entity_type = ? AND entity_id = ?",
            [$this->getId(), $entityType, $entityId]
        );

        $data = [
            'value' => $value,
            'meta' => json_encode($meta)
        ];

        if ($existing) {
            // Update existing value
            $this->db->update('field_values', $data, 'id = ?', [$existing['id']]);
        } else {
            // Insert new value
            $data['field_id'] = $this->getId();
            $data['entity_type'] = $entityType;
            $data['entity_id'] = $entityId;
            $this->db->insert('field_values', $data);
        }

        return $this;
    }

    /**
     * Get validation rules as array
     */
    public function getValidationRules()
    {
        $rules = $this->data['validation_rules'] ?? '{}';
        return json_decode($rules, true) ?: [];
    }

    /**
     * Get settings as array
     */
    public function getSettings()
    {
        $settings = $this->data['settings'] ?? '{}';
        return json_decode($settings, true) ?: [];
    }

    /**
     * Get default value
     */
    public function getDefaultValue()
    {
        $defaultValue = $this->data['default_value'] ?? null;
        $fieldType = $this->getFieldTypeSlug();

        // Process default value based on field type
        switch ($fieldType) {
            case 'boolean':
                return (bool) $defaultValue;
            case 'number':
            case 'range':
                return is_numeric($defaultValue) ? (float) $defaultValue : null;
            case 'multiselect':
            case 'checkbox':
            case 'gallery':
                return $defaultValue ? json_decode($defaultValue, true) : [];
            default:
                return $defaultValue;
        }
    }

    /**
     * Get field type
     */
    public function getFieldType()
    {
        return FieldType::find($this->getFieldTypeId());
    }

    /**
     * Validate field value
     */
    public function validate($value)
    {
        $rules = $this->getValidationRules();
        $errors = [];

        // Required validation
        if ($this->isRequired() && empty($value)) {
            $errors[] = $this->getLabel() . ' is required.';
        }

        // Type-specific validation
        $fieldType = $this->getFieldTypeSlug();
        
        switch ($fieldType) {
            case 'email':
                if ($value && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $errors[] = $this->getLabel() . ' must be a valid email address.';
                }
                break;
            case 'url':
                if ($value && !filter_var($value, FILTER_VALIDATE_URL)) {
                    $errors[] = $this->getLabel() . ' must be a valid URL.';
                }
                break;
            case 'number':
                if ($value !== null && !is_numeric($value)) {
                    $errors[] = $this->getLabel() . ' must be a number.';
                }
                break;
        }

        // Custom validation rules
        if (isset($rules['min_length']) && strlen($value) < $rules['min_length']) {
            $errors[] = $this->getLabel() . ' must be at least ' . $rules['min_length'] . ' characters.';
        }

        if (isset($rules['max_length']) && strlen($value) > $rules['max_length']) {
            $errors[] = $this->getLabel() . ' must not exceed ' . $rules['max_length'] . ' characters.';
        }

        return $errors;
    }

    // Getters
    public function getId() { return $this->data['id'] ?? null; }
    public function getFieldGroupId() { return $this->data['field_group_id'] ?? null; }
    public function getFieldTypeId() { return $this->data['field_type_id'] ?? null; }
    public function getName() { return $this->data['name'] ?? null; }
    public function getSlug() { return $this->data['slug'] ?? null; }
    public function getLabel() { return $this->data['label'] ?? null; }
    public function getDescription() { return $this->data['description'] ?? null; }
    public function getPlaceholder() { return $this->data['placeholder'] ?? null; }
    public function isRequired() { return (bool) ($this->data['is_required'] ?? false); }
    public function isActive() { return (bool) ($this->data['is_active'] ?? false); }
    public function getSortOrder() { return $this->data['sort_order'] ?? 0; }
    public function getFieldTypeSlug() { return $this->data['field_type_slug'] ?? null; }
    public function getComponent() { return $this->data['component'] ?? null; }
    public function getCreatedAt() { return $this->data['created_at'] ?? null; }
    public function getUpdatedAt() { return $this->data['updated_at'] ?? null; }

    /**
     * Convert to array
     */
    public function toArray()
    {
        $data = $this->data;
        $data['validation_rules'] = $this->getValidationRules();
        $data['settings'] = $this->getSettings();
        $data['options'] = $this->getOptions();
        $data['default_value'] = $this->getDefaultValue();
        return $data;
    }

    /**
     * Magic getter
     */
    public function __get($key)
    {
        return $this->data[$key] ?? null;
    }

    /**
     * Magic isset
     */
    public function __isset($key)
    {
        return isset($this->data[$key]);
    }
}
