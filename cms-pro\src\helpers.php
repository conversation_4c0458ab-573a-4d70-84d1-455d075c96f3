<?php

if (!function_exists('env')) {
    /**
     * Get environment variable value
     */
    function env($key, $default = null)
    {
        $value = getenv($key);
        
        if ($value === false) {
            return $default;
        }
        
        // Convert string booleans
        switch (strtolower($value)) {
            case 'true':
            case '(true)':
                return true;
            case 'false':
            case '(false)':
                return false;
            case 'empty':
            case '(empty)':
                return '';
            case 'null':
            case '(null)':
                return null;
        }
        
        return $value;
    }
}

if (!function_exists('config')) {
    /**
     * Get configuration value
     */
    function config($key, $default = null)
    {
        static $config = null;
        
        if ($config === null) {
            $config = new CmsPro\Core\Config();
        }
        
        return $config->get($key, $default);
    }
}

if (!function_exists('app')) {
    /**
     * Get application instance
     */
    function app($abstract = null)
    {
        $app = CmsPro\Core\Application::getInstance();
        
        if ($abstract === null) {
            return $app;
        }
        
        return $app->make($abstract);
    }
}

if (!function_exists('view')) {
    /**
     * Render view
     */
    function view($template, $data = [])
    {
        return app()->getView()->render($template, $data);
    }
}

if (!function_exists('redirect')) {
    /**
     * Create redirect response
     */
    function redirect($url, $status = 302)
    {
        $response = new Symfony\Component\HttpFoundation\RedirectResponse($url, $status);
        return $response;
    }
}

if (!function_exists('url')) {
    /**
     * Generate URL
     */
    function url($path = '')
    {
        $baseUrl = rtrim(config('app.url'), '/');
        return $baseUrl . '/' . ltrim($path, '/');
    }
}

if (!function_exists('asset')) {
    /**
     * Generate asset URL
     */
    function asset($path)
    {
        return url('assets/' . ltrim($path, '/'));
    }
}

if (!function_exists('route')) {
    /**
     * Generate route URL
     */
    function route($name, $parameters = [])
    {
        return app()->getRouter()->generate($name, $parameters);
    }
}

if (!function_exists('csrf_token')) {
    /**
     * Get CSRF token
     */
    function csrf_token()
    {
        return app()->getSession()->get('_token');
    }
}

if (!function_exists('csrf_field')) {
    /**
     * Generate CSRF field
     */
    function csrf_field()
    {
        $token = csrf_token();
        return '<input type="hidden" name="_token" value="' . $token . '">';
    }
}

if (!function_exists('old')) {
    /**
     * Get old input value
     */
    function old($key, $default = null)
    {
        return app()->getSession()->getFlashBag()->get('old_input')[$key] ?? $default;
    }
}

if (!function_exists('session')) {
    /**
     * Get session value
     */
    function session($key = null, $default = null)
    {
        $session = app()->getSession();
        
        if ($key === null) {
            return $session;
        }
        
        return $session->get($key, $default);
    }
}

if (!function_exists('auth')) {
    /**
     * Get auth instance
     */
    function auth()
    {
        return app()->getAuth();
    }
}

if (!function_exists('user')) {
    /**
     * Get authenticated user
     */
    function user()
    {
        return auth()->user();
    }
}

if (!function_exists('dd')) {
    /**
     * Dump and die
     */
    function dd(...$vars)
    {
        foreach ($vars as $var) {
            var_dump($var);
        }
        die();
    }
}

if (!function_exists('sanitize')) {
    /**
     * Sanitize input
     */
    function sanitize($input)
    {
        if (is_array($input)) {
            return array_map('sanitize', $input);
        }
        
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('slug')) {
    /**
     * Generate URL slug
     */
    function slug($text)
    {
        $slugify = new Cocur\Slugify\Slugify();
        return $slugify->slugify($text);
    }
}

if (!function_exists('storage_path')) {
    /**
     * Get storage path
     */
    function storage_path($path = '')
    {
        return STORAGE_PATH . '/' . ltrim($path, '/');
    }
}

if (!function_exists('public_path')) {
    /**
     * Get public path
     */
    function public_path($path = '')
    {
        return PUBLIC_PATH . '/' . ltrim($path, '/');
    }
}

if (!function_exists('trans')) {
    /**
     * Translate text
     */
    function trans($key, $replace = [], $locale = null)
    {
        return app()->make('translator')->trans($key, $replace, $locale);
    }
}

if (!function_exists('__')) {
    /**
     * Translate text (alias)
     */
    function __($key, $replace = [], $locale = null)
    {
        return trans($key, $replace, $locale);
    }
}
