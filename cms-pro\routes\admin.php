<?php

/**
 * Admin Routes
 * 
 * Backend/Admin routes for the CMS
 */

use CmsPro\Core\Application;

$app = Application::getInstance();
$router = $app->getRouter();

// Admin prefix group
$router->group(['prefix' => 'admin'], function($router) {
    
    // Admin Dashboard
    $router->get('/', 'CmsPro\Controllers\Admin\DashboardController@index', 'admin.dashboard');
    $router->get('/dashboard', 'CmsPro\Controllers\Admin\DashboardController@index', 'admin.dashboard.home');

    // Content Management
    $router->get('/content', 'CmsPro\Controllers\Admin\ContentController@index', 'admin.content.index');
    $router->get('/content/create', 'CmsPro\Controllers\Admin\ContentController@create', 'admin.content.create');
    $router->post('/content', 'CmsPro\Controllers\Admin\ContentController@store', 'admin.content.store');
    $router->get('/content/{id}', 'CmsPro\Controllers\Admin\ContentController@show', 'admin.content.show');
    $router->get('/content/{id}/edit', 'CmsPro\Controllers\Admin\ContentController@edit', 'admin.content.edit');
    $router->put('/content/{id}', 'CmsPro\Controllers\Admin\ContentController@update', 'admin.content.update');
    $router->delete('/content/{id}', 'CmsPro\Controllers\Admin\ContentController@destroy', 'admin.content.destroy');

    // Pages Management
    $router->get('/pages', 'CmsPro\Controllers\Admin\PageController@index', 'admin.pages.index');
    $router->get('/pages/create', 'CmsPro\Controllers\Admin\PageController@create', 'admin.pages.create');
    $router->post('/pages', 'CmsPro\Controllers\Admin\PageController@store', 'admin.pages.store');
    $router->get('/pages/{id}/edit', 'CmsPro\Controllers\Admin\PageController@edit', 'admin.pages.edit');
    $router->put('/pages/{id}', 'CmsPro\Controllers\Admin\PageController@update', 'admin.pages.update');
    $router->delete('/pages/{id}', 'CmsPro\Controllers\Admin\PageController@destroy', 'admin.pages.destroy');
    $router->post('/pages/bulk-action', 'CmsPro\Controllers\Admin\PageController@bulkAction', 'admin.pages.bulk');
    $router->post('/pages/auto-save', 'CmsPro\Controllers\Admin\PageController@autoSave', 'admin.pages.autosave');
    $router->post('/pages/preview', 'CmsPro\Controllers\Admin\PageController@preview', 'admin.pages.preview');
    $router->get('/pages/{id}/revisions/{revisionId}', 'CmsPro\Controllers\Admin\PageController@getRevision', 'admin.pages.revision');
    $router->post('/pages/{id}/revisions/{revisionId}/restore', 'CmsPro\Controllers\Admin\PageController@restoreRevision', 'admin.pages.restore');

    // Blog Management
    $router->get('/blog', 'CmsPro\Controllers\Admin\BlogController@index', 'admin.blog.index');
    $router->get('/blog/create', 'CmsPro\Controllers\Admin\BlogController@create', 'admin.blog.create');
    $router->post('/blog', 'CmsPro\Controllers\Admin\BlogController@store', 'admin.blog.store');
    $router->get('/blog/{id}/edit', 'CmsPro\Controllers\Admin\BlogController@edit', 'admin.blog.edit');
    $router->put('/blog/{id}', 'CmsPro\Controllers\Admin\BlogController@update', 'admin.blog.update');
    $router->delete('/blog/{id}', 'CmsPro\Controllers\Admin\BlogController@destroy', 'admin.blog.destroy');

    // Categories
    $router->get('/categories', 'CmsPro\Controllers\Admin\CategoryController@index', 'admin.categories.index');
    $router->post('/categories', 'CmsPro\Controllers\Admin\CategoryController@store', 'admin.categories.store');
    $router->put('/categories/{id}', 'CmsPro\Controllers\Admin\CategoryController@update', 'admin.categories.update');
    $router->delete('/categories/{id}', 'CmsPro\Controllers\Admin\CategoryController@destroy', 'admin.categories.destroy');

    // Dynamic Fields Management
    $router->get('/fields', 'CmsPro\Controllers\Admin\FieldController@index', 'admin.fields.index');
    $router->get('/fields/create', 'CmsPro\Controllers\Admin\FieldController@create', 'admin.fields.create');
    $router->post('/fields', 'CmsPro\Controllers\Admin\FieldController@store', 'admin.fields.store');
    $router->get('/fields/{id}/edit', 'CmsPro\Controllers\Admin\FieldController@edit', 'admin.fields.edit');
    $router->put('/fields/{id}', 'CmsPro\Controllers\Admin\FieldController@update', 'admin.fields.update');
    $router->delete('/fields/{id}', 'CmsPro\Controllers\Admin\FieldController@destroy', 'admin.fields.destroy');

    // Field Groups
    $router->get('/field-groups', 'CmsPro\Controllers\Admin\FieldGroupController@index', 'admin.field-groups.index');
    $router->post('/field-groups', 'CmsPro\Controllers\Admin\FieldGroupController@store', 'admin.field-groups.store');
    $router->put('/field-groups/{id}', 'CmsPro\Controllers\Admin\FieldGroupController@update', 'admin.field-groups.update');
    $router->delete('/field-groups/{id}', 'CmsPro\Controllers\Admin\FieldGroupController@destroy', 'admin.field-groups.destroy');

    // Media Management
    $router->get('/media', 'CmsPro\Controllers\Admin\MediaController@index', 'admin.media.index');
    $router->post('/media/upload', 'CmsPro\Controllers\Admin\MediaController@upload', 'admin.media.upload');
    $router->delete('/media/{id}', 'CmsPro\Controllers\Admin\MediaController@destroy', 'admin.media.destroy');
    $router->post('/media/{id}/crop', 'CmsPro\Controllers\Admin\MediaController@crop', 'admin.media.crop');

    // User Management
    $router->get('/users', 'CmsPro\Controllers\Admin\UserController@index', 'admin.users.index');
    $router->get('/users/create', 'CmsPro\Controllers\Admin\UserController@create', 'admin.users.create');
    $router->post('/users', 'CmsPro\Controllers\Admin\UserController@store', 'admin.users.store');
    $router->get('/users/{id}/edit', 'CmsPro\Controllers\Admin\UserController@edit', 'admin.users.edit');
    $router->put('/users/{id}', 'CmsPro\Controllers\Admin\UserController@update', 'admin.users.update');
    $router->delete('/users/{id}', 'CmsPro\Controllers\Admin\UserController@destroy', 'admin.users.destroy');

    // Settings
    $router->get('/settings', 'CmsPro\Controllers\Admin\SettingsController@index', 'admin.settings.index');
    $router->post('/settings', 'CmsPro\Controllers\Admin\SettingsController@update', 'admin.settings.update');

    // Themes
    $router->get('/themes', 'CmsPro\Controllers\Admin\ThemeController@index', 'admin.themes.index');
    $router->post('/themes/{theme}/activate', 'CmsPro\Controllers\Admin\ThemeController@activate', 'admin.themes.activate');
    $router->get('/themes/customize', 'CmsPro\Controllers\Admin\ThemeController@customize', 'admin.themes.customize');
    $router->post('/themes/customize', 'CmsPro\Controllers\Admin\ThemeController@saveCustomization', 'admin.themes.customize.save');

    // Languages
    $router->get('/languages', 'CmsPro\Controllers\Admin\LanguageController@index', 'admin.languages.index');
    $router->post('/languages', 'CmsPro\Controllers\Admin\LanguageController@store', 'admin.languages.store');
    $router->get('/languages/{id}/translations', 'CmsPro\Controllers\Admin\LanguageController@translations', 'admin.languages.translations');
    $router->post('/languages/{id}/translations', 'CmsPro\Controllers\Admin\LanguageController@saveTranslations', 'admin.languages.translations.save');

    // Backups
    $router->get('/backups', 'CmsPro\Controllers\Admin\BackupController@index', 'admin.backups.index');
    $router->post('/backups/create', 'CmsPro\Controllers\Admin\BackupController@create', 'admin.backups.create');
    $router->post('/backups/{id}/restore', 'CmsPro\Controllers\Admin\BackupController@restore', 'admin.backups.restore');
    $router->delete('/backups/{id}', 'CmsPro\Controllers\Admin\BackupController@destroy', 'admin.backups.destroy');

    // System Info
    $router->get('/system', 'CmsPro\Controllers\Admin\SystemController@index', 'admin.system.index');
    $router->get('/system/logs', 'CmsPro\Controllers\Admin\SystemController@logs', 'admin.system.logs');
    $router->post('/system/clear-cache', 'CmsPro\Controllers\Admin\SystemController@clearCache', 'admin.system.clear-cache');

    // Profile
    $router->get('/profile', 'CmsPro\Controllers\Admin\ProfileController@index', 'admin.profile.index');
    $router->put('/profile', 'CmsPro\Controllers\Admin\ProfileController@update', 'admin.profile.update');
    $router->post('/profile/2fa', 'CmsPro\Controllers\Admin\ProfileController@enable2FA', 'admin.profile.2fa.enable');
    $router->delete('/profile/2fa', 'CmsPro\Controllers\Admin\ProfileController@disable2FA', 'admin.profile.2fa.disable');
});

// Admin Authentication (separate from frontend auth)
$router->get('/admin/login', 'CmsPro\Controllers\Admin\Auth\LoginController@showLoginForm', 'admin.login');
$router->post('/admin/login', 'CmsPro\Controllers\Admin\Auth\LoginController@login', 'admin.login.submit');
$router->post('/admin/logout', 'CmsPro\Controllers\Admin\Auth\LoginController@logout', 'admin.logout');
