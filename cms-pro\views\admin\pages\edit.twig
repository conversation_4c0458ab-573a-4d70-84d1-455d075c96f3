{% extends "admin/pages/create.twig" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="page-form-container">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ title }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ url('/admin/pages') }}">{{ __('Pages') }}</a>
                    </li>
                    <li class="breadcrumb-item active">{{ __('Edit') }}</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <a href="{{ page.getUrl() }}" class="btn btn-outline-info" target="_blank">
                <i class="fas fa-external-link-alt me-2"></i>{{ __('View Page') }}
            </a>
            <a href="{{ url('/admin/pages') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>{{ __('Back to Pages') }}
            </a>
        </div>
    </div>

    <!-- Page Info Bar -->
    <div class="alert alert-info d-flex align-items-center mb-4">
        <div class="flex-grow-1">
            <div class="d-flex align-items-center">
                <i class="fas fa-info-circle me-2"></i>
                <div>
                    <strong>{{ __('Page ID') }}:</strong> {{ page.id }} |
                    <strong>{{ __('Created') }}:</strong> {{ page.created_at|date('M j, Y H:i') }} |
                    <strong>{{ __('Last Modified') }}:</strong> {{ page.updated_at|date('M j, Y H:i') }}
                    {% if page.author %}
                    | <strong>{{ __('Author') }}:</strong> {{ page.author.first_name }} {{ page.author.last_name }}
                    {% endif %}
                </div>
            </div>
        </div>
        {% if page.revisions and page.revisions|length > 0 %}
        <div>
            <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#revisionsModal">
                <i class="fas fa-history me-1"></i>{{ __('Revisions') }} ({{ page.revisions|length }})
            </button>
        </div>
        {% endif %}
    </div>

    <form method="POST" action="{{ url('/admin/pages/' ~ page.id) }}" class="page-form" id="page-form" novalidate>
        {{ csrf_field() | raw }}
        <input type="hidden" name="_method" value="PUT">
        
        <div class="row">
            <!-- Main Content Column -->
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-edit me-2"></i>{{ __('Page Content') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Title -->
                        <div class="mb-3">
                            <label for="title" class="form-label">
                                {{ __('Title') }} <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control {{ errors.title ? 'is-invalid' : '' }}" 
                                   id="title" 
                                   name="title" 
                                   value="{{ old('title', page.title) }}" 
                                   required
                                   maxlength="255"
                                   placeholder="{{ __('Enter page title...') }}">
                            {% if errors.title %}
                                <div class="invalid-feedback">{{ errors.title }}</div>
                            {% endif %}
                        </div>

                        <!-- Slug -->
                        <div class="mb-3">
                            <label for="slug" class="form-label">{{ __('URL Slug') }}</label>
                            <div class="input-group">
                                <span class="input-group-text">{{ url('/') }}/</span>
                                <input type="text" 
                                       class="form-control {{ errors.slug ? 'is-invalid' : '' }}" 
                                       id="slug" 
                                       name="slug" 
                                       value="{{ old('slug', page.slug) }}"
                                       maxlength="255"
                                       placeholder="{{ __('auto-generated') }}">
                                <button type="button" class="btn btn-outline-secondary" id="generate-slug">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                            {% if errors.slug %}
                                <div class="invalid-feedback">{{ errors.slug }}</div>
                            {% endif %}
                            <div class="form-text">
                                {{ __('Current URL') }}: <a href="{{ page.getFullUrl() }}" target="_blank">{{ page.getFullUrl() }}</a>
                            </div>
                        </div>

                        <!-- Content -->
                        <div class="mb-3">
                            <label for="content" class="form-label">
                                {{ __('Content') }} <span class="text-danger">*</span>
                            </label>
                            
                            {% include 'admin/fields/wysiwyg-form.twig' with {
                                field: {
                                    name: 'content',
                                    label: 'Content',
                                    value: old('content', page.content),
                                    required: true,
                                    height: 500,
                                    editor_type: 'advanced'
                                },
                                errors: errors
                            } %}
                        </div>

                        <!-- Excerpt -->
                        <div class="mb-3">
                            <label for="excerpt" class="form-label">{{ __('Excerpt') }}</label>
                            <textarea class="form-control {{ errors.excerpt ? 'is-invalid' : '' }}" 
                                      id="excerpt" 
                                      name="excerpt" 
                                      rows="3"
                                      maxlength="500"
                                      placeholder="{{ __('Optional short description...') }}">{{ old('excerpt', page.excerpt) }}</textarea>
                            {% if errors.excerpt %}
                                <div class="invalid-feedback">{{ errors.excerpt }}</div>
                            {% endif %}
                            <div class="form-text">
                                {{ __('Brief description for search engines and previews') }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEO Settings -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-success">
                            <i class="fas fa-search me-2"></i>{{ __('SEO Settings') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Meta Title -->
                        <div class="mb-3">
                            <label for="meta_title" class="form-label">{{ __('Meta Title') }}</label>
                            <input type="text" 
                                   class="form-control {{ errors.meta_title ? 'is-invalid' : '' }}" 
                                   id="meta_title" 
                                   name="meta_title" 
                                   value="{{ old('meta_title', page.meta_title) }}"
                                   maxlength="255"
                                   placeholder="{{ __('Leave empty to use page title') }}">
                            {% if errors.meta_title %}
                                <div class="invalid-feedback">{{ errors.meta_title }}</div>
                            {% endif %}
                            <div class="form-text">
                                <span class="meta-title-length">{{ page.meta_title|length }}</span>/60 {{ __('characters (recommended)') }}
                            </div>
                        </div>

                        <!-- Meta Description -->
                        <div class="mb-3">
                            <label for="meta_description" class="form-label">{{ __('Meta Description') }}</label>
                            <textarea class="form-control {{ errors.meta_description ? 'is-invalid' : '' }}" 
                                      id="meta_description" 
                                      name="meta_description" 
                                      rows="3"
                                      maxlength="500"
                                      placeholder="{{ __('Brief description for search engines...') }}">{{ old('meta_description', page.meta_description) }}</textarea>
                            {% if errors.meta_description %}
                                <div class="invalid-feedback">{{ errors.meta_description }}</div>
                            {% endif %}
                            <div class="form-text">
                                <span class="meta-description-length">{{ page.meta_description|length }}</span>/160 {{ __('characters (recommended)') }}
                            </div>
                        </div>

                        <!-- Meta Keywords -->
                        <div class="mb-3">
                            <label for="meta_keywords" class="form-label">{{ __('Meta Keywords') }}</label>
                            <input type="text" 
                                   class="form-control {{ errors.meta_keywords ? 'is-invalid' : '' }}" 
                                   id="meta_keywords" 
                                   name="meta_keywords" 
                                   value="{{ old('meta_keywords', page.meta_keywords) }}"
                                   maxlength="255"
                                   placeholder="{{ __('keyword1, keyword2, keyword3') }}">
                            {% if errors.meta_keywords %}
                                <div class="invalid-feedback">{{ errors.meta_keywords }}</div>
                            {% endif %}
                            <div class="form-text">
                                {{ __('Comma-separated keywords (optional)') }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Custom Fields -->
                {% if custom_fields and custom_fields|length > 0 %}
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-info">
                            <i class="fas fa-cogs me-2"></i>{{ __('Custom Fields') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        {% for field in custom_fields %}
                            {% include 'admin/fields/' ~ field.type ~ '-form.twig' with {
                                field: field,
                                value: old('custom_fields.' ~ field.name, page.custom_fields[field.name] ?? null),
                                errors: errors
                            } %}
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Sidebar Column -->
            <div class="col-lg-4">
                <!-- Publish Settings -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-paper-plane me-2"></i>{{ __('Publish Settings') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Status -->
                        <div class="mb-3">
                            <label for="status" class="form-label">{{ __('Status') }}</label>
                            <select class="form-select {{ errors.status ? 'is-invalid' : '' }}" 
                                    id="status" 
                                    name="status" 
                                    required>
                                {% for key, label in statuses %}
                                <option value="{{ key }}" {{ old('status', page.status) == key ? 'selected' : '' }}>
                                    {{ label }}
                                </option>
                                {% endfor %}
                            </select>
                            {% if errors.status %}
                                <div class="invalid-feedback">{{ errors.status }}</div>
                            {% endif %}
                        </div>

                        <!-- Published Date -->
                        <div class="mb-3" id="published-date-group">
                            <label for="published_at" class="form-label">{{ __('Published Date') }}</label>
                            <input type="datetime-local" 
                                   class="form-control {{ errors.published_at ? 'is-invalid' : '' }}" 
                                   id="published_at" 
                                   name="published_at" 
                                   value="{{ old('published_at', page.published_at ? page.published_at.format('Y-m-d\\TH:i') : '') }}">
                            {% if errors.published_at %}
                                <div class="invalid-feedback">{{ errors.published_at }}</div>
                            {% endif %}
                        </div>

                        <!-- Scheduled Date -->
                        <div class="mb-3" id="scheduled-date-group" style="display: none;">
                            <label for="scheduled_at" class="form-label">{{ __('Scheduled Date') }}</label>
                            <input type="datetime-local" 
                                   class="form-control {{ errors.scheduled_at ? 'is-invalid' : '' }}" 
                                   id="scheduled_at" 
                                   name="scheduled_at" 
                                   value="{{ old('scheduled_at', page.scheduled_at ? page.scheduled_at.format('Y-m-d\\TH:i') : '') }}">
                            {% if errors.scheduled_at %}
                                <div class="invalid-feedback">{{ errors.scheduled_at }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Page Settings -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-warning">
                            <i class="fas fa-cog me-2"></i>{{ __('Page Settings') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Template -->
                        <div class="mb-3">
                            <label for="template" class="form-label">{{ __('Template') }}</label>
                            <select class="form-select {{ errors.template ? 'is-invalid' : '' }}" 
                                    id="template" 
                                    name="template">
                                {% for key, label in templates %}
                                <option value="{{ key }}" {{ old('template', page.template) == key ? 'selected' : '' }}>
                                    {{ label }}
                                </option>
                                {% endfor %}
                            </select>
                            {% if errors.template %}
                                <div class="invalid-feedback">{{ errors.template }}</div>
                            {% endif %}
                        </div>

                        <!-- Parent Page -->
                        <div class="mb-3">
                            <label for="parent_id" class="form-label">{{ __('Parent Page') }}</label>
                            <select class="form-select {{ errors.parent_id ? 'is-invalid' : '' }}" 
                                    id="parent_id" 
                                    name="parent_id">
                                <option value="">{{ __('No Parent (Root Page)') }}</option>
                                {% for parentPage in parent_pages %}
                                <option value="{{ parentPage.id }}" {{ old('parent_id', page.parent_id) == parentPage.id ? 'selected' : '' }}>
                                    {{ parentPage.title }}
                                </option>
                                {% endfor %}
                            </select>
                            {% if errors.parent_id %}
                                <div class="invalid-feedback">{{ errors.parent_id }}</div>
                            {% endif %}
                        </div>

                        <!-- Featured Image -->
                        <div class="mb-3">
                            <label for="featured_image" class="form-label">{{ __('Featured Image') }}</label>
                            <div class="input-group">
                                <input type="text" 
                                       class="form-control {{ errors.featured_image ? 'is-invalid' : '' }}" 
                                       id="featured_image" 
                                       name="featured_image" 
                                       value="{{ old('featured_image', page.featured_image) }}"
                                       placeholder="{{ __('Image URL or path') }}">
                                <button type="button" class="btn btn-outline-secondary" id="browse-image">
                                    <i class="fas fa-folder-open"></i>
                                </button>
                            </div>
                            {% if errors.featured_image %}
                                <div class="invalid-feedback">{{ errors.featured_image }}</div>
                            {% endif %}
                            {% if page.featured_image %}
                            <div class="mt-2">
                                <img src="{{ asset(page.featured_image) }}" alt="Featured Image" class="img-thumbnail" style="max-width: 150px;">
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" name="action" value="save" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ __('Update Page') }}
                            </button>
                            
                            <div class="btn-group">
                                <button type="submit" name="action" value="save_and_continue" class="btn btn-outline-primary">
                                    <i class="fas fa-save me-2"></i>{{ __('Save & Continue') }}
                                </button>
                                <button type="submit" name="action" value="save_and_new" class="btn btn-outline-primary">
                                    <i class="fas fa-plus me-2"></i>{{ __('Save & New') }}
                                </button>
                            </div>
                            
                            <button type="button" class="btn btn-outline-secondary" id="preview-page">
                                <i class="fas fa-eye me-2"></i>{{ __('Preview') }}
                            </button>
                            
                            {% if auth().can('pages.delete') %}
                            <hr>
                            <button type="button" class="btn btn-outline-danger" id="delete-page" 
                                    data-page-id="{{ page.id }}" data-page-title="{{ page.title }}">
                                <i class="fas fa-trash me-2"></i>{{ __('Move to Trash') }}
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Revisions Modal -->
{% if page.revisions and page.revisions|length > 0 %}
<div class="modal fade" id="revisionsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Page Revisions') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>{{ __('Date') }}</th>
                                <th>{{ __('User') }}</th>
                                <th>{{ __('Changes') }}</th>
                                <th>{{ __('Actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for revision in page.revisions %}
                            <tr>
                                <td>{{ revision.created_at|date('M j, Y H:i') }}</td>
                                <td>{{ revision.user ? revision.user.first_name ~ ' ' ~ revision.user.last_name : 'System' }}</td>
                                <td>
                                    {% if revision.title != page.title %}
                                        <span class="badge bg-info">Title</span>
                                    {% endif %}
                                    {% if revision.content != page.content %}
                                        <span class="badge bg-primary">Content</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-primary view-revision" 
                                            data-revision-id="{{ revision.id }}">
                                        {{ __('View') }}
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Confirm Delete') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>{{ __('Are you sure you want to move this page to trash?') }}</p>
                <p><strong class="page-title-placeholder">{{ page.title }}</strong></p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {{ __('This action can be undone from the trash.') }}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ __('Cancel') }}
                </button>
                <button type="button" class="btn btn-danger" id="confirmDelete">
                    <i class="fas fa-trash me-2"></i>{{ __('Move to Trash') }}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
// Extend PageForm for edit functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize base form functionality
    window.pageForm = new PageForm();

    // Add edit-specific functionality
    const editEnhancements = {
        init() {
            this.setupDeleteButton();
            this.setupRevisionViewer();
        },

        setupDeleteButton() {
            const deleteButton = document.getElementById('delete-page');
            if (deleteButton) {
                deleteButton.addEventListener('click', () => {
                    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
                    modal.show();
                });
            }

            const confirmDelete = document.getElementById('confirmDelete');
            if (confirmDelete) {
                confirmDelete.addEventListener('click', async () => {
                    try {
                        const response = await fetch(`/admin/pages/{{ page.id }}`, {
                            method: 'DELETE',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest',
                                'X-CSRF-TOKEN': this.getCsrfToken()
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            window.pageForm.showNotification(result.message, 'success');
                            setTimeout(() => {
                                window.location.href = '/admin/pages';
                            }, 1000);
                        } else {
                            window.pageForm.showNotification(result.message, 'error');
                        }

                    } catch (error) {
                        console.error('Delete error:', error);
                        window.pageForm.showNotification('An error occurred while deleting the page.', 'error');
                    }
                });
            }
        },

        setupRevisionViewer() {
            const revisionButtons = document.querySelectorAll('.view-revision');
            revisionButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const revisionId = button.dataset.revisionId;
                    this.viewRevision(revisionId);
                });
            });
        },

        async viewRevision(revisionId) {
            try {
                const response = await fetch(`/admin/pages/{{ page.id }}/revisions/${revisionId}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': this.getCsrfToken()
                    }
                });

                const revision = await response.json();

                if (revision.success) {
                    this.showRevisionModal(revision.data);
                } else {
                    window.pageForm.showNotification('Failed to load revision', 'error');
                }

            } catch (error) {
                console.error('Revision load error:', error);
                window.pageForm.showNotification('Failed to load revision', 'error');
            }
        },

        showRevisionModal(revision) {
            // Create revision comparison modal
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Revision from ${new Date(revision.created_at).toLocaleString()}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Current Version</h6>
                                    <div class="border p-3 bg-light">
                                        <strong>Title:</strong> ${document.getElementById('title').value}<br>
                                        <strong>Content:</strong> <div class="mt-2">${document.getElementById('content').value}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>Revision</h6>
                                    <div class="border p-3 bg-warning bg-opacity-10">
                                        <strong>Title:</strong> ${revision.title}<br>
                                        <strong>Content:</strong> <div class="mt-2">${revision.content}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-warning" onclick="editEnhancements.restoreRevision('${revision.id}')">
                                Restore This Revision
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            // Clean up when modal is hidden
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        },

        async restoreRevision(revisionId) {
            if (!confirm('Are you sure you want to restore this revision? Current changes will be lost.')) {
                return;
            }

            try {
                const response = await fetch(`/admin/pages/{{ page.id }}/revisions/${revisionId}/restore`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': this.getCsrfToken()
                    }
                });

                const result = await response.json();

                if (result.success) {
                    window.pageForm.showNotification('Revision restored successfully', 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    window.pageForm.showNotification(result.message || 'Failed to restore revision', 'error');
                }

            } catch (error) {
                console.error('Restore error:', error);
                window.pageForm.showNotification('Failed to restore revision', 'error');
            }
        },

        getCsrfToken() {
            return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
        }
    };

    // Initialize edit enhancements
    editEnhancements.init();

    // Make available globally for modal callbacks
    window.editEnhancements = editEnhancements;
});
</script>

<style>
/* Edit-specific styles */
.page-form-container .alert-info {
    font-size: 0.875rem;
}

.page-form-container .breadcrumb {
    font-size: 0.875rem;
    margin-bottom: 0;
}

.page-form-container .breadcrumb-item + .breadcrumb-item::before {
    content: "›";
}

.img-thumbnail {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.25rem;
}

/* Revision modal styles */
.modal-xl .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

.bg-warning.bg-opacity-10 {
    background-color: rgba(255, 193, 7, 0.1) !important;
}

/* Dark mode support */
[data-bs-theme="dark"] .alert-info {
    background-color: #1e3a5f;
    border-color: #2c5aa0;
    color: #9ec5fe;
}

[data-bs-theme="dark"] .img-thumbnail {
    border-color: #495057;
    background-color: #2d3748;
}

[data-bs-theme="dark"] .bg-light {
    background-color: #2d3748 !important;
    color: #e2e8f0;
}
</style>
{% endblock %}
