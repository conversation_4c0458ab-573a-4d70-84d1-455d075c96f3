<?php

namespace CmsPro\Services;

use PragmaRX\Google2FA\Google2FA;
use CmsPro\Models\User;

/**
 * Two-Factor Authentication Service
 * 
 * @package CmsPro\Services
 */
class TwoFactorService
{
    private $google2fa;

    public function __construct()
    {
        $this->google2fa = new Google2FA();
    }

    /**
     * Generate secret key for user
     */
    public function generateSecretKey()
    {
        return $this->google2fa->generateSecretKey();
    }

    /**
     * Get QR code URL
     */
    public function getQRCodeUrl(User $user, $secret)
    {
        $companyName = config('app.name', 'CMS Pro');
        $companyEmail = $user->getEmail();
        
        return $this->google2fa->getQRCodeUrl(
            $companyName,
            $companyEmail,
            $secret
        );
    }

    /**
     * Verify TOTP code
     */
    public function verifyCode($secret, $code, $window = 1)
    {
        return $this->google2fa->verifyKey($secret, $code, $window);
    }

    /**
     * Enable 2FA for user
     */
    public function enableTwoFactor(User $user, $code)
    {
        $secret = $user->two_factor_secret;
        
        if (!$secret) {
            throw new \RuntimeException('No secret key found for user.');
        }

        if (!$this->verifyCode($secret, $code)) {
            throw new \RuntimeException('Invalid verification code.');
        }

        // Generate backup codes
        $backupCodes = $this->generateBackupCodes();

        $user->update([
            'two_factor_enabled' => true,
            'two_factor_recovery_codes' => json_encode($backupCodes)
        ]);

        return $backupCodes;
    }

    /**
     * Disable 2FA for user
     */
    public function disableTwoFactor(User $user)
    {
        $user->update([
            'two_factor_enabled' => false,
            'two_factor_secret' => null,
            'two_factor_recovery_codes' => null
        ]);
    }

    /**
     * Setup 2FA for user (generate secret)
     */
    public function setupTwoFactor(User $user)
    {
        $secret = $this->generateSecretKey();
        
        $user->update([
            'two_factor_secret' => $secret
        ]);

        return [
            'secret' => $secret,
            'qr_code_url' => $this->getQRCodeUrl($user, $secret)
        ];
    }

    /**
     * Verify 2FA code during login
     */
    public function verifyLoginCode(User $user, $code)
    {
        if (!$user->isTwoFactorEnabled()) {
            return true; // 2FA not enabled
        }

        $secret = $user->two_factor_secret;
        
        // First try TOTP code
        if ($this->verifyCode($secret, $code)) {
            return true;
        }

        // Then try backup codes
        return $this->verifyBackupCode($user, $code);
    }

    /**
     * Generate backup codes
     */
    public function generateBackupCodes($count = 8)
    {
        $codes = [];
        
        for ($i = 0; $i < $count; $i++) {
            $codes[] = strtoupper(substr(bin2hex(random_bytes(4)), 0, 8));
        }
        
        return $codes;
    }

    /**
     * Verify backup code
     */
    public function verifyBackupCode(User $user, $code)
    {
        $backupCodes = json_decode($user->two_factor_recovery_codes, true);
        
        if (!$backupCodes || !is_array($backupCodes)) {
            return false;
        }

        $code = strtoupper($code);
        
        if (in_array($code, $backupCodes)) {
            // Remove used backup code
            $backupCodes = array_filter($backupCodes, function($backupCode) use ($code) {
                return $backupCode !== $code;
            });
            
            $user->update([
                'two_factor_recovery_codes' => json_encode(array_values($backupCodes))
            ]);
            
            return true;
        }
        
        return false;
    }

    /**
     * Regenerate backup codes
     */
    public function regenerateBackupCodes(User $user)
    {
        if (!$user->isTwoFactorEnabled()) {
            throw new \RuntimeException('Two-factor authentication is not enabled.');
        }

        $backupCodes = $this->generateBackupCodes();
        
        $user->update([
            'two_factor_recovery_codes' => json_encode($backupCodes)
        ]);
        
        return $backupCodes;
    }

    /**
     * Get remaining backup codes count
     */
    public function getRemainingBackupCodesCount(User $user)
    {
        $backupCodes = json_decode($user->two_factor_recovery_codes, true);
        
        return $backupCodes ? count($backupCodes) : 0;
    }

    /**
     * Check if user needs to verify 2FA
     */
    public function needsVerification(User $user)
    {
        if (!$user->isTwoFactorEnabled()) {
            return false;
        }

        // Check if user has already verified 2FA in this session
        return !session()->has('2fa_verified_' . $user->getId());
    }

    /**
     * Mark 2FA as verified for current session
     */
    public function markAsVerified(User $user)
    {
        session()->put('2fa_verified_' . $user->getId(), true);
    }

    /**
     * Clear 2FA verification for current session
     */
    public function clearVerification(User $user)
    {
        session()->forget('2fa_verified_' . $user->getId());
    }

    /**
     * Get 2FA status for user
     */
    public function getStatus(User $user)
    {
        return [
            'enabled' => $user->isTwoFactorEnabled(),
            'has_secret' => !empty($user->two_factor_secret),
            'backup_codes_count' => $this->getRemainingBackupCodesCount($user),
            'needs_verification' => $this->needsVerification($user)
        ];
    }
}
