<?php

namespace CmsPro\Controllers\Frontend;

use CmsPro\Controllers\BaseController;
use Symfony\Component\HttpFoundation\Request;

/**
 * Home Controller
 * 
 * @package CmsPro\Controllers\Frontend
 */
class HomeController extends BaseController
{
    /**
     * Display the home page
     */
    public function index(Request $request)
    {
        $this->request = $request;
        
        // Get homepage content
        $data = [
            'title' => config('app.name'),
            'meta_description' => 'Welcome to ' . config('app.name'),
            'content' => $this->getHomePageContent(),
            'featured_posts' => $this->getFeaturedPosts(),
            'recent_posts' => $this->getRecentPosts(),
        ];
        
        return $this->view('frontend/home.twig', $data);
    }

    /**
     * Get homepage content
     */
    private function getHomePageContent()
    {
        // This will be implemented when we create the content management system
        // For now, return default content
        return [
            'hero_title' => 'Welcome to CMS Pro',
            'hero_subtitle' => 'Professional Content Management System',
            'hero_description' => 'Build amazing websites with our powerful and flexible CMS.',
            'features' => [
                [
                    'title' => 'Dynamic Fields',
                    'description' => 'Create custom fields for any content type',
                    'icon' => 'fas fa-cogs'
                ],
                [
                    'title' => 'Multi-language',
                    'description' => 'Support for multiple languages and RTL',
                    'icon' => 'fas fa-globe'
                ],
                [
                    'title' => 'SEO Optimized',
                    'description' => 'Built-in SEO tools and optimization',
                    'icon' => 'fas fa-search'
                ],
                [
                    'title' => 'Secure',
                    'description' => 'Advanced security features and protection',
                    'icon' => 'fas fa-shield-alt'
                ]
            ]
        ];
    }

    /**
     * Get featured posts
     */
    private function getFeaturedPosts()
    {
        // This will be implemented when we create the blog system
        return [];
    }

    /**
     * Get recent posts
     */
    private function getRecentPosts()
    {
        // This will be implemented when we create the blog system
        return [];
    }
}
