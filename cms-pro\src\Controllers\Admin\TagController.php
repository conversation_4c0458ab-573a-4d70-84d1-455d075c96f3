<?php

namespace CmsPro\Controllers\Admin;

use CmsPro\Controllers\BaseController;
use CmsPro\Models\Tag;
use CmsPro\Services\ValidationService;
use CmsPro\Services\SanitizationService;
use CmsPro\Services\ActivityLogger;
use CmsPro\Services\SeoService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Tag Controller for Admin Panel
 * 
 * @package CmsPro\Controllers\Admin
 */
class TagController extends BaseController
{
    private $validationService;
    private $sanitizationService;
    private $activityLogger;
    private $seoService;

    public function __construct()
    {
        parent::__construct();
        
        $this->validationService = new ValidationService();
        $this->sanitizationService = new SanitizationService();
        $this->activityLogger = new ActivityLogger();
        $this->seoService = new SeoService();
    }

    /**
     * Display tags list
     */
    public function index(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('tags.view')) {
                return $this->forbidden('You do not have permission to view tags.');
            }

            // Get filters
            $filters = [
                'search' => $request->query->get('search', ''),
                'page' => max(1, (int) $request->query->get('page', 1)),
                'per_page' => 20
            ];

            // Get tags
            $result = Tag::getFiltered($filters);
            $tags = $result['tags'];
            $pagination = $result['pagination'];

            // Get popular tags
            $popularTags = Tag::getPopular(10);

            // Log activity
            $this->activityLogger->log('tags_viewed', [
                'user_id' => auth()->id(),
                'filters' => $filters
            ]);

            $data = [
                'title' => __('Tags'),
                'tags' => $tags,
                'pagination' => $pagination,
                'popular_tags' => $popularTags,
                'current_filters' => $filters
            ];

            return $this->view('admin/tags/index.twig', $data);

        } catch (\Exception $e) {
            $this->logError('tags_index_error', $e, $request);
            $this->flashError(__('An error occurred while loading tags.'));
            return $this->redirectToRoute('admin.dashboard');
        }
    }

    /**
     * Show create tag form
     */
    public function create(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('tags.create')) {
                return $this->forbidden('You do not have permission to create tags.');
            }

            $data = [
                'title' => __('Create Tag'),
                'tag' => new Tag(),
                'action' => 'create'
            ];

            return $this->view('admin/tags/form.twig', $data);

        } catch (\Exception $e) {
            $this->logError('tags_create_form_error', $e, $request);
            $this->flashError(__('An error occurred while loading the form.'));
            return $this->redirectToRoute('admin.tags.index');
        }
    }

    /**
     * Store new tag
     */
    public function store(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('tags.create')) {
                return $this->forbidden('You do not have permission to create tags.');
            }

            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                $this->flashError(__('Security token mismatch. Please try again.'));
                return $this->back();
            }

            // Validation rules
            $rules = [
                'name' => 'required|string|max:50',
                'description' => 'nullable|string|max:255',
                'color' => 'nullable|string|max:7',
                'meta_title' => 'nullable|string|max:60',
                'meta_description' => 'nullable|string|max:160',
                'meta_keywords' => 'nullable|string|max:255'
            ];

            // Validate input
            $validatedData = $this->validationService->validate($request->request->all(), $rules);

            // Sanitize input
            $sanitizedData = $this->sanitizeTagData($validatedData);

            // Generate slug
            $slug = $this->seoService->generateSlug($sanitizedData['name'], 'tag');
            $sanitizedData['slug'] = $slug;

            // Create tag
            $tag = Tag::create($sanitizedData);

            if ($tag) {
                // Log activity
                $this->activityLogger->log('tag_created', [
                    'user_id' => auth()->id(),
                    'tag_id' => $tag->getId(),
                    'tag_name' => $tag->getName()
                ]);

                $this->flashSuccess(__('Tag created successfully.'));
                
                if ($request->request->get('save_and_continue')) {
                    return $this->redirectToRoute('admin.tags.edit', ['id' => $tag->getId()]);
                }
                
                return $this->redirectToRoute('admin.tags.index');
            }

            $this->flashError(__('Failed to create tag.'));
            return $this->back();

        } catch (\Exception $e) {
            $this->logError('tags_store_error', $e, $request);
            $this->flashError(__('An error occurred while creating the tag.'));
            return $this->back();
        }
    }

    /**
     * Show edit tag form
     */
    public function edit(Request $request, $id)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('tags.edit')) {
                return $this->forbidden('You do not have permission to edit tags.');
            }

            // Find tag
            $tag = Tag::find($id);
            if (!$tag) {
                $this->flashError(__('Tag not found.'));
                return $this->redirectToRoute('admin.tags.index');
            }

            $data = [
                'title' => __('Edit Tag'),
                'tag' => $tag,
                'action' => 'edit'
            ];

            return $this->view('admin/tags/form.twig', $data);

        } catch (\Exception $e) {
            $this->logError('tags_edit_form_error', $e, $request);
            $this->flashError(__('An error occurred while loading the tag.'));
            return $this->redirectToRoute('admin.tags.index');
        }
    }

    /**
     * Update tag
     */
    public function update(Request $request, $id)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('tags.edit')) {
                return $this->forbidden('You do not have permission to edit tags.');
            }

            // Find tag
            $tag = Tag::find($id);
            if (!$tag) {
                $this->flashError(__('Tag not found.'));
                return $this->redirectToRoute('admin.tags.index');
            }

            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                $this->flashError(__('Security token mismatch. Please try again.'));
                return $this->back();
            }

            // Validation rules
            $rules = [
                'name' => 'required|string|max:50',
                'description' => 'nullable|string|max:255',
                'color' => 'nullable|string|max:7',
                'meta_title' => 'nullable|string|max:60',
                'meta_description' => 'nullable|string|max:160',
                'meta_keywords' => 'nullable|string|max:255'
            ];

            // Validate input
            $validatedData = $this->validationService->validate($request->request->all(), $rules);

            // Sanitize input
            $sanitizedData = $this->sanitizeTagData($validatedData);

            // Update slug if name changed
            if ($sanitizedData['name'] !== $tag->getName()) {
                $slug = $this->seoService->generateSlug($sanitizedData['name'], 'tag', $id);
                $sanitizedData['slug'] = $slug;
            }

            // Update tag
            $updated = $tag->update($sanitizedData);

            if ($updated) {
                // Log activity
                $this->activityLogger->log('tag_updated', [
                    'user_id' => auth()->id(),
                    'tag_id' => $tag->getId(),
                    'tag_name' => $tag->getName()
                ]);

                $this->flashSuccess(__('Tag updated successfully.'));
                
                if ($request->request->get('save_and_continue')) {
                    return $this->redirectToRoute('admin.tags.edit', ['id' => $id]);
                }
                
                return $this->redirectToRoute('admin.tags.index');
            }

            $this->flashError(__('Failed to update tag.'));
            return $this->back();

        } catch (\Exception $e) {
            $this->logError('tags_update_error', $e, $request);
            $this->flashError(__('An error occurred while updating the tag.'));
            return $this->back();
        }
    }

    /**
     * Delete tag
     */
    public function destroy(Request $request, $id)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('tags.delete')) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'You do not have permission to delete tags.'
                    ], 403);
                }
                
                return $this->forbidden('You do not have permission to delete tags.');
            }

            // Find tag
            $tag = Tag::find($id);
            if (!$tag) {
                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Tag not found.'
                    ], 404);
                }
                
                $this->flashError(__('Tag not found.'));
                return $this->redirectToRoute('admin.tags.index');
            }

            // Store tag info for logging
            $tagName = $tag->getName();

            // Delete tag
            $deleted = $tag->delete();

            if ($deleted) {
                // Log activity
                $this->activityLogger->log('tag_deleted', [
                    'user_id' => auth()->id(),
                    'tag_id' => $id,
                    'tag_name' => $tagName
                ]);

                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => true,
                        'message' => 'Tag deleted successfully.'
                    ]);
                }

                $this->flashSuccess(__('Tag deleted successfully.'));
                return $this->redirectToRoute('admin.tags.index');
            }

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Failed to delete tag.'
                ]);
            }

            $this->flashError(__('Failed to delete tag.'));
            return $this->redirectToRoute('admin.tags.index');

        } catch (\Exception $e) {
            $this->logError('tags_delete_error', $e, $request);
            
            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'An error occurred while deleting the tag.'
                ], 500);
            }
            
            $this->flashError(__('An error occurred while deleting the tag.'));
            return $this->redirectToRoute('admin.tags.index');
        }
    }

    /**
     * Search tags for AJAX requests
     */
    public function search(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('tags.view')) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'You do not have permission to view tags.'
                ], 403);
            }

            $query = $request->query->get('q', '');
            $limit = min(20, max(1, (int) $request->query->get('limit', 10)));

            if (strlen($query) < 2) {
                return new JsonResponse([
                    'success' => true,
                    'tags' => []
                ]);
            }

            $tags = Tag::search($query, $limit);

            $data = [];
            foreach ($tags as $tag) {
                $data[] = [
                    'id' => $tag->getId(),
                    'name' => $tag->getName(),
                    'slug' => $tag->getSlug(),
                    'color' => $tag->getColor(),
                    'posts_count' => $tag->getPostsCount()
                ];
            }

            return new JsonResponse([
                'success' => true,
                'tags' => $data
            ]);

        } catch (\Exception $e) {
            $this->logError('tags_search_error', $e, $request);
            
            return new JsonResponse([
                'success' => false,
                'message' => 'An error occurred while searching tags.'
            ], 500);
        }
    }

    /**
     * Get tag cloud data
     */
    public function cloud(Request $request)
    {
        $this->request = $request;

        try {
            // Check permission
            if (!auth()->can('tags.view')) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'You do not have permission to view tags.'
                ], 403);
            }

            $limit = min(100, max(10, (int) $request->query->get('limit', 50)));
            $cloudData = Tag::getCloudData($limit);

            return new JsonResponse([
                'success' => true,
                'cloud_data' => $cloudData
            ]);

        } catch (\Exception $e) {
            $this->logError('tags_cloud_error', $e, $request);
            
            return new JsonResponse([
                'success' => false,
                'message' => 'An error occurred while loading tag cloud.'
            ], 500);
        }
    }

    /**
     * Sanitize tag data
     */
    private function sanitizeTagData($data)
    {
        return [
            'name' => $this->sanitizationService->sanitizeInput($data['name']),
            'description' => $this->sanitizationService->sanitizeInput($data['description'] ?? ''),
            'color' => $this->sanitizationService->sanitizeInput($data['color'] ?? '#6c757d'),
            'meta_title' => $this->sanitizationService->sanitizeInput($data['meta_title'] ?? ''),
            'meta_description' => $this->sanitizationService->sanitizeInput($data['meta_description'] ?? ''),
            'meta_keywords' => $this->sanitizationService->sanitizeInput($data['meta_keywords'] ?? '')
        ];
    }

    /**
     * Validate CSRF token
     */
    private function validateCsrfToken(Request $request)
    {
        $token = $request->request->get('_token') ?: $request->headers->get('X-CSRF-TOKEN');
        
        if (!$token) {
            return false;
        }

        $sessionToken = session()->get('_token');
        return $sessionToken && hash_equals($sessionToken, $token);
    }

    /**
     * Log errors
     */
    private function logError($event, \Exception $e, Request $request)
    {
        $logData = [
            'event' => $event,
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'user_id' => auth()->id(),
            'ip_address' => $request->getClientIp(),
            'user_agent' => $request->headers->get('User-Agent'),
            'url' => $request->getUri()
        ];

        error_log(json_encode($logData), 3, storage_path('logs/tags.log'));
        
        if ($this->activityLogger) {
            $this->activityLogger->log($event, $logData);
        }
    }
}
