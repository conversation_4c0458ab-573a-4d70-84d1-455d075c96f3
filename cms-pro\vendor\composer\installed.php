<?php return array(
    'root' => array(
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => null,
        'name' => 'cms-pro/professional-cms',
        'dev' => false,
    ),
    'versions' => array(
        'cms-pro/professional-cms' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => null,
            'dev_requirement' => false,
        ),
        'cocur/slugify' => array(
            'pretty_version' => 'v3.2',
            'version' => '3.2.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cocur/slugify',
            'aliases' => array(),
            'reference' => 'd41701efe58ba2df9cae029c3d21e1518cc6780e',
            'dev_requirement' => false,
        ),
        'doctrine/cache' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/cache',
            'aliases' => array(),
            'reference' => '1ca8f21980e770095a31456042471a57bc4c68fb',
            'dev_requirement' => false,
        ),
        'doctrine/dbal' => array(
            'pretty_version' => '2.13.9',
            'version' => '2.13.9.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/dbal',
            'aliases' => array(),
            'reference' => 'c480849ca3ad6706a39c970cdfe6888fa8a058b8',
            'dev_requirement' => false,
        ),
        'doctrine/deprecations' => array(
            'pretty_version' => '1.1.5',
            'version' => '1.1.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/deprecations',
            'aliases' => array(),
            'reference' => '459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38',
            'dev_requirement' => false,
        ),
        'doctrine/event-manager' => array(
            'pretty_version' => '1.2.0',
            'version' => '1.2.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/event-manager',
            'aliases' => array(),
            'reference' => '95aa4cb529f1e96576f3fda9f5705ada4056a520',
            'dev_requirement' => false,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v5.5.1',
            'version' => '5.5.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'reference' => '83b609028194aa042ea33b5af2d41a7427de80e6',
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '6.5.8',
            'version' => '6.5.8.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'reference' => 'a52f0440530b54fa079ce76e8c5d196a42cad981',
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '1.5.3',
            'version' => '1.5.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'reference' => '67ab6e18aaa14d753cc148911d273f6e6cb6721e',
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '1.9.1',
            'version' => '1.9.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'reference' => 'e4490cabc77465aaee90b20cfc9a770f8c04be6b',
            'dev_requirement' => false,
        ),
        'intervention/image' => array(
            'pretty_version' => '2.7.2',
            'version' => '2.7.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../intervention/image',
            'aliases' => array(),
            'reference' => '04be355f8d6734c826045d02a1079ad658322dad',
            'dev_requirement' => false,
        ),
        'league/csv' => array(
            'pretty_version' => '9.5.0',
            'version' => '9.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/csv',
            'aliases' => array(),
            'reference' => 'b348d09d0d258a4f068efb50a2510dc63101c213',
            'dev_requirement' => false,
        ),
        'league/flysystem' => array(
            'pretty_version' => '1.0.70',
            'version' => '1.0.70.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem',
            'aliases' => array(),
            'reference' => '585824702f534f8d3cf7fab7225e8466cc4b7493',
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '1.27.1',
            'version' => '1.27.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'reference' => '904713c5929655dc9b97288b69cfeedad610c9a1',
            'dev_requirement' => false,
        ),
        'paragonie/constant_time_encoding' => array(
            'pretty_version' => 'v2.7.0',
            'version' => '2.7.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/constant_time_encoding',
            'aliases' => array(),
            'reference' => '52a0d99e69f56b9ec27ace92ba56897fe6993105',
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '9.99.100.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'dev_requirement' => false,
        ),
        'phpmailer/phpmailer' => array(
            'pretty_version' => 'v6.10.0',
            'version' => '6.10.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpmailer/phpmailer',
            'aliases' => array(),
            'reference' => 'bf74d75a1fde6beaa34a0ddae2ec5fce0f72a144',
            'dev_requirement' => false,
        ),
        'pragmarx/google2fa' => array(
            'pretty_version' => 'v7.0.0',
            'version' => '7.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pragmarx/google2fa',
            'aliases' => array(),
            'reference' => '0afb47f8a686bd203fe85a05bab85139f3c1971e',
            'dev_requirement' => false,
        ),
        'psr/container' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'reference' => 'b7ce3b176482dbbc1245ebf52b181af44c2cf55f',
            'dev_requirement' => false,
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'reference' => 'f6561bf28d520154e4b0ec72be95418abe6d9363',
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0.0',
            ),
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'dev_requirement' => false,
        ),
        'ramsey/uuid' => array(
            'pretty_version' => '3.9.7',
            'version' => '3.9.7.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/uuid',
            'aliases' => array(),
            'reference' => 'dc75aa439eb4c1b77f5379fd958b3dc0e6014178',
            'dev_requirement' => false,
        ),
        'rhumsaa/uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.9.7',
            ),
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v1.10.0',
            'version' => '1.10.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'reference' => '761c8b8387cfe5f8026594a75fdf0a4e83ba6974',
            'dev_requirement' => false,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v4.4.49',
            'version' => '4.4.49.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'reference' => '191413c7b832c015bb38eae963f2e57498c3c173',
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v4.4.47',
            'version' => '4.4.47.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'reference' => '0eaf33cd6d1b3eaa50e7bc48b17f6e45789df35d',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.30.0',
            'version' => '1.30.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'reference' => '0424dff1c58f028c451efff2045f5d92410bd540',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.30.0',
            'version' => '1.30.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'reference' => 'a6e83bdeb3c84391d1dfe16f42e40727ce524a5c',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.30.0',
            'version' => '1.30.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'reference' => 'a95281b0be0d9ab48050ebd988b967875cdb9fdb',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.30.0',
            'version' => '1.30.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'reference' => 'fd22ab50000ef01661e2a31d850ebaa297f8e03c',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php56' => array(
            'pretty_version' => 'v1.20.0',
            'version' => '1.20.0.0',
            'type' => 'metapackage',
            'install_path' => null,
            'aliases' => array(),
            'reference' => '54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php72' => array(
            'pretty_version' => 'v1.30.0',
            'version' => '1.30.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php72',
            'aliases' => array(),
            'reference' => '10112722600777e02d2745716b70c5db4ca70442',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.30.0',
            'version' => '1.30.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'reference' => '77fa7995ac1b21ab60769b7323d600a991a90433',
            'dev_requirement' => false,
        ),
        'symfony/routing' => array(
            'pretty_version' => 'v4.4.44',
            'version' => '4.4.44.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/routing',
            'aliases' => array(),
            'reference' => 'f7751fd8b60a07f3f349947a309b5bdfce22d6ae',
            'dev_requirement' => false,
        ),
        'symfony/security-core' => array(
            'pretty_version' => 'v4.4.48',
            'version' => '4.4.48.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/security-core',
            'aliases' => array(),
            'reference' => '423ccb332784b236dfe6c5f396d0ac49db57c914',
            'dev_requirement' => false,
        ),
        'symfony/security-csrf' => array(
            'pretty_version' => 'v4.4.37',
            'version' => '4.4.37.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/security-csrf',
            'aliases' => array(),
            'reference' => '45c956ef58135091f53732646a0acd28034f02c0',
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v1.10.0',
            'version' => '1.10.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'reference' => 'afa00c500c2d6aea6e3b2f4862355f507bc5ebb4',
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v1.10.0',
            'version' => '1.10.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'reference' => '7462e5c4cb8b9cd152f992e8f10963b5641921f6',
            'dev_requirement' => false,
        ),
        'symfony/validator' => array(
            'pretty_version' => 'v4.4.48',
            'version' => '4.4.48.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/validator',
            'aliases' => array(),
            'reference' => '54781a4c41efbd283b779110bf8ae7f263737775',
            'dev_requirement' => false,
        ),
        'twig/twig' => array(
            'pretty_version' => 'v2.16.1',
            'version' => '2.16.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../twig/twig',
            'aliases' => array(),
            'reference' => '19185947ec75d433a3ac650af32fc05649b95ee1',
            'dev_requirement' => false,
        ),
    ),
);
