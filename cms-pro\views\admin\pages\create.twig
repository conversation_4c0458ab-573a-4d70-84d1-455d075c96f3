{% extends "layouts/admin.twig" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="page-form-container">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ title }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ url('/admin/pages') }}">{{ __('Pages') }}</a>
                    </li>
                    <li class="breadcrumb-item active">{{ __('Create') }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ url('/admin/pages') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>{{ __('Back to Pages') }}
            </a>
        </div>
    </div>

    <form method="POST" action="{{ url('/admin/pages') }}" class="page-form" id="page-form" novalidate>
        {{ csrf_field() | raw }}
        
        <div class="row">
            <!-- Main Content Column -->
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-edit me-2"></i>{{ __('Page Content') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Title -->
                        <div class="mb-3">
                            <label for="title" class="form-label">
                                {{ __('Title') }} <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control {{ errors.title ? 'is-invalid' : '' }}" 
                                   id="title" 
                                   name="title" 
                                   value="{{ old('title', page.title) }}" 
                                   required
                                   maxlength="255"
                                   placeholder="{{ __('Enter page title...') }}">
                            {% if errors.title %}
                                <div class="invalid-feedback">{{ errors.title }}</div>
                            {% endif %}
                        </div>

                        <!-- Slug -->
                        <div class="mb-3">
                            <label for="slug" class="form-label">{{ __('URL Slug') }}</label>
                            <div class="input-group">
                                <span class="input-group-text">{{ url('/') }}/</span>
                                <input type="text" 
                                       class="form-control {{ errors.slug ? 'is-invalid' : '' }}" 
                                       id="slug" 
                                       name="slug" 
                                       value="{{ old('slug', page.slug) }}"
                                       maxlength="255"
                                       placeholder="{{ __('auto-generated') }}">
                                <button type="button" class="btn btn-outline-secondary" id="generate-slug">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                            {% if errors.slug %}
                                <div class="invalid-feedback">{{ errors.slug }}</div>
                            {% endif %}
                            <div class="form-text">
                                {{ __('Leave empty to auto-generate from title') }}
                            </div>
                        </div>

                        <!-- Content -->
                        <div class="mb-3">
                            <label for="content" class="form-label">
                                {{ __('Content') }} <span class="text-danger">*</span>
                            </label>
                            
                            {% include 'admin/fields/wysiwyg-form.twig' with {
                                field: {
                                    name: 'content',
                                    label: 'Content',
                                    value: old('content', page.content),
                                    required: true,
                                    height: 500,
                                    editor_type: 'advanced'
                                },
                                errors: errors
                            } %}
                        </div>

                        <!-- Excerpt -->
                        <div class="mb-3">
                            <label for="excerpt" class="form-label">{{ __('Excerpt') }}</label>
                            <textarea class="form-control {{ errors.excerpt ? 'is-invalid' : '' }}" 
                                      id="excerpt" 
                                      name="excerpt" 
                                      rows="3"
                                      maxlength="500"
                                      placeholder="{{ __('Optional short description...') }}">{{ old('excerpt', page.excerpt) }}</textarea>
                            {% if errors.excerpt %}
                                <div class="invalid-feedback">{{ errors.excerpt }}</div>
                            {% endif %}
                            <div class="form-text">
                                {{ __('Brief description for search engines and previews') }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEO Settings -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-success">
                            <i class="fas fa-search me-2"></i>{{ __('SEO Settings') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Meta Title -->
                        <div class="mb-3">
                            <label for="meta_title" class="form-label">{{ __('Meta Title') }}</label>
                            <input type="text" 
                                   class="form-control {{ errors.meta_title ? 'is-invalid' : '' }}" 
                                   id="meta_title" 
                                   name="meta_title" 
                                   value="{{ old('meta_title', page.meta_title) }}"
                                   maxlength="255"
                                   placeholder="{{ __('Leave empty to use page title') }}">
                            {% if errors.meta_title %}
                                <div class="invalid-feedback">{{ errors.meta_title }}</div>
                            {% endif %}
                            <div class="form-text">
                                <span class="meta-title-length">0</span>/60 {{ __('characters (recommended)') }}
                            </div>
                        </div>

                        <!-- Meta Description -->
                        <div class="mb-3">
                            <label for="meta_description" class="form-label">{{ __('Meta Description') }}</label>
                            <textarea class="form-control {{ errors.meta_description ? 'is-invalid' : '' }}" 
                                      id="meta_description" 
                                      name="meta_description" 
                                      rows="3"
                                      maxlength="500"
                                      placeholder="{{ __('Brief description for search engines...') }}">{{ old('meta_description', page.meta_description) }}</textarea>
                            {% if errors.meta_description %}
                                <div class="invalid-feedback">{{ errors.meta_description }}</div>
                            {% endif %}
                            <div class="form-text">
                                <span class="meta-description-length">0</span>/160 {{ __('characters (recommended)') }}
                            </div>
                        </div>

                        <!-- Meta Keywords -->
                        <div class="mb-3">
                            <label for="meta_keywords" class="form-label">{{ __('Meta Keywords') }}</label>
                            <input type="text" 
                                   class="form-control {{ errors.meta_keywords ? 'is-invalid' : '' }}" 
                                   id="meta_keywords" 
                                   name="meta_keywords" 
                                   value="{{ old('meta_keywords', page.meta_keywords) }}"
                                   maxlength="255"
                                   placeholder="{{ __('keyword1, keyword2, keyword3') }}">
                            {% if errors.meta_keywords %}
                                <div class="invalid-feedback">{{ errors.meta_keywords }}</div>
                            {% endif %}
                            <div class="form-text">
                                {{ __('Comma-separated keywords (optional)') }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Custom Fields -->
                {% if custom_fields and custom_fields|length > 0 %}
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-info">
                            <i class="fas fa-cogs me-2"></i>{{ __('Custom Fields') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        {% for field in custom_fields %}
                            {% include 'admin/fields/' ~ field.type ~ '-form.twig' with {
                                field: field,
                                value: old('custom_fields.' ~ field.name, page.custom_fields[field.name] ?? null),
                                errors: errors
                            } %}
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Sidebar Column -->
            <div class="col-lg-4">
                <!-- Publish Settings -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-paper-plane me-2"></i>{{ __('Publish Settings') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Status -->
                        <div class="mb-3">
                            <label for="status" class="form-label">{{ __('Status') }}</label>
                            <select class="form-select {{ errors.status ? 'is-invalid' : '' }}" 
                                    id="status" 
                                    name="status" 
                                    required>
                                {% for key, label in statuses %}
                                <option value="{{ key }}" {{ old('status', page.status) == key ? 'selected' : '' }}>
                                    {{ label }}
                                </option>
                                {% endfor %}
                            </select>
                            {% if errors.status %}
                                <div class="invalid-feedback">{{ errors.status }}</div>
                            {% endif %}
                        </div>

                        <!-- Published Date -->
                        <div class="mb-3" id="published-date-group">
                            <label for="published_at" class="form-label">{{ __('Published Date') }}</label>
                            <input type="datetime-local" 
                                   class="form-control {{ errors.published_at ? 'is-invalid' : '' }}" 
                                   id="published_at" 
                                   name="published_at" 
                                   value="{{ old('published_at', page.published_at ? page.published_at.format('Y-m-d\\TH:i') : '') }}">
                            {% if errors.published_at %}
                                <div class="invalid-feedback">{{ errors.published_at }}</div>
                            {% endif %}
                        </div>

                        <!-- Scheduled Date -->
                        <div class="mb-3" id="scheduled-date-group" style="display: none;">
                            <label for="scheduled_at" class="form-label">{{ __('Scheduled Date') }}</label>
                            <input type="datetime-local" 
                                   class="form-control {{ errors.scheduled_at ? 'is-invalid' : '' }}" 
                                   id="scheduled_at" 
                                   name="scheduled_at" 
                                   value="{{ old('scheduled_at', page.scheduled_at ? page.scheduled_at.format('Y-m-d\\TH:i') : '') }}">
                            {% if errors.scheduled_at %}
                                <div class="invalid-feedback">{{ errors.scheduled_at }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Page Settings -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="m-0 font-weight-bold text-warning">
                            <i class="fas fa-cog me-2"></i>{{ __('Page Settings') }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Template -->
                        <div class="mb-3">
                            <label for="template" class="form-label">{{ __('Template') }}</label>
                            <select class="form-select {{ errors.template ? 'is-invalid' : '' }}" 
                                    id="template" 
                                    name="template">
                                {% for key, label in templates %}
                                <option value="{{ key }}" {{ old('template', page.template) == key ? 'selected' : '' }}>
                                    {{ label }}
                                </option>
                                {% endfor %}
                            </select>
                            {% if errors.template %}
                                <div class="invalid-feedback">{{ errors.template }}</div>
                            {% endif %}
                        </div>

                        <!-- Parent Page -->
                        <div class="mb-3">
                            <label for="parent_id" class="form-label">{{ __('Parent Page') }}</label>
                            <select class="form-select {{ errors.parent_id ? 'is-invalid' : '' }}" 
                                    id="parent_id" 
                                    name="parent_id">
                                <option value="">{{ __('No Parent (Root Page)') }}</option>
                                {% for parentPage in parent_pages %}
                                <option value="{{ parentPage.id }}" {{ old('parent_id', page.parent_id) == parentPage.id ? 'selected' : '' }}>
                                    {{ parentPage.title }}
                                </option>
                                {% endfor %}
                            </select>
                            {% if errors.parent_id %}
                                <div class="invalid-feedback">{{ errors.parent_id }}</div>
                            {% endif %}
                        </div>

                        <!-- Featured Image -->
                        <div class="mb-3">
                            <label for="featured_image" class="form-label">{{ __('Featured Image') }}</label>
                            <div class="input-group">
                                <input type="text" 
                                       class="form-control {{ errors.featured_image ? 'is-invalid' : '' }}" 
                                       id="featured_image" 
                                       name="featured_image" 
                                       value="{{ old('featured_image', page.featured_image) }}"
                                       placeholder="{{ __('Image URL or path') }}">
                                <button type="button" class="btn btn-outline-secondary" id="browse-image">
                                    <i class="fas fa-folder-open"></i>
                                </button>
                            </div>
                            {% if errors.featured_image %}
                                <div class="invalid-feedback">{{ errors.featured_image }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" name="action" value="save" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ __('Save Page') }}
                            </button>
                            
                            <div class="btn-group">
                                <button type="submit" name="action" value="save_and_continue" class="btn btn-outline-primary">
                                    <i class="fas fa-save me-2"></i>{{ __('Save & Continue') }}
                                </button>
                                <button type="submit" name="action" value="save_and_new" class="btn btn-outline-primary">
                                    <i class="fas fa-plus me-2"></i>{{ __('Save & New') }}
                                </button>
                            </div>
                            
                            <button type="button" class="btn btn-outline-secondary" id="preview-page">
                                <i class="fas fa-eye me-2"></i>{{ __('Preview') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
/**
 * Page Form Management
 * Enhanced with auto-save, validation, and user experience features
 */
class PageForm {
    constructor() {
        this.form = document.getElementById('page-form');
        this.titleField = document.getElementById('title');
        this.slugField = document.getElementById('slug');
        this.statusField = document.getElementById('status');
        this.autoSaveInterval = null;
        this.hasUnsavedChanges = false;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupSlugGeneration();
        this.setupStatusHandling();
        this.setupSeoCounters();
        this.setupAutoSave();
        this.setupFormValidation();
        this.setupUnsavedChangesWarning();
    }

    setupEventListeners() {
        // Generate slug button
        document.getElementById('generate-slug').addEventListener('click', () => {
            this.generateSlug();
        });

        // Browse image button
        document.getElementById('browse-image').addEventListener('click', () => {
            this.openMediaLibrary();
        });

        // Preview button
        document.getElementById('preview-page').addEventListener('click', () => {
            this.previewPage();
        });

        // Form submission
        this.form.addEventListener('submit', (e) => {
            this.handleFormSubmit(e);
        });

        // Track changes
        this.form.addEventListener('input', () => {
            this.hasUnsavedChanges = true;
        });

        this.form.addEventListener('change', () => {
            this.hasUnsavedChanges = true;
        });
    }

    setupSlugGeneration() {
        // Auto-generate slug when title changes
        this.titleField.addEventListener('input', () => {
            if (!this.slugField.value || this.slugField.dataset.autoGenerated === 'true') {
                this.generateSlug();
                this.slugField.dataset.autoGenerated = 'true';
            }
        });

        // Mark slug as manually edited
        this.slugField.addEventListener('input', () => {
            this.slugField.dataset.autoGenerated = 'false';
        });
    }

    setupStatusHandling() {
        this.statusField.addEventListener('change', () => {
            this.toggleDateFields();
        });

        // Initial setup
        this.toggleDateFields();
    }

    setupSeoCounters() {
        // Meta title counter
        const metaTitleField = document.getElementById('meta_title');
        const metaTitleCounter = document.querySelector('.meta-title-length');

        if (metaTitleField && metaTitleCounter) {
            metaTitleField.addEventListener('input', () => {
                const length = metaTitleField.value.length;
                metaTitleCounter.textContent = length;
                metaTitleCounter.className = length > 60 ? 'text-warning' : 'text-muted';
            });

            // Initial count
            metaTitleCounter.textContent = metaTitleField.value.length;
        }

        // Meta description counter
        const metaDescField = document.getElementById('meta_description');
        const metaDescCounter = document.querySelector('.meta-description-length');

        if (metaDescField && metaDescCounter) {
            metaDescField.addEventListener('input', () => {
                const length = metaDescField.value.length;
                metaDescCounter.textContent = length;
                metaDescCounter.className = length > 160 ? 'text-warning' : 'text-muted';
            });

            // Initial count
            metaDescCounter.textContent = metaDescField.value.length;
        }
    }

    setupAutoSave() {
        // Auto-save every 30 seconds
        this.autoSaveInterval = setInterval(() => {
            if (this.hasUnsavedChanges) {
                this.autoSave();
            }
        }, 30000);
    }

    setupFormValidation() {
        // Real-time validation
        const requiredFields = this.form.querySelectorAll('[required]');

        requiredFields.forEach(field => {
            field.addEventListener('blur', () => {
                this.validateField(field);
            });
        });
    }

    setupUnsavedChangesWarning() {
        window.addEventListener('beforeunload', (e) => {
            if (this.hasUnsavedChanges) {
                e.preventDefault();
                e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
                return e.returnValue;
            }
        });
    }

    generateSlug() {
        const title = this.titleField.value.trim();
        if (!title) return;

        // Simple slug generation
        const slug = title
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/-+/g, '-') // Remove multiple hyphens
            .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens

        this.slugField.value = slug;
        this.slugField.dataset.autoGenerated = 'true';
    }

    toggleDateFields() {
        const status = this.statusField.value;
        const publishedGroup = document.getElementById('published-date-group');
        const scheduledGroup = document.getElementById('scheduled-date-group');

        if (status === 'scheduled') {
            publishedGroup.style.display = 'none';
            scheduledGroup.style.display = 'block';
            document.getElementById('scheduled_at').required = true;
            document.getElementById('published_at').required = false;
        } else if (status === 'published') {
            publishedGroup.style.display = 'block';
            scheduledGroup.style.display = 'none';
            document.getElementById('published_at').required = false;
            document.getElementById('scheduled_at').required = false;
        } else {
            publishedGroup.style.display = 'none';
            scheduledGroup.style.display = 'none';
            document.getElementById('published_at').required = false;
            document.getElementById('scheduled_at').required = false;
        }
    }

    validateField(field) {
        const isValid = field.checkValidity();

        if (isValid) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        } else {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
        }

        return isValid;
    }

    async autoSave() {
        try {
            const formData = new FormData(this.form);
            formData.append('auto_save', '1');

            const response = await fetch('/admin/pages/auto-save', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': this.getCsrfToken()
                }
            });

            if (response.ok) {
                this.showAutoSaveIndicator('saved');
                this.hasUnsavedChanges = false;
            } else {
                this.showAutoSaveIndicator('error');
            }

        } catch (error) {
            console.error('Auto-save error:', error);
            this.showAutoSaveIndicator('error');
        }
    }

    showAutoSaveIndicator(status) {
        let indicator = document.getElementById('auto-save-indicator');

        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'auto-save-indicator';
            indicator.className = 'position-fixed';
            indicator.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 1050;';
            document.body.appendChild(indicator);
        }

        const messages = {
            saving: '<span class="badge bg-info"><i class="fas fa-spinner fa-spin me-1"></i>Auto-saving...</span>',
            saved: '<span class="badge bg-success"><i class="fas fa-check me-1"></i>Auto-saved</span>',
            error: '<span class="badge bg-danger"><i class="fas fa-exclamation-triangle me-1"></i>Auto-save failed</span>'
        };

        indicator.innerHTML = messages[status] || messages.saved;

        // Hide after 3 seconds
        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.remove();
            }
        }, 3000);
    }

    openMediaLibrary() {
        // This would open a media library modal
        // For now, just show a placeholder
        alert('Media library integration coming soon!');
    }

    previewPage() {
        // Save current form data to session and open preview
        const formData = new FormData(this.form);

        fetch('/admin/pages/preview', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': this.getCsrfToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.open(data.preview_url, '_blank');
            } else {
                this.showNotification(data.message || 'Preview failed', 'error');
            }
        })
        .catch(error => {
            console.error('Preview error:', error);
            this.showNotification('Preview failed', 'error');
        });
    }

    async handleFormSubmit(e) {
        e.preventDefault();

        // Validate form
        if (!this.form.checkValidity()) {
            e.stopPropagation();
            this.form.classList.add('was-validated');

            // Focus first invalid field
            const firstInvalid = this.form.querySelector(':invalid');
            if (firstInvalid) {
                firstInvalid.focus();
                firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }

            return;
        }

        // Show loading state
        const submitButton = e.submitter;
        const originalText = submitButton.innerHTML;
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';

        try {
            const formData = new FormData(this.form);

            const response = await fetch(this.form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.hasUnsavedChanges = false;
                this.showNotification(result.message, 'success');

                if (result.redirect) {
                    setTimeout(() => {
                        window.location.href = result.redirect;
                    }, 1000);
                }
            } else {
                this.showNotification(result.message || 'Save failed', 'error');
            }

        } catch (error) {
            console.error('Form submission error:', error);
            this.showNotification('An error occurred while saving', 'error');
        } finally {
            // Restore button state
            submitButton.disabled = false;
            submitButton.innerHTML = originalText;
        }
    }

    getCsrfToken() {
        return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
        notification.innerHTML = `
            ${this.escapeHtml(message)}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    destroy() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.pageForm = new PageForm();
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (window.pageForm) {
        window.pageForm.destroy();
    }
});
</script>

<style>
/* Page Form Styles */
.page-form-container .card-header h6 {
    font-size: 0.875rem;
    font-weight: 600;
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-text {
    font-size: 0.8125rem;
}

.meta-title-length,
.meta-description-length {
    font-weight: 500;
}

.text-warning {
    color: #f0ad4e !important;
}

/* Auto-save indicator */
#auto-save-indicator {
    animation: fadeInOut 0.3s ease-in-out;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translateX(-50%) translateY(-10px); }
    100% { opacity: 1; transform: translateX(-50%) translateY(0); }
}

/* Form validation styles */
.was-validated .form-control:valid,
.form-control.is-valid {
    border-color: #28a745;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.38 1.38 3.72-**********-4.66 4.66z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:invalid,
.form-control.is-invalid {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 7.4 5.8 6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .page-form-container .col-lg-8,
    .page-form-container .col-lg-4 {
        margin-bottom: 1rem;
    }

    .btn-group {
        width: 100%;
    }

    .btn-group .btn {
        flex: 1;
    }
}

/* Dark mode support */
[data-bs-theme="dark"] .card-header {
    background-color: #2d3748 !important;
    border-color: #4a5568;
}

[data-bs-theme="dark"] .form-control {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
}

[data-bs-theme="dark"] .form-control:focus {
    background-color: #2d3748;
    border-color: #0d6efd;
    color: #e2e8f0;
}
</style>
{% endblock %}
