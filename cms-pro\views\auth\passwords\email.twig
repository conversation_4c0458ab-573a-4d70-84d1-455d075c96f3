{% extends "layouts/base.twig" %}

{% block title %}{{ __('Reset Password') }} - {{ app.name }}{% endblock %}

{% block body_class %}auth-page{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-lg border-0 mt-5">
                <div class="card-header bg-info text-white text-center py-4">
                    <h3 class="mb-0">
                        <i class="fas fa-key me-2"></i>
                        {{ __('Reset Password') }}
                    </h3>
                </div>
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <p class="text-muted">
                            {{ __('Enter your email address and we\'ll send you a link to reset your password.') }}
                        </p>
                    </div>

                    <form method="POST" action="{{ url('/password/email') }}" class="needs-validation" novalidate>
                        {{ csrf_field() | raw }}
                        
                        <div class="mb-4">
                            <label for="email" class="form-label">{{ __('Email Address') }}</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input 
                                    type="email" 
                                    class="form-control" 
                                    id="email" 
                                    name="email" 
                                    value="{{ old('email') }}"
                                    required 
                                    autofocus
                                    placeholder="{{ __('Enter your email address') }}"
                                >
                                <div class="invalid-feedback">
                                    {{ __('Please enter a valid email address.') }}
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-info btn-lg">
                                <i class="fas fa-paper-plane me-2"></i>
                                {{ __('Send Reset Link') }}
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center py-3">
                    <div class="row">
                        <div class="col">
                            <a href="{{ url('/login') }}" class="text-decoration-none">
                                <i class="fas fa-arrow-left me-1"></i>
                                {{ __('Back to Login') }}
                            </a>
                        </div>
                        {% if config('auth.registration_enabled', true) %}
                        <div class="col">
                            <a href="{{ url('/register') }}" class="text-decoration-none">
                                <i class="fas fa-user-plus me-1"></i>
                                {{ __('Register') }}
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Help Section -->
            <div class="card mt-3 border-secondary">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ __('What happens next?') }}
                    </h6>
                </div>
                <div class="card-body py-3">
                    <small class="text-muted">
                        <ol class="mb-0 ps-3">
                            <li>{{ __('We\'ll send a reset link to your email address') }}</li>
                            <li>{{ __('Click the link in the email (valid for 1 hour)') }}</li>
                            <li>{{ __('Enter your new password') }}</li>
                            <li>{{ __('Login with your new password') }}</li>
                        </ol>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
// Auto-focus on email field when page loads
document.addEventListener('DOMContentLoaded', function() {
    const emailField = document.getElementById('email');
    if (emailField && !emailField.value) {
        emailField.focus();
    }
});
</script>
{% endblock %}

{% block styles %}
{{ parent() }}
<style>
.auth-page .card-header.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
}

.auth-page .btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border: none;
    font-weight: 600;
}

.auth-page .btn-info:hover {
    background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
    transform: translateY(-1px);
}

.auth-page .input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
}

.auth-page .form-control:focus {
    border-color: #17a2b8;
    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
}

.auth-page .card-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

.auth-page .card-footer a {
    color: #17a2b8;
    font-weight: 500;
}

.auth-page .card-footer a:hover {
    color: #138496;
    text-decoration: underline !important;
}

.auth-page ol {
    font-size: 0.85rem;
}

.auth-page ol li {
    margin-bottom: 0.25rem;
}
</style>
{% endblock %}
