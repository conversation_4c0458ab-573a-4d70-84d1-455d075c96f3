<?php

namespace CmsPro\Database\Seeders;

use CmsPro\Models\Role;
use CmsPro\Models\Permission;
use CmsPro\Models\User;

/**
 * Role and Permission Seeder
 * 
 * Creates system roles and permissions for RBAC
 */
class RolePermissionSeeder
{
    public function run()
    {
        echo "Creating system permissions...\n";
        $this->createPermissions();
        
        echo "Creating system roles...\n";
        $this->createRoles();
        
        echo "Assigning permissions to roles...\n";
        $this->assignPermissionsToRoles();
        
        echo "Creating default admin user...\n";
        $this->createDefaultAdmin();
        
        echo "Role and Permission seeder completed!\n";
    }
    
    private function createPermissions()
    {
        Permission::createSystemPermissions();
    }
    
    private function createRoles()
    {
        Role::createSystemRoles();
    }
    
    private function assignPermissionsToRoles()
    {
        $db = app()->getDatabase();
        
        // Get roles
        $superAdmin = $db->selectOne("SELECT * FROM roles WHERE slug = 'super_admin'");
        $admin = $db->selectOne("SELECT * FROM roles WHERE slug = 'admin'");
        $editor = $db->selectOne("SELECT * FROM roles WHERE slug = 'editor'");
        $author = $db->selectOne("SELECT * FROM roles WHERE slug = 'author'");
        $subscriber = $db->selectOne("SELECT * FROM roles WHERE slug = 'subscriber'");
        
        // Get all permissions
        $permissions = $db->select("SELECT * FROM permissions WHERE status = 'active'");
        $permissionsBySlug = [];
        foreach ($permissions as $permission) {
            $permissionsBySlug[$permission['slug']] = $permission;
        }
        
        // Super Admin - All permissions
        if ($superAdmin) {
            $superAdminRole = Role::find($superAdmin['id']);
            foreach ($permissions as $permission) {
                $superAdminRole->assignPermission($permission['id']);
            }
        }
        
        // Admin - Most permissions except system-critical ones
        if ($admin) {
            $adminRole = Role::find($admin['id']);
            $adminPermissions = [
                'admin.access', 'admin.dashboard',
                'users.view', 'users.create', 'users.edit', 'users.delete', 'users.manage_roles',
                'roles.view', 'roles.create', 'roles.edit',
                'pages.view', 'pages.create', 'pages.edit', 'pages.delete', 'pages.publish', 'pages.manage_all',
                'content.view', 'content.create', 'content.edit', 'content.delete', 'content.publish', 'content.manage_all',
                'media.view', 'media.upload', 'media.edit', 'media.delete', 'media.manage_all',
                'fields.view', 'fields.create', 'fields.edit', 'fields.delete',
                'settings.view', 'settings.edit'
            ];
            
            foreach ($adminPermissions as $permSlug) {
                if (isset($permissionsBySlug[$permSlug])) {
                    $adminRole->assignPermission($permissionsBySlug[$permSlug]['id']);
                }
            }
        }
        
        // Editor - Content management permissions
        if ($editor) {
            $editorRole = Role::find($editor['id']);
            $editorPermissions = [
                'admin.access', 'admin.dashboard',
                'pages.view', 'pages.create', 'pages.edit', 'pages.publish', 'pages.manage_all',
                'content.view', 'content.create', 'content.edit', 'content.publish', 'content.manage_all',
                'media.view', 'media.upload', 'media.edit', 'media.manage_all',
                'users.view'
            ];
            
            foreach ($editorPermissions as $permSlug) {
                if (isset($permissionsBySlug[$permSlug])) {
                    $editorRole->assignPermission($permissionsBySlug[$permSlug]['id']);
                }
            }
        }
        
        // Author - Own content management
        if ($author) {
            $authorRole = Role::find($author['id']);
            $authorPermissions = [
                'admin.access', 'admin.dashboard',
                'pages.view', 'pages.create', 'pages.edit',
                'content.view', 'content.create', 'content.edit',
                'media.view', 'media.upload', 'media.edit'
            ];
            
            foreach ($authorPermissions as $permSlug) {
                if (isset($permissionsBySlug[$permSlug])) {
                    $authorRole->assignPermission($permissionsBySlug[$permSlug]['id']);
                }
            }
        }
        
        // Subscriber - Basic access only
        if ($subscriber) {
            $subscriberRole = Role::find($subscriber['id']);
            $subscriberPermissions = [
                'admin.access', 'admin.dashboard'
            ];
            
            foreach ($subscriberPermissions as $permSlug) {
                if (isset($permissionsBySlug[$permSlug])) {
                    $subscriberRole->assignPermission($permissionsBySlug[$permSlug]['id']);
                }
            }
        }
    }
    
    private function createDefaultAdmin()
    {
        $db = app()->getDatabase();
        
        // Check if admin user already exists
        $existingAdmin = $db->selectOne("SELECT * FROM users WHERE email = '<EMAIL>'");
        
        if (!$existingAdmin) {
            // Create admin user
            $adminData = [
                'first_name' => 'System',
                'last_name' => 'Administrator',
                'email' => '<EMAIL>',
                'username' => 'admin',
                'password' => password_hash('admin123', PASSWORD_DEFAULT),
                'status' => 'active',
                'email_verified_at' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $adminId = $db->insert('users', $adminData);
            
            if ($adminId) {
                // Assign super admin role
                $superAdminRole = $db->selectOne("SELECT * FROM roles WHERE slug = 'super_admin'");
                if ($superAdminRole) {
                    $db->insert('user_roles', [
                        'user_id' => $adminId,
                        'role_id' => $superAdminRole['id'],
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                }
                
                echo "Default admin user created:\n";
                echo "Email: <EMAIL>\n";
                echo "Password: admin123\n";
                echo "Please change the password after first login!\n";
            }
        } else {
            echo "Admin user already exists.\n";
        }
    }
}

// Run the seeder if called directly
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    require_once __DIR__ . '/../../bootstrap/app.php';
    
    $seeder = new RolePermissionSeeder();
    $seeder->run();
}
