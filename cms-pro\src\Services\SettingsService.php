<?php

namespace CmsPro\Services;

use CmsPro\Core\Database;

/**
 * Settings Service
 * 
 * Manages system settings and configuration
 */
class SettingsService
{
    private $db;
    private $cache = [];
    private $cacheLoaded = false;

    public function __construct()
    {
        $this->db = app()->getDatabase();
    }

    /**
     * Get setting value
     */
    public function get($key, $default = null)
    {
        $this->loadCache();
        
        return $this->cache[$key] ?? $default;
    }

    /**
     * Set setting value
     */
    public function set($key, $value)
    {
        return $this->updateSetting($key, $value);
    }

    /**
     * Update setting
     */
    public function updateSetting($key, $value)
    {
        try {
            // Check if setting exists
            $existing = $this->db->selectOne(
                "SELECT id FROM settings WHERE setting_key = ?",
                [$key]
            );

            if ($existing) {
                // Update existing setting
                $result = $this->db->update(
                    "UPDATE settings SET setting_value = ?, updated_at = ? WHERE setting_key = ?",
                    [json_encode($value), date('Y-m-d H:i:s'), $key]
                );
            } else {
                // Create new setting
                $result = $this->db->insert('settings', [
                    'setting_key' => $key,
                    'setting_value' => json_encode($value),
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }

            if ($result) {
                // Update cache
                $this->cache[$key] = $value;
                return true;
            }

            return false;

        } catch (\Exception $e) {
            error_log("Settings update error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all settings grouped by category
     */
    public function getAllSettings()
    {
        $this->loadCache();

        // Define setting categories and their default values
        $categories = [
            'general' => [
                'site_name' => 'CMS Pro',
                'site_description' => 'A powerful content management system',
                'site_url' => 'http://localhost',
                'admin_email' => '<EMAIL>',
                'timezone' => 'UTC',
                'date_format' => 'Y-m-d',
                'time_format' => 'H:i:s',
                'language' => 'en',
                'maintenance_mode' => false,
                'site_logo' => '',
                'site_favicon' => ''
            ],
            'seo' => [
                'seo_title' => '',
                'seo_description' => '',
                'seo_keywords' => '',
                'robots_txt' => "User-agent: *\nDisallow:",
                'google_analytics' => '',
                'google_search_console' => '',
                'facebook_pixel' => ''
            ],
            'media' => [
                'max_upload_size' => 10, // MB
                'allowed_file_types' => 'jpg,jpeg,png,gif,pdf,doc,docx,txt',
                'image_quality' => 85,
                'thumbnail_width' => 300,
                'thumbnail_height' => 300
            ],
            'email' => [
                'mail_driver' => 'smtp',
                'mail_host' => 'localhost',
                'mail_port' => 587,
                'mail_username' => '',
                'mail_password' => '',
                'mail_encryption' => 'tls',
                'mail_from_address' => '<EMAIL>',
                'mail_from_name' => 'CMS Pro'
            ],
            'security' => [
                'password_min_length' => 8,
                'password_require_uppercase' => true,
                'password_require_lowercase' => true,
                'password_require_numbers' => true,
                'password_require_symbols' => false,
                'session_lifetime' => 120, // minutes
                'max_login_attempts' => 5,
                'lockout_duration' => 15 // minutes
            ],
            'advanced' => [
                'debug_mode' => false,
                'log_level' => 'error',
                'cache_enabled' => true,
                'cache_lifetime' => 60, // minutes
                'compression_enabled' => true
            ]
        ];

        // Merge with actual values from database
        $result = [];
        foreach ($categories as $category => $defaults) {
            $result[$category] = [];
            foreach ($defaults as $key => $defaultValue) {
                $result[$category][$key] = $this->get($key, $defaultValue);
            }
        }

        return $result;
    }

    /**
     * Test email configuration
     */
    public function testEmailConfiguration($testEmail)
    {
        try {
            $emailSettings = $this->getAllSettings()['email'];
            
            // Create a simple test email
            $subject = 'CMS Pro Email Test';
            $message = 'This is a test email from CMS Pro. If you received this, your email configuration is working correctly.';
            
            // Use PHP's mail function for testing (in production, use a proper mailer)
            $headers = [
                'From: ' . $emailSettings['mail_from_name'] . ' <' . $emailSettings['mail_from_address'] . '>',
                'Reply-To: ' . $emailSettings['mail_from_address'],
                'Content-Type: text/html; charset=UTF-8'
            ];
            
            $success = mail($testEmail, $subject, $message, implode("\r\n", $headers));
            
            if ($success) {
                return [
                    'success' => true,
                    'message' => 'Test email sent successfully to ' . $testEmail
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to send test email. Please check your email configuration.'
                ];
            }
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Email test failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Clear cache
     */
    public function clearCache($type = 'all')
    {
        try {
            switch ($type) {
                case 'settings':
                    $this->cache = [];
                    $this->cacheLoaded = false;
                    break;
                    
                case 'twig':
                    // Clear Twig cache if exists
                    $twigCacheDir = storage_path('cache/twig');
                    if (is_dir($twigCacheDir)) {
                        $this->deleteDirectory($twigCacheDir);
                    }
                    break;
                    
                case 'all':
                default:
                    // Clear settings cache
                    $this->cache = [];
                    $this->cacheLoaded = false;
                    
                    // Clear Twig cache
                    $twigCacheDir = storage_path('cache/twig');
                    if (is_dir($twigCacheDir)) {
                        $this->deleteDirectory($twigCacheDir);
                    }
                    
                    // Clear other caches
                    $cacheDir = storage_path('cache');
                    if (is_dir($cacheDir)) {
                        $files = glob($cacheDir . '/*.cache');
                        foreach ($files as $file) {
                            if (is_file($file)) {
                                unlink($file);
                            }
                        }
                    }
                    break;
            }
            
            return [
                'success' => true,
                'message' => ucfirst($type) . ' cache cleared successfully.'
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to clear cache: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get setting categories for UI
     */
    public function getCategories()
    {
        return [
            'general' => [
                'name' => __('General'),
                'icon' => 'fas fa-cog',
                'description' => __('Basic site configuration')
            ],
            'seo' => [
                'name' => __('SEO'),
                'icon' => 'fas fa-search',
                'description' => __('Search engine optimization')
            ],
            'media' => [
                'name' => __('Media'),
                'icon' => 'fas fa-images',
                'description' => __('File upload and media settings')
            ],
            'email' => [
                'name' => __('Email'),
                'icon' => 'fas fa-envelope',
                'description' => __('Email configuration and SMTP settings')
            ],
            'security' => [
                'name' => __('Security'),
                'icon' => 'fas fa-shield-alt',
                'description' => __('Password policies and security settings')
            ],
            'advanced' => [
                'name' => __('Advanced'),
                'icon' => 'fas fa-code',
                'description' => __('Advanced system configuration')
            ]
        ];
    }

    /**
     * Load settings cache
     */
    private function loadCache()
    {
        if ($this->cacheLoaded) {
            return;
        }

        try {
            $settings = $this->db->select("SELECT setting_key, setting_value FROM settings");
            
            foreach ($settings as $setting) {
                $this->cache[$setting['setting_key']] = json_decode($setting['setting_value'], true);
            }
            
            $this->cacheLoaded = true;
            
        } catch (\Exception $e) {
            error_log("Settings cache load error: " . $e->getMessage());
        }
    }

    /**
     * Delete directory recursively
     */
    private function deleteDirectory($dir)
    {
        if (!is_dir($dir)) {
            return false;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;
            if (is_dir($path)) {
                $this->deleteDirectory($path);
            } else {
                unlink($path);
            }
        }
        
        return rmdir($dir);
    }

    /**
     * Get timezone list
     */
    public function getTimezones()
    {
        $timezones = [];
        $identifiers = \DateTimeZone::listIdentifiers();
        
        foreach ($identifiers as $identifier) {
            $timezone = new \DateTimeZone($identifier);
            $datetime = new \DateTime('now', $timezone);
            $offset = $datetime->format('P');
            
            $timezones[$identifier] = '(UTC' . $offset . ') ' . str_replace('_', ' ', $identifier);
        }
        
        return $timezones;
    }

    /**
     * Get available languages
     */
    public function getLanguages()
    {
        return [
            'en' => 'English',
            'tr' => 'Türkçe',
            'es' => 'Español',
            'fr' => 'Français',
            'de' => 'Deutsch',
            'it' => 'Italiano',
            'pt' => 'Português',
            'ru' => 'Русский',
            'zh' => '中文',
            'ja' => '日本語'
        ];
    }

    /**
     * Get date formats
     */
    public function getDateFormats()
    {
        return [
            'Y-m-d' => date('Y-m-d') . ' (2023-12-25)',
            'm/d/Y' => date('m/d/Y') . ' (12/25/2023)',
            'd/m/Y' => date('d/m/Y') . ' (25/12/2023)',
            'F j, Y' => date('F j, Y') . ' (December 25, 2023)',
            'j F Y' => date('j F Y') . ' (25 December 2023)',
            'M j, Y' => date('M j, Y') . ' (Dec 25, 2023)',
            'j M Y' => date('j M Y') . ' (25 Dec 2023)'
        ];
    }

    /**
     * Get time formats
     */
    public function getTimeFormats()
    {
        return [
            'H:i:s' => date('H:i:s') . ' (24-hour with seconds)',
            'H:i' => date('H:i') . ' (24-hour)',
            'g:i:s A' => date('g:i:s A') . ' (12-hour with seconds)',
            'g:i A' => date('g:i A') . ' (12-hour)'
        ];
    }
}
