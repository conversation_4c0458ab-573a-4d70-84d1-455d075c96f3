<?php

/**
 * Performance Configuration
 * 
 * Settings for optimizing application performance
 */

return [
    
    /*
    |--------------------------------------------------------------------------
    | Cache Configuration
    |--------------------------------------------------------------------------
    */
    'cache' => [
        'default_ttl' => 3600, // 1 hour
        'page_cache_ttl' => 7200, // 2 hours
        'api_cache_ttl' => 1800, // 30 minutes
        'search_cache_ttl' => 900, // 15 minutes
        'analytics_cache_ttl' => 3600, // 1 hour
        
        'tags' => [
            'pages' => 'pages_cache',
            'blog' => 'blog_cache',
            'users' => 'users_cache',
            'settings' => 'settings_cache',
            'media' => 'media_cache'
        ]
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Database Optimization
    |--------------------------------------------------------------------------
    */
    'database' => [
        'query_cache' => true,
        'connection_pooling' => true,
        'slow_query_log' => true,
        'slow_query_threshold' => 2.0, // seconds
        
        'indexes' => [
            'pages' => ['slug', 'status', 'author_id', 'created_at'],
            'blog_posts' => ['slug', 'status', 'author_id', 'category_id', 'published_at'],
            'users' => ['email', 'status', 'role_id'],
            'media' => ['file_type', 'folder', 'created_at'],
            'activity_logs' => ['user_id', 'action', 'created_at']
        ]
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Asset Optimization
    |--------------------------------------------------------------------------
    */
    'assets' => [
        'minify_css' => true,
        'minify_js' => true,
        'combine_files' => true,
        'gzip_compression' => true,
        'browser_cache_ttl' => 2592000, // 30 days
        
        'cdn' => [
            'enabled' => false,
            'url' => 'https://cdn.example.com',
            'assets' => ['css', 'js', 'images']
        ]
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Image Optimization
    |--------------------------------------------------------------------------
    */
    'images' => [
        'auto_optimize' => true,
        'quality' => 85,
        'progressive_jpeg' => true,
        'webp_conversion' => true,
        'lazy_loading' => true,
        
        'sizes' => [
            'thumbnail' => [150, 150],
            'medium' => [300, 300],
            'large' => [800, 600],
            'hero' => [1200, 600]
        ]
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Memory Management
    |--------------------------------------------------------------------------
    */
    'memory' => [
        'limit' => '256M',
        'gc_probability' => 1,
        'gc_divisor' => 100,
        'max_execution_time' => 300
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Session Optimization
    |--------------------------------------------------------------------------
    */
    'session' => [
        'gc_maxlifetime' => 7200, // 2 hours
        'cookie_lifetime' => 0, // Browser session
        'regenerate_id' => true,
        'use_strict_mode' => true
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Output Compression
    |--------------------------------------------------------------------------
    */
    'compression' => [
        'enabled' => true,
        'level' => 6, // 1-9, higher = better compression but more CPU
        'min_length' => 1024, // Don't compress files smaller than 1KB
        'types' => [
            'text/html',
            'text/css',
            'text/javascript',
            'application/javascript',
            'application/json',
            'application/xml'
        ]
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Monitoring
    |--------------------------------------------------------------------------
    */
    'monitoring' => [
        'enabled' => true,
        'log_slow_requests' => true,
        'slow_request_threshold' => 3.0, // seconds
        'memory_usage_threshold' => 0.8, // 80% of memory limit
        
        'metrics' => [
            'response_time' => true,
            'memory_usage' => true,
            'database_queries' => true,
            'cache_hit_ratio' => true
        ]
    ]
];
