<?php

namespace CmsPro\Services;

/**
 * Cache Service
 * 
 * Simple file-based caching system
 */
class CacheService
{
    private $cacheDir;
    private $defaultTtl;

    public function __construct()
    {
        $this->cacheDir = storage_path('cache');
        $this->defaultTtl = 3600; // 1 hour default
        
        // Create cache directory if it doesn't exist
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }

    /**
     * Get cached value
     */
    public function get($key, $default = null)
    {
        $filename = $this->getCacheFilename($key);
        
        if (!file_exists($filename)) {
            return $default;
        }
        
        $data = file_get_contents($filename);
        $cache = unserialize($data);
        
        // Check if cache has expired
        if ($cache['expires'] < time()) {
            $this->delete($key);
            return $default;
        }
        
        return $cache['value'];
    }

    /**
     * Set cached value
     */
    public function set($key, $value, $ttl = null)
    {
        $ttl = $ttl ?: $this->defaultTtl;
        $filename = $this->getCacheFilename($key);
        
        $cache = [
            'value' => $value,
            'expires' => time() + $ttl,
            'created' => time()
        ];
        
        return file_put_contents($filename, serialize($cache)) !== false;
    }

    /**
     * Check if key exists in cache
     */
    public function has($key)
    {
        return $this->get($key) !== null;
    }

    /**
     * Delete cached value
     */
    public function delete($key)
    {
        $filename = $this->getCacheFilename($key);
        
        if (file_exists($filename)) {
            return unlink($filename);
        }
        
        return true;
    }

    /**
     * Clear all cache
     */
    public function clear()
    {
        $files = glob($this->cacheDir . '/*.cache');
        $cleared = 0;
        
        foreach ($files as $file) {
            if (unlink($file)) {
                $cleared++;
            }
        }
        
        return $cleared;
    }

    /**
     * Clear expired cache entries
     */
    public function clearExpired()
    {
        $files = glob($this->cacheDir . '/*.cache');
        $cleared = 0;
        
        foreach ($files as $file) {
            $data = file_get_contents($file);
            $cache = unserialize($data);
            
            if ($cache['expires'] < time()) {
                if (unlink($file)) {
                    $cleared++;
                }
            }
        }
        
        return $cleared;
    }

    /**
     * Get cache statistics
     */
    public function getStats()
    {
        $files = glob($this->cacheDir . '/*.cache');
        $totalSize = 0;
        $expired = 0;
        $active = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
            
            $data = file_get_contents($file);
            $cache = unserialize($data);
            
            if ($cache['expires'] < time()) {
                $expired++;
            } else {
                $active++;
            }
        }
        
        return [
            'total_files' => count($files),
            'active_files' => $active,
            'expired_files' => $expired,
            'total_size' => $totalSize,
            'total_size_formatted' => $this->formatBytes($totalSize)
        ];
    }

    /**
     * Remember a value (get from cache or execute callback)
     */
    public function remember($key, $callback, $ttl = null)
    {
        $value = $this->get($key);
        
        if ($value !== null) {
            return $value;
        }
        
        $value = $callback();
        $this->set($key, $value, $ttl);
        
        return $value;
    }

    /**
     * Get cache filename for key
     */
    private function getCacheFilename($key)
    {
        $hash = md5($key);
        return $this->cacheDir . '/' . $hash . '.cache';
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Set default TTL
     */
    public function setDefaultTtl($ttl)
    {
        $this->defaultTtl = $ttl;
    }

    /**
     * Get default TTL
     */
    public function getDefaultTtl()
    {
        return $this->defaultTtl;
    }

    /**
     * Increment a cached value
     */
    public function increment($key, $value = 1)
    {
        $current = $this->get($key, 0);
        $new = $current + $value;
        $this->set($key, $new);
        
        return $new;
    }

    /**
     * Decrement a cached value
     */
    public function decrement($key, $value = 1)
    {
        $current = $this->get($key, 0);
        $new = $current - $value;
        $this->set($key, $new);
        
        return $new;
    }

    /**
     * Get multiple cached values
     */
    public function getMultiple($keys, $default = null)
    {
        $result = [];
        
        foreach ($keys as $key) {
            $result[$key] = $this->get($key, $default);
        }
        
        return $result;
    }

    /**
     * Set multiple cached values
     */
    public function setMultiple($values, $ttl = null)
    {
        $success = true;
        
        foreach ($values as $key => $value) {
            if (!$this->set($key, $value, $ttl)) {
                $success = false;
            }
        }
        
        return $success;
    }

    /**
     * Delete multiple cached values
     */
    public function deleteMultiple($keys)
    {
        $success = true;
        
        foreach ($keys as $key) {
            if (!$this->delete($key)) {
                $success = false;
            }
        }
        
        return $success;
    }

    /**
     * Tag-based cache invalidation
     */
    public function tags($tags)
    {
        return new TaggedCache($this, $tags);
    }

    /**
     * Flush cache by pattern
     */
    public function flush($pattern = '*')
    {
        $files = glob($this->cacheDir . '/' . $pattern . '.cache');
        $flushed = 0;
        
        foreach ($files as $file) {
            if (unlink($file)) {
                $flushed++;
            }
        }
        
        return $flushed;
    }
}

/**
 * Tagged Cache Helper
 */
class TaggedCache
{
    private $cache;
    private $tags;

    public function __construct(CacheService $cache, $tags)
    {
        $this->cache = $cache;
        $this->tags = is_array($tags) ? $tags : [$tags];
    }

    public function get($key, $default = null)
    {
        return $this->cache->get($this->taggedKey($key), $default);
    }

    public function set($key, $value, $ttl = null)
    {
        $taggedKey = $this->taggedKey($key);
        
        // Store the key in tag index
        foreach ($this->tags as $tag) {
            $tagKeys = $this->cache->get("tag:{$tag}", []);
            $tagKeys[] = $taggedKey;
            $this->cache->set("tag:{$tag}", array_unique($tagKeys));
        }
        
        return $this->cache->set($taggedKey, $value, $ttl);
    }

    public function flush()
    {
        foreach ($this->tags as $tag) {
            $tagKeys = $this->cache->get("tag:{$tag}", []);
            
            foreach ($tagKeys as $key) {
                $this->cache->delete($key);
            }
            
            $this->cache->delete("tag:{$tag}");
        }
    }

    private function taggedKey($key)
    {
        return 'tagged:' . implode(':', $this->tags) . ':' . $key;
    }
}
