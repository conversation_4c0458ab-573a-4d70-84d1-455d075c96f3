<?php

namespace CmsPro\Controllers\Admin;

use CmsPro\Controllers\BaseController;
use CmsPro\Models\ActivityLog;
use CmsPro\Models\User;
use CmsPro\Services\ActivityLogger;
use Symfony\Component\HttpFoundation\Request;

/**
 * Activity Log Admin Controller
 * 
 * @package CmsPro\Controllers\Admin
 */
class ActivityLogController extends BaseController
{
    private $activityLogger;

    public function __construct()
    {
        parent::__construct();
        $this->activityLogger = new ActivityLogger();
    }

    /**
     * Display activity logs
     */
    public function index(Request $request)
    {
        $this->request = $request;

        // Check permission
        if (!auth()->can('logs.view')) {
            return $this->forbidden('You do not have permission to view activity logs.');
        }

        $page = (int) $request->query->get('page', 1);
        $perPage = (int) $request->query->get('per_page', 25);

        // Get filters
        $filters = [
            'user_id' => $request->query->get('user_id'),
            'action' => $request->query->get('action'),
            'date_from' => $request->query->get('date_from'),
            'date_to' => $request->query->get('date_to'),
            'ip_address' => $request->query->get('ip_address'),
            'search' => $request->query->get('search')
        ];

        // Remove empty filters
        $filters = array_filter($filters);

        // Get paginated logs
        $logs = ActivityLog::paginate($page, $perPage, $filters);

        // Get filter options
        $users = User::all();
        $actions = ActivityLog::getUniqueActions();

        $data = [
            'title' => __('Activity Logs'),
            'meta_description' => __('View system activity logs'),
            'logs' => $logs,
            'users' => $users,
            'actions' => $actions,
            'filters' => $filters,
            'current_page' => $page,
            'per_page' => $perPage
        ];

        return $this->view('admin/activity-logs/index.twig', $data);
    }

    /**
     * Show activity log details
     */
    public function show(Request $request, $parameters)
    {
        $this->request = $request;
        $id = $parameters['id'] ?? null;

        // Check permission
        if (!auth()->can('logs.view')) {
            return $this->forbidden('You do not have permission to view activity logs.');
        }

        $log = ActivityLog::find($id);

        if (!$log) {
            $this->flashError(__('Activity log not found.'));
            return $this->redirectToRoute('admin.activity-logs.index');
        }

        $data = [
            'title' => __('Activity Log Details'),
            'meta_description' => __('View activity log details'),
            'log' => $log
        ];

        return $this->view('admin/activity-logs/show.twig', $data);
    }

    /**
     * Get activity statistics
     */
    public function statistics(Request $request)
    {
        $this->request = $request;

        // Check permission
        if (!auth()->can('logs.view')) {
            return $this->forbidden('You do not have permission to view activity logs.');
        }

        $days = (int) $request->query->get('days', 7);
        $stats = ActivityLog::getStatistics($days);

        $data = [
            'title' => __('Activity Statistics'),
            'meta_description' => __('View activity statistics'),
            'stats' => $stats,
            'days' => $days
        ];

        return $this->view('admin/activity-logs/statistics.twig', $data);
    }

    /**
     * Export activity logs
     */
    public function export(Request $request)
    {
        $this->request = $request;

        // Check permission
        if (!auth()->can('logs.view')) {
            return $this->forbidden('You do not have permission to export activity logs.');
        }

        $format = $request->query->get('format', 'csv');
        $filters = [
            'user_id' => $request->query->get('user_id'),
            'action' => $request->query->get('action'),
            'date_from' => $request->query->get('date_from'),
            'date_to' => $request->query->get('date_to'),
            'ip_address' => $request->query->get('ip_address'),
            'search' => $request->query->get('search')
        ];

        // Remove empty filters
        $filters = array_filter($filters);

        // Get all matching logs (no pagination for export)
        $logs = ActivityLog::paginate(1, 10000, $filters);

        if ($format === 'csv') {
            return $this->exportCsv($logs['data']);
        } elseif ($format === 'json') {
            return $this->exportJson($logs['data']);
        }

        $this->flashError(__('Invalid export format.'));
        return $this->back();
    }

    /**
     * Clean old logs
     */
    public function clean(Request $request)
    {
        $this->request = $request;

        // Check permission
        if (!auth()->can('logs.manage')) {
            return $this->forbidden('You do not have permission to manage activity logs.');
        }

        $days = (int) $request->request->get('days', 90);

        if ($days < 30) {
            $this->flashError(__('Cannot delete logs newer than 30 days.'));
            return $this->back();
        }

        try {
            $deleted = ActivityLog::deleteOld($days);
            
            // Log this action
            $this->activityLogger->logSystem('logs_cleaned', [
                'days' => $days,
                'deleted_count' => $deleted
            ]);

            $this->flashSuccess(__('Successfully deleted :count old log entries.', ['count' => $deleted]));

        } catch (\Exception $e) {
            $this->flashError(__('Error cleaning logs: :error', ['error' => $e->getMessage()]));
        }

        return $this->back();
    }

    /**
     * Export logs as CSV
     */
    private function exportCsv($logs)
    {
        $filename = 'activity_logs_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, [
            'ID',
            'User',
            'Action',
            'Description',
            'IP Address',
            'User Agent',
            'Created At'
        ]);
        
        // CSV data
        foreach ($logs as $log) {
            $user = $log->getUser();
            fputcsv($output, [
                $log->getId(),
                $user ? $user['name'] . ' (' . $user['username'] . ')' : 'System',
                $log->getAction(),
                $log->getDescription(),
                $log->getIpAddress(),
                $log->getUserAgent(),
                $log->getCreatedAt()
            ]);
        }
        
        fclose($output);
        exit;
    }

    /**
     * Export logs as JSON
     */
    private function exportJson($logs)
    {
        $filename = 'activity_logs_' . date('Y-m-d_H-i-s') . '.json';
        
        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $data = [];
        foreach ($logs as $log) {
            $data[] = [
                'id' => $log->getId(),
                'user' => $log->getUser(),
                'action' => $log->getAction(),
                'description' => $log->getDescription(),
                'ip_address' => $log->getIpAddress(),
                'user_agent' => $log->getUserAgent(),
                'properties' => $log->getProperties(),
                'created_at' => $log->getCreatedAt()
            ];
        }
        
        echo json_encode($data, JSON_PRETTY_PRINT);
        exit;
    }
}
