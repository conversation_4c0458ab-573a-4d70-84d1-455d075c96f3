<?php

namespace CmsPro\Controllers\Auth;

use CmsPro\Controllers\BaseController;
use CmsPro\Core\Auth;
use CmsPro\Services\RateLimitingService;
use CmsPro\Services\SecurityService;
use CmsPro\Services\ActivityLogger;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Login Controller
 * 
 * @package CmsPro\Controllers\Auth
 */
class LoginController extends BaseController
{
    private $auth;
    private $rateLimitingService;
    private $securityService;
    private $activityLogger;

    public function __construct()
    {
        parent::__construct();
        $this->auth = new Auth();
        $this->rateLimitingService = new RateLimitingService();
        $this->securityService = new SecurityService();
        $this->activityLogger = new ActivityLogger();
    }

    /**
     * Show login form
     */
    public function showLoginForm(Request $request)
    {
        $this->request = $request;

        // Redirect if already authenticated
        if ($this->auth->check()) {
            return $this->redirectToRoute('home');
        }

        $data = [
            'title' => __('Login'),
            'meta_description' => __('Login to your account'),
        ];

        return $this->view('auth/login.twig', $data);
    }

    /**
     * Handle login attempt - SECURITY ENHANCED
     */
    public function login(Request $request)
    {
        $this->request = $request;

        try {
            // Validate CSRF token
            if (!$this->validateCsrfToken($request)) {
                $this->logSecurityEvent('login_csrf_failure', $request);

                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Invalid CSRF token'
                    ], 403);
                }

                $this->flashError(__('Security token mismatch. Please try again.'));
                return $this->back();
            }

            // Redirect if already authenticated
            if ($this->auth->check()) {
                return $this->redirectToRoute('dashboard');
            }

            $ipAddress = $request->getClientIp();
            $userAgent = $request->headers->get('User-Agent');

            // Check rate limiting
            if (!$this->rateLimitingService->attempt('login:' . $ipAddress, 5, 300)) { // 5 attempts per 5 minutes
                $this->logSecurityEvent('login_rate_limit_exceeded', $request);

                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Too many login attempts. Please try again later.'
                    ], 429);
                }

                $this->flashError(__('Too many login attempts. Please try again in 5 minutes.'));
                return $this->back();
            }

            // Validate and sanitize input
            $credentials = $this->validate($request, [
                'login' => 'required|string|max:255',
                'password' => 'required|string|min:6|max:255'
            ]);

            // Sanitize login input
            $login = $this->securityService->sanitizeInput($credentials['login']);
            $password = $credentials['password'];
            $remember = (bool) $request->request->get('remember', false);

            // Check for suspicious patterns
            if ($this->detectSuspiciousLogin($login, $password, $request)) {
                $this->logSecurityEvent('suspicious_login_attempt', $request, [
                    'login' => $login,
                    'patterns_detected' => true
                ]);

                $this->flashError(__('Invalid credentials. Please try again.'));
                return $this->back();
            }

            // Attempt login
            $loginResult = $this->auth->attempt([
                'email' => $login,
                'username' => $login,
                'password' => $password
            ], $remember);

            if ($loginResult) {
                // Clear rate limiting on successful login
                $this->rateLimitingService->clear('login:' . $ipAddress);

                // Log successful login
                $this->activityLogger->log('user_login', [
                    'user_id' => $this->auth->id(),
                    'ip_address' => $ipAddress,
                    'user_agent' => $userAgent,
                    'remember' => $remember
                ]);

                // Check if 2FA is enabled
                if ($this->auth->user()->two_factor_enabled) {
                    session()->put('2fa_user_id', $this->auth->id());
                    $this->auth->logout(); // Logout until 2FA is verified

                    if ($request->isXmlHttpRequest()) {
                        return new JsonResponse([
                            'success' => true,
                            'requires_2fa' => true,
                            'redirect' => route('2fa.show')
                        ]);
                    }

                    return $this->redirectToRoute('2fa.show');
                }

                $this->flashSuccess(__('Login successful! Welcome back.'));

                // Redirect to intended URL or dashboard
                $intended = session()->get('intended_url', route('dashboard'));
                session()->forget('intended_url');

                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => true,
                        'redirect' => $intended
                    ]);
                }

                return $this->redirect($intended);

            } else {
                // Log failed login attempt
                $this->logSecurityEvent('login_failed', $request, [
                    'login' => $login
                ]);

                $this->flashError(__('Invalid credentials. Please try again.'));
            }

        } catch (\Exception $e) {
            // Log error
            $this->logSecurityEvent('login_error', $request, [
                'error' => $e->getMessage()
            ]);

            $this->flashError(__('An error occurred during login. Please try again.'));
        }

        // Flash input for form repopulation (excluding password)
        $flashData = $request->request->all();
        unset($flashData['password']);
        session()->flashInput($flashData);

        if ($request->isXmlHttpRequest()) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Login failed'
            ], 400);
        }

        return $this->back();
    }

    /**
     * Handle logout - SECURITY ENHANCED
     */
    public function logout(Request $request)
    {
        $this->request = $request;

        try {
            // Validate CSRF token for logout
            if (!$this->validateCsrfToken($request)) {
                $this->logSecurityEvent('logout_csrf_failure', $request);

                if ($request->isXmlHttpRequest()) {
                    return new JsonResponse([
                        'success' => false,
                        'message' => 'Invalid CSRF token'
                    ], 403);
                }

                $this->flashError(__('Security token mismatch.'));
                return $this->back();
            }

            // Log logout activity
            if ($this->auth->check()) {
                $this->activityLogger->log('user_logout', [
                    'user_id' => $this->auth->id(),
                    'ip_address' => $request->getClientIp(),
                    'user_agent' => $request->headers->get('User-Agent')
                ]);
            }

            // Perform logout
            $this->auth->logout();

            // Clear all sessions
            session()->invalidate();
            session()->regenerateToken();

            $this->flashSuccess(__('You have been logged out successfully.'));

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => true,
                    'redirect' => route('home')
                ]);
            }

            return $this->redirectToRoute('home');

        } catch (\Exception $e) {
            $this->logSecurityEvent('logout_error', $request, [
                'error' => $e->getMessage()
            ]);

            // Force logout even on error
            $this->auth->logout();
            session()->invalidate();

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'success' => true,
                    'redirect' => route('home')
                ]);
            }

            return $this->redirectToRoute('home');
        }
    }

    /**
     * Detect suspicious login patterns
     */
    private function detectSuspiciousLogin($login, $password, Request $request)
    {
        // Check for SQL injection patterns
        $sqlPatterns = [
            '/(\bor\b|\band\b).*[\'"].*[\'"]/',
            '/union.*select/i',
            '/drop.*table/i',
            '/insert.*into/i',
            '/update.*set/i',
            '/delete.*from/i'
        ];

        foreach ($sqlPatterns as $pattern) {
            if (preg_match($pattern, $login) || preg_match($pattern, $password)) {
                return true;
            }
        }

        // Check for XSS patterns
        $xssPatterns = [
            '/<script[^>]*>/i',
            '/javascript:/i',
            '/vbscript:/i',
            '/on\w+\s*=/i'
        ];

        foreach ($xssPatterns as $pattern) {
            if (preg_match($pattern, $login)) {
                return true;
            }
        }

        // Check for unusual characters
        if (preg_match('/[<>"\'\\\]/', $login)) {
            return true;
        }

        // Check for extremely long inputs (potential buffer overflow)
        if (strlen($login) > 255 || strlen($password) > 255) {
            return true;
        }

        return false;
    }

    /**
     * Validate CSRF token
     */
    private function validateCsrfToken(Request $request)
    {
        $token = $request->request->get('_token') ?: $request->headers->get('X-CSRF-TOKEN');

        if (!$token) {
            return false;
        }

        $sessionToken = session()->get('_token');
        return $sessionToken && hash_equals($sessionToken, $token);
    }

    /**
     * Log security events
     */
    private function logSecurityEvent($event, Request $request, $additionalData = [])
    {
        try {
            $logData = array_merge([
                'event' => $event,
                'timestamp' => date('Y-m-d H:i:s'),
                'ip_address' => $request->getClientIp(),
                'user_agent' => $request->headers->get('User-Agent'),
                'url' => $request->getUri(),
                'method' => $request->getMethod()
            ], $additionalData);

            // Log to security log file
            error_log(json_encode($logData), 3, storage_path('logs/security.log'));

            // Also log to activity logger if available
            if ($this->activityLogger) {
                $this->activityLogger->log($event, $logData);
            }

        } catch (\Exception $e) {
            // Fail silently to not break the main functionality
            error_log('Failed to log security event: ' . $e->getMessage());
        }
    }
}
