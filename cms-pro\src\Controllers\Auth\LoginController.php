<?php

namespace CmsPro\Controllers\Auth;

use CmsPro\Controllers\BaseController;
use CmsPro\Core\Auth;
use Symfony\Component\HttpFoundation\Request;

/**
 * Login Controller
 * 
 * @package CmsPro\Controllers\Auth
 */
class LoginController extends BaseController
{
    private $auth;

    public function __construct()
    {
        parent::__construct();
        $this->auth = new Auth();
    }

    /**
     * Show login form
     */
    public function showLoginForm(Request $request)
    {
        $this->request = $request;

        // Redirect if already authenticated
        if ($this->auth->check()) {
            return $this->redirectToRoute('home');
        }

        $data = [
            'title' => __('Login'),
            'meta_description' => __('Login to your account'),
        ];

        return $this->view('auth/login.twig', $data);
    }

    /**
     * Handle login attempt
     */
    public function login(Request $request)
    {
        $this->request = $request;

        // Redirect if already authenticated
        if ($this->auth->check()) {
            return $this->redirectToRoute('home');
        }

        // Validate input
        $credentials = $this->validate($request, [
            'login' => 'required',
            'password' => 'required'
        ]);

        $remember = $request->request->get('remember', false);

        try {
            // Attempt login
            if ($this->auth->attempt([
                'email' => $credentials['login'],
                'username' => $credentials['login'],
                'password' => $credentials['password']
            ], $remember)) {
                
                $this->flashSuccess(__('Login successful! Welcome back.'));
                
                // Redirect to intended URL or dashboard
                $intended = session()->get('intended_url', '/');
                session()->forget('intended_url');
                
                return $this->redirect($intended);
            } else {
                $this->flashError(__('Invalid credentials. Please try again.'));
            }
        } catch (\Exception $e) {
            $this->flashError($e->getMessage());
        }

        // Flash input for form repopulation
        session()->flashInput($request->request->all());

        return $this->back();
    }

    /**
     * Handle logout
     */
    public function logout(Request $request)
    {
        $this->request = $request;

        $this->auth->logout();
        $this->flashSuccess(__('You have been logged out successfully.'));

        return $this->redirectToRoute('home');
    }
}
