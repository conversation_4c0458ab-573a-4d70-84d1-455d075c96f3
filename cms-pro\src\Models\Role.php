<?php

namespace CmsPro\Models;

use CmsPro\Core\Database;

/**
 * Role Model
 * 
 * @package CmsPro\Models
 */
class Role
{
    private $db;
    private $data = [];

    public function __construct(Database $db = null)
    {
        $this->db = $db ?: app()->getDatabase();
    }

    /**
     * Find role by ID
     */
    public static function find($id)
    {
        $instance = new static();
        $roleData = $instance->db->selectOne(
            "SELECT * FROM roles WHERE id = ?",
            [$id]
        );

        if ($roleData) {
            $instance->data = $roleData;
            return $instance;
        }

        return null;
    }

    /**
     * Find role by slug
     */
    public static function findBySlug($slug)
    {
        $instance = new static();
        $roleData = $instance->db->selectOne(
            "SELECT * FROM roles WHERE slug = ?",
            [$slug]
        );

        if ($roleData) {
            $instance->data = $roleData;
            return $instance;
        }

        return null;
    }

    /**
     * Get default role
     */
    public static function getDefault()
    {
        $instance = new static();
        $roleData = $instance->db->selectOne(
            "SELECT * FROM roles WHERE is_default = 1"
        );

        if ($roleData) {
            $instance->data = $roleData;
            return $instance;
        }

        return null;
    }

    /**
     * Get all roles
     */
    public static function all()
    {
        $instance = new static();
        $rolesData = $instance->db->select("SELECT * FROM roles ORDER BY name");
        
        $roles = [];
        foreach ($rolesData as $roleData) {
            $role = new static();
            $role->data = $roleData;
            $roles[] = $role;
        }
        
        return $roles;
    }

    /**
     * Create new role
     */
    public static function create($data)
    {
        $instance = new static();
        
        // Generate slug if not provided
        if (!isset($data['slug'])) {
            $data['slug'] = strtolower(str_replace(' ', '-', $data['name']));
        }

        $instance->db->insert('roles', $data);
        $roleId = $instance->db->lastInsertId();

        return static::find($roleId);
    }

    /**
     * Update role
     */
    public function update($data)
    {
        $this->db->update('roles', $data, 'id = ?', [$this->getId()]);
        
        // Refresh data
        $this->data = array_merge($this->data, $data);
        
        return $this;
    }

    /**
     * Delete role
     */
    public function delete()
    {
        // Don't allow deletion if users are assigned to this role
        $userCount = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM users WHERE role_id = ? AND deleted_at IS NULL",
            [$this->getId()]
        );

        if ($userCount['count'] > 0) {
            throw new \RuntimeException('Cannot delete role with assigned users.');
        }

        $this->db->delete('roles', 'id = ?', [$this->getId()]);
        return true;
    }

    /**
     * Get role permissions
     */
    public function getPermissions()
    {
        $permissions = $this->db->select(
            "SELECT p.* FROM permissions p 
             INNER JOIN role_permissions rp ON p.id = rp.permission_id 
             WHERE rp.role_id = ? 
             ORDER BY p.group, p.name",
            [$this->getId()]
        );

        return $permissions;
    }

    /**
     * Check if role has permission
     */
    public function hasPermission($permissionSlug)
    {
        $permission = $this->db->selectOne(
            "SELECT p.id FROM permissions p 
             INNER JOIN role_permissions rp ON p.id = rp.permission_id 
             WHERE rp.role_id = ? AND p.slug = ?",
            [$this->getId(), $permissionSlug]
        );

        return !empty($permission);
    }

    /**
     * Assign permission to role
     */
    public function assignPermission($permissionId)
    {
        // Check if already assigned
        $existing = $this->db->selectOne(
            "SELECT id FROM role_permissions WHERE role_id = ? AND permission_id = ?",
            [$this->getId(), $permissionId]
        );

        if (!$existing) {
            $this->db->insert('role_permissions', [
                'role_id' => $this->getId(),
                'permission_id' => $permissionId
            ]);
        }

        return $this;
    }

    /**
     * Remove permission from role
     */
    public function removePermission($permissionId)
    {
        $this->db->delete(
            'role_permissions',
            'role_id = ? AND permission_id = ?',
            [$this->getId(), $permissionId]
        );

        return $this;
    }

    /**
     * Sync permissions (replace all permissions with new ones)
     */
    public function syncPermissions($permissionIds)
    {
        // Remove all existing permissions
        $this->db->delete('role_permissions', 'role_id = ?', [$this->getId()]);

        // Add new permissions
        foreach ($permissionIds as $permissionId) {
            $this->assignPermission($permissionId);
        }

        return $this;
    }

    /**
     * Get users with this role
     */
    public function getUsers()
    {
        $usersData = $this->db->select(
            "SELECT * FROM users WHERE role_id = ? AND deleted_at IS NULL ORDER BY first_name, last_name",
            [$this->getId()]
        );

        $users = [];
        foreach ($usersData as $userData) {
            $user = new User();
            $user->data = $userData;
            $users[] = $user;
        }

        return $users;
    }

    /**
     * Get user count for this role
     */
    public function getUserCount()
    {
        $result = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM users WHERE role_id = ? AND deleted_at IS NULL",
            [$this->getId()]
        );

        return $result['count'] ?? 0;
    }

    // Getters
    public function getId() { return $this->data['id'] ?? null; }
    public function getName() { return $this->data['name'] ?? null; }
    public function getSlug() { return $this->data['slug'] ?? null; }
    public function getDescription() { return $this->data['description'] ?? null; }
    public function isDefault() { return (bool) ($this->data['is_default'] ?? false); }
    public function getCreatedAt() { return $this->data['created_at'] ?? null; }
    public function getUpdatedAt() { return $this->data['updated_at'] ?? null; }

    /**
     * Convert to array
     */
    public function toArray()
    {
        return $this->data;
    }

    /**
     * Magic getter
     */
    public function __get($key)
    {
        return $this->data[$key] ?? null;
    }

    /**
     * Magic isset
     */
    public function __isset($key)
    {
        return isset($this->data[$key]);
    }

    // Status constants
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    // System roles
    const ROLE_SUPER_ADMIN = 'super_admin';
    const ROLE_ADMIN = 'admin';
    const ROLE_EDITOR = 'editor';
    const ROLE_AUTHOR = 'author';
    const ROLE_SUBSCRIBER = 'subscriber';

    /**
     * Get all available statuses
     */
    public static function getStatuses()
    {
        return [
            self::STATUS_ACTIVE => __('Active'),
            self::STATUS_INACTIVE => __('Inactive')
        ];
    }

    /**
     * Get system roles
     */
    public static function getSystemRoles()
    {
        return [
            self::ROLE_SUPER_ADMIN => [
                'name' => __('Super Administrator'),
                'description' => __('Full system access with all permissions'),
                'level' => 100
            ],
            self::ROLE_ADMIN => [
                'name' => __('Administrator'),
                'description' => __('Administrative access to most features'),
                'level' => 80
            ],
            self::ROLE_EDITOR => [
                'name' => __('Editor'),
                'description' => __('Can create, edit, and publish content'),
                'level' => 60
            ],
            self::ROLE_AUTHOR => [
                'name' => __('Author'),
                'description' => __('Can create and edit own content'),
                'level' => 40
            ],
            self::ROLE_SUBSCRIBER => [
                'name' => __('Subscriber'),
                'description' => __('Basic user with limited access'),
                'level' => 20
            ]
        ];
    }

    /**
     * Check if role has permission
     */
    public function hasPermission($permissionSlug)
    {
        $permissions = $this->getPermissions();

        foreach ($permissions as $permission) {
            if ($permission['slug'] === $permissionSlug) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get role permissions
     */
    public function getPermissions()
    {
        if (!$this->id) return [];

        return $this->db->select(
            "SELECT p.* FROM permissions p
             INNER JOIN role_permissions rp ON p.id = rp.permission_id
             WHERE rp.role_id = ? AND p.status = 'active'
             ORDER BY p.category, p.name",
            [$this->id]
        );
    }

    /**
     * Assign permission to role
     */
    public function assignPermission($permissionId)
    {
        if (!$this->id) return false;

        // Check if permission already assigned
        $existing = $this->db->selectOne(
            "SELECT id FROM role_permissions WHERE role_id = ? AND permission_id = ?",
            [$this->id, $permissionId]
        );

        if ($existing) return true;

        return $this->db->insert('role_permissions', [
            'role_id' => $this->id,
            'permission_id' => $permissionId,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Remove permission from role
     */
    public function removePermission($permissionId)
    {
        if (!$this->id) return false;

        return $this->db->delete(
            "DELETE FROM role_permissions WHERE role_id = ? AND permission_id = ?",
            [$this->id, $permissionId]
        );
    }

    /**
     * Sync permissions for role
     */
    public function syncPermissions($permissionIds)
    {
        if (!$this->id) return false;

        // Remove all existing permissions
        $this->db->delete("DELETE FROM role_permissions WHERE role_id = ?", [$this->id]);

        // Add new permissions
        foreach ($permissionIds as $permissionId) {
            $this->assignPermission($permissionId);
        }

        return true;
    }

    /**
     * Get users count for this role
     */
    public function getUsersCount()
    {
        if (!$this->id) return 0;

        $result = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM user_roles WHERE role_id = ?",
            [$this->id]
        );

        return $result['count'] ?? 0;
    }

    /**
     * Check if role is system role
     */
    public function isSystem()
    {
        return (bool) ($this->is_system ?? false);
    }

    /**
     * Check if role can be deleted
     */
    public function canBeDeleted()
    {
        // System roles cannot be deleted
        if ($this->isSystem()) {
            return false;
        }

        // Roles with users cannot be deleted
        if ($this->getUsersCount() > 0) {
            return false;
        }

        return true;
    }

    /**
     * Get role level
     */
    public function getLevel()
    {
        return $this->level ?? 0;
    }

    /**
     * Create system roles if they don't exist
     */
    public static function createSystemRoles()
    {
        $instance = new static();
        $systemRoles = self::getSystemRoles();

        foreach ($systemRoles as $slug => $roleData) {
            // Check if role exists
            $existing = $instance->db->selectOne(
                "SELECT id FROM roles WHERE slug = ?",
                [$slug]
            );

            if (!$existing) {
                $instance->db->insert('roles', [
                    'name' => $roleData['name'],
                    'slug' => $slug,
                    'description' => $roleData['description'],
                    'level' => $roleData['level'],
                    'status' => self::STATUS_ACTIVE,
                    'is_system' => 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }
        }
    }
}
