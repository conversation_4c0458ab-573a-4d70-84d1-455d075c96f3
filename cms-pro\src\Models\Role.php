<?php

namespace CmsPro\Models;

use CmsPro\Core\Database;

/**
 * Role Model
 * 
 * @package CmsPro\Models
 */
class Role
{
    private $db;
    private $data = [];

    public function __construct(Database $db = null)
    {
        $this->db = $db ?: app()->getDatabase();
    }

    /**
     * Find role by ID
     */
    public static function find($id)
    {
        $instance = new static();
        $roleData = $instance->db->selectOne(
            "SELECT * FROM roles WHERE id = ?",
            [$id]
        );

        if ($roleData) {
            $instance->data = $roleData;
            return $instance;
        }

        return null;
    }

    /**
     * Find role by slug
     */
    public static function findBySlug($slug)
    {
        $instance = new static();
        $roleData = $instance->db->selectOne(
            "SELECT * FROM roles WHERE slug = ?",
            [$slug]
        );

        if ($roleData) {
            $instance->data = $roleData;
            return $instance;
        }

        return null;
    }

    /**
     * Get default role
     */
    public static function getDefault()
    {
        $instance = new static();
        $roleData = $instance->db->selectOne(
            "SELECT * FROM roles WHERE is_default = 1"
        );

        if ($roleData) {
            $instance->data = $roleData;
            return $instance;
        }

        return null;
    }

    /**
     * Get all roles
     */
    public static function all()
    {
        $instance = new static();
        $rolesData = $instance->db->select("SELECT * FROM roles ORDER BY name");
        
        $roles = [];
        foreach ($rolesData as $roleData) {
            $role = new static();
            $role->data = $roleData;
            $roles[] = $role;
        }
        
        return $roles;
    }

    /**
     * Create new role
     */
    public static function create($data)
    {
        $instance = new static();
        
        // Generate slug if not provided
        if (!isset($data['slug'])) {
            $data['slug'] = strtolower(str_replace(' ', '-', $data['name']));
        }

        $instance->db->insert('roles', $data);
        $roleId = $instance->db->lastInsertId();

        return static::find($roleId);
    }

    /**
     * Update role
     */
    public function update($data)
    {
        $this->db->update('roles', $data, 'id = ?', [$this->getId()]);
        
        // Refresh data
        $this->data = array_merge($this->data, $data);
        
        return $this;
    }

    /**
     * Delete role
     */
    public function delete()
    {
        // Don't allow deletion if users are assigned to this role
        $userCount = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM users WHERE role_id = ? AND deleted_at IS NULL",
            [$this->getId()]
        );

        if ($userCount['count'] > 0) {
            throw new \RuntimeException('Cannot delete role with assigned users.');
        }

        $this->db->delete('roles', 'id = ?', [$this->getId()]);
        return true;
    }

    /**
     * Get role permissions
     */
    public function getPermissions()
    {
        $permissions = $this->db->select(
            "SELECT p.* FROM permissions p 
             INNER JOIN role_permissions rp ON p.id = rp.permission_id 
             WHERE rp.role_id = ? 
             ORDER BY p.group, p.name",
            [$this->getId()]
        );

        return $permissions;
    }

    /**
     * Check if role has permission
     */
    public function hasPermission($permissionSlug)
    {
        $permission = $this->db->selectOne(
            "SELECT p.id FROM permissions p 
             INNER JOIN role_permissions rp ON p.id = rp.permission_id 
             WHERE rp.role_id = ? AND p.slug = ?",
            [$this->getId(), $permissionSlug]
        );

        return !empty($permission);
    }

    /**
     * Assign permission to role
     */
    public function assignPermission($permissionId)
    {
        // Check if already assigned
        $existing = $this->db->selectOne(
            "SELECT id FROM role_permissions WHERE role_id = ? AND permission_id = ?",
            [$this->getId(), $permissionId]
        );

        if (!$existing) {
            $this->db->insert('role_permissions', [
                'role_id' => $this->getId(),
                'permission_id' => $permissionId
            ]);
        }

        return $this;
    }

    /**
     * Remove permission from role
     */
    public function removePermission($permissionId)
    {
        $this->db->delete(
            'role_permissions',
            'role_id = ? AND permission_id = ?',
            [$this->getId(), $permissionId]
        );

        return $this;
    }

    /**
     * Sync permissions (replace all permissions with new ones)
     */
    public function syncPermissions($permissionIds)
    {
        // Remove all existing permissions
        $this->db->delete('role_permissions', 'role_id = ?', [$this->getId()]);

        // Add new permissions
        foreach ($permissionIds as $permissionId) {
            $this->assignPermission($permissionId);
        }

        return $this;
    }

    /**
     * Get users with this role
     */
    public function getUsers()
    {
        $usersData = $this->db->select(
            "SELECT * FROM users WHERE role_id = ? AND deleted_at IS NULL ORDER BY first_name, last_name",
            [$this->getId()]
        );

        $users = [];
        foreach ($usersData as $userData) {
            $user = new User();
            $user->data = $userData;
            $users[] = $user;
        }

        return $users;
    }

    /**
     * Get user count for this role
     */
    public function getUserCount()
    {
        $result = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM users WHERE role_id = ? AND deleted_at IS NULL",
            [$this->getId()]
        );

        return $result['count'] ?? 0;
    }

    // Getters
    public function getId() { return $this->data['id'] ?? null; }
    public function getName() { return $this->data['name'] ?? null; }
    public function getSlug() { return $this->data['slug'] ?? null; }
    public function getDescription() { return $this->data['description'] ?? null; }
    public function isDefault() { return (bool) ($this->data['is_default'] ?? false); }
    public function getCreatedAt() { return $this->data['created_at'] ?? null; }
    public function getUpdatedAt() { return $this->data['updated_at'] ?? null; }

    /**
     * Convert to array
     */
    public function toArray()
    {
        return $this->data;
    }

    /**
     * Magic getter
     */
    public function __get($key)
    {
        return $this->data[$key] ?? null;
    }

    /**
     * Magic isset
     */
    public function __isset($key)
    {
        return isset($this->data[$key]);
    }
}
