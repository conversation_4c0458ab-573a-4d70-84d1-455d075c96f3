{% extends "layouts/admin.twig" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="blog-management-container">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ title }}</h1>
            <p class="text-muted mb-0">{{ __('Manage your blog posts and content') }}</p>
        </div>
        <div class="btn-group">
            {% if auth().can('blog.create') %}
            <a href="{{ url('/admin/blog/create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>{{ __('New Post') }}
            </a>
            {% endif %}
            <div class="dropdown">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-cog me-2"></i>{{ __('Actions') }}
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{{ url('/admin/categories') }}">
                        <i class="fas fa-folder me-2"></i>{{ __('Manage Categories') }}
                    </a></li>
                    <li><a class="dropdown-item" href="{{ url('/admin/tags') }}">
                        <i class="fas fa-tags me-2"></i>{{ __('Manage Tags') }}
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" id="bulk-actions-btn">
                        <i class="fas fa-tasks me-2"></i>{{ __('Bulk Actions') }}
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon bg-primary">
                                <i class="fas fa-file-alt"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="stat-value">{{ posts|length }}</div>
                            <div class="stat-label">{{ __('Total Posts') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon bg-success">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="stat-value">{{ posts|filter(p => p.status == 'published')|length }}</div>
                            <div class="stat-label">{{ __('Published') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon bg-warning">
                                <i class="fas fa-edit"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="stat-value">{{ posts|filter(p => p.status == 'draft')|length }}</div>
                            <div class="stat-label">{{ __('Drafts') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon bg-info">
                                <i class="fas fa-eye"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="stat-value">{{ posts|map(p => p.views)|reduce((a, b) => a + b, 0) }}</div>
                            <div class="stat-label">{{ __('Total Views') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Filters Sidebar -->
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-filter me-2"></i>{{ __('Filters') }}
                    </h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ url('/admin/blog') }}" id="filters-form">
                        <!-- Search -->
                        <div class="mb-3">
                            <label for="search" class="form-label">{{ __('Search') }}</label>
                            <input type="text" class="form-control form-control-sm" id="search" name="search" 
                                   value="{{ current_filters.search }}" 
                                   placeholder="{{ __('Search posts...') }}">
                        </div>
                        
                        <!-- Status Filter -->
                        <div class="mb-3">
                            <label for="status" class="form-label">{{ __('Status') }}</label>
                            <select class="form-select form-select-sm" id="status" name="status">
                                <option value="all" {{ current_filters.status == 'all' ? 'selected' : '' }}>
                                    {{ __('All Status') }}
                                </option>
                                <option value="published" {{ current_filters.status == 'published' ? 'selected' : '' }}>
                                    {{ __('Published') }}
                                </option>
                                <option value="draft" {{ current_filters.status == 'draft' ? 'selected' : '' }}>
                                    {{ __('Draft') }}
                                </option>
                                <option value="scheduled" {{ current_filters.status == 'scheduled' ? 'selected' : '' }}>
                                    {{ __('Scheduled') }}
                                </option>
                                <option value="archived" {{ current_filters.status == 'archived' ? 'selected' : '' }}>
                                    {{ __('Archived') }}
                                </option>
                            </select>
                        </div>
                        
                        <!-- Category Filter -->
                        <div class="mb-3">
                            <label for="category" class="form-label">{{ __('Category') }}</label>
                            <select class="form-select form-select-sm" id="category" name="category">
                                <option value="" {{ current_filters.category == '' ? 'selected' : '' }}>
                                    {{ __('All Categories') }}
                                </option>
                                {% for category in categories %}
                                <option value="{{ category.id }}" {{ current_filters.category == category.id ? 'selected' : '' }}>
                                    {{ category.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <!-- Author Filter -->
                        <div class="mb-3">
                            <label for="author" class="form-label">{{ __('Author') }}</label>
                            <select class="form-select form-select-sm" id="author" name="author">
                                <option value="" {{ current_filters.author == '' ? 'selected' : '' }}>
                                    {{ __('All Authors') }}
                                </option>
                                {% for author in authors %}
                                <option value="{{ author.id }}" {{ current_filters.author == author.id ? 'selected' : '' }}>
                                    {{ author.first_name }} {{ author.last_name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <!-- Date Range -->
                        <div class="mb-3">
                            <label for="date_from" class="form-label">{{ __('Date From') }}</label>
                            <input type="date" class="form-control form-control-sm" id="date_from" name="date_from" 
                                   value="{{ current_filters.date_from }}">
                        </div>
                        
                        <div class="mb-3">
                            <label for="date_to" class="form-label">{{ __('Date To') }}</label>
                            <input type="date" class="form-control form-control-sm" id="date_to" name="date_to" 
                                   value="{{ current_filters.date_to }}">
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-search me-1"></i>{{ __('Apply Filters') }}
                            </button>
                            <a href="{{ url('/admin/blog') }}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times me-1"></i>{{ __('Clear') }}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Posts List -->
        <div class="col-md-9">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-list me-2"></i>{{ __('Blog Posts') }}
                    </h6>
                    
                    <!-- Bulk Actions -->
                    <div class="bulk-actions" style="display: none;">
                        <span class="selected-count me-2">0 {{ __('selected') }}</span>
                        {% if auth().can('blog.delete') %}
                        <button type="button" class="btn btn-outline-danger btn-sm" id="bulk-delete-btn">
                            <i class="fas fa-trash me-1"></i>{{ __('Delete') }}
                        </button>
                        {% endif %}
                    </div>
                </div>
                
                <div class="card-body p-0">
                    {% if posts and posts|length > 0 %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" class="form-check-input" id="select-all">
                                    </th>
                                    <th>{{ __('Title') }}</th>
                                    <th width="120">{{ __('Status') }}</th>
                                    <th width="150">{{ __('Category') }}</th>
                                    <th width="150">{{ __('Author') }}</th>
                                    <th width="100">{{ __('Views') }}</th>
                                    <th width="120">{{ __('Date') }}</th>
                                    <th width="120">{{ __('Actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for post in posts %}
                                <tr data-post-id="{{ post.id }}">
                                    <td>
                                        <input type="checkbox" class="form-check-input post-checkbox" value="{{ post.id }}">
                                    </td>
                                    <td>
                                        <div class="post-title-cell">
                                            <h6 class="mb-1">
                                                <a href="{{ url('/admin/blog/' ~ post.id ~ '/edit') }}" class="text-decoration-none">
                                                    {{ post.title }}
                                                </a>
                                            </h6>
                                            {% if post.excerpt %}
                                            <small class="text-muted">{{ post.excerpt|slice(0, 80) }}...</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ post.status == 'published' ? 'success' : (post.status == 'draft' ? 'warning' : 'secondary') }}">
                                            {{ __(post.status|title) }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if post.category_name %}
                                        <span class="badge bg-light text-dark">{{ post.category_name }}</span>
                                        {% else %}
                                        <span class="text-muted">{{ __('Uncategorized') }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ post.author_name }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ post.views }}</span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ post.created_at|date('M d, Y') }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            {% if auth().can('blog.edit') and (auth().can('blog.edit_all') or post.author_id == auth().id()) %}
                                            <a href="{{ url('/admin/blog/' ~ post.id ~ '/edit') }}" 
                                               class="btn btn-outline-primary btn-sm" title="{{ __('Edit') }}">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}
                                            
                                            <a href="{{ url('/' ~ post.slug) }}" target="_blank"
                                               class="btn btn-outline-info btn-sm" title="{{ __('View') }}">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            {% if auth().can('blog.delete') and (auth().can('blog.delete_all') or post.author_id == auth().id()) %}
                                            <button type="button" class="btn btn-outline-danger btn-sm delete-post-btn" 
                                                    data-post-id="{{ post.id }}" data-post-title="{{ post.title }}" 
                                                    title="{{ __('Delete') }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if pagination.pages > 1 %}
                    <div class="card-footer bg-white border-0">
                        <div class="d-flex justify-content-center">
                            <nav aria-label="Blog posts pagination">
                                <ul class="pagination mb-0">
                                    {% if pagination.current_page > 1 %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url('/admin/blog') }}?{{ http_build_query(current_filters|merge({page: pagination.current_page - 1})) }}">
                                            {{ __('Previous') }}
                                        </a>
                                    </li>
                                    {% endif %}
                                    
                                    {% for page in range(max(1, pagination.current_page - 2), min(pagination.pages, pagination.current_page + 2)) %}
                                    <li class="page-item {{ page == pagination.current_page ? 'active' : '' }}">
                                        <a class="page-link" href="{{ url('/admin/blog') }}?{{ http_build_query(current_filters|merge({page: page})) }}">
                                            {{ page }}
                                        </a>
                                    </li>
                                    {% endfor %}
                                    
                                    {% if pagination.current_page < pagination.pages %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url('/admin/blog') }}?{{ http_build_query(current_filters|merge({page: pagination.current_page + 1})) }}">
                                            {{ __('Next') }}
                                        </a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">{{ __('No blog posts found') }}</h5>
                        <p class="text-muted">
                            {% if current_filters.search or current_filters.status != 'all' or current_filters.category or current_filters.author %}
                                {{ __('Try adjusting your filters or') }}
                                <a href="{{ url('/admin/blog') }}">{{ __('view all posts') }}</a>
                            {% else %}
                                {{ __('Get started by creating your first blog post.') }}
                            {% endif %}
                        </p>
                        {% if auth().can('blog.create') %}
                        <a href="{{ url('/admin/blog/create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>{{ __('Create First Post') }}
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
