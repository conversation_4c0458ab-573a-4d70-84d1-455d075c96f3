<?php

namespace CmsPro\Services;

use CmsPro\Models\User;
use CmsPro\Core\Database;

/**
 * Activity Logger Service
 * 
 * @package CmsPro\Services
 */
class ActivityLogger
{
    private $db;

    public function __construct(Database $db = null)
    {
        $this->db = $db ?: app()->getDatabase();
    }

    /**
     * Log user activity
     */
    public function log($action, $description = null, $properties = [], User $user = null, $ipAddress = null, $userAgent = null)
    {
        // Get current user if not provided
        if (!$user && auth()->check()) {
            $user = auth()->user();
        }

        // Get request details if not provided
        if (!$ipAddress || !$userAgent) {
            $request = app()->getRequest();
            $ipAddress = $ipAddress ?: $request->getClientIp();
            $userAgent = $userAgent ?: $request->headers->get('User-Agent');
        }

        // Prepare data
        $data = [
            'user_id' => $user ? $user->getId() : null,
            'action' => $action,
            'description' => $description ?: $this->generateDescription($action),
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'properties' => json_encode($properties)
        ];

        // Insert log entry
        $this->db->insert('user_activity_log', $data);

        return true;
    }

    /**
     * Log authentication events
     */
    public function logAuth($action, $user = null, $properties = [])
    {
        $descriptions = [
            'login' => 'User logged in',
            'logout' => 'User logged out',
            'login_failed' => 'Failed login attempt',
            'password_reset_requested' => 'Password reset requested',
            'password_reset_completed' => 'Password reset completed',
            '2fa_enabled' => 'Two-factor authentication enabled',
            '2fa_disabled' => 'Two-factor authentication disabled',
            '2fa_verified' => 'Two-factor authentication verified',
            '2fa_failed' => 'Two-factor authentication failed',
            'account_locked' => 'Account locked due to failed attempts',
            'account_unlocked' => 'Account unlocked'
        ];

        $description = $descriptions[$action] ?? 'Authentication event';
        
        return $this->log($action, $description, $properties, $user);
    }

    /**
     * Log content management events
     */
    public function logContent($action, $contentType, $contentId, $properties = [])
    {
        $properties['content_type'] = $contentType;
        $properties['content_id'] = $contentId;

        $descriptions = [
            'content_created' => "Created {$contentType}",
            'content_updated' => "Updated {$contentType}",
            'content_deleted' => "Deleted {$contentType}",
            'content_published' => "Published {$contentType}",
            'content_unpublished' => "Unpublished {$contentType}",
            'content_viewed' => "Viewed {$contentType}"
        ];

        $description = $descriptions[$action] ?? "Content action: {$action}";
        
        return $this->log($action, $description, $properties);
    }

    /**
     * Log user management events
     */
    public function logUserManagement($action, $targetUserId, $properties = [])
    {
        $properties['target_user_id'] = $targetUserId;

        $descriptions = [
            'user_created' => 'Created new user',
            'user_updated' => 'Updated user information',
            'user_deleted' => 'Deleted user',
            'user_role_changed' => 'Changed user role',
            'user_activated' => 'Activated user account',
            'user_deactivated' => 'Deactivated user account',
            'user_permissions_changed' => 'Changed user permissions'
        ];

        $description = $descriptions[$action] ?? "User management action: {$action}";
        
        return $this->log($action, $description, $properties);
    }

    /**
     * Log system events
     */
    public function logSystem($action, $properties = [])
    {
        $descriptions = [
            'system_backup_created' => 'System backup created',
            'system_backup_restored' => 'System backup restored',
            'cache_cleared' => 'System cache cleared',
            'settings_updated' => 'System settings updated',
            'theme_changed' => 'Theme changed',
            'plugin_activated' => 'Plugin activated',
            'plugin_deactivated' => 'Plugin deactivated',
            'maintenance_mode_enabled' => 'Maintenance mode enabled',
            'maintenance_mode_disabled' => 'Maintenance mode disabled'
        ];

        $description = $descriptions[$action] ?? "System action: {$action}";
        
        return $this->log($action, $description, $properties);
    }

    /**
     * Log security events
     */
    public function logSecurity($action, $properties = [])
    {
        $descriptions = [
            'suspicious_activity' => 'Suspicious activity detected',
            'brute_force_attempt' => 'Brute force attack attempt',
            'sql_injection_attempt' => 'SQL injection attempt',
            'xss_attempt' => 'XSS attack attempt',
            'csrf_token_mismatch' => 'CSRF token mismatch',
            'unauthorized_access' => 'Unauthorized access attempt',
            'file_upload_blocked' => 'Malicious file upload blocked',
            'ip_blocked' => 'IP address blocked',
            'rate_limit_exceeded' => 'Rate limit exceeded'
        ];

        $description = $descriptions[$action] ?? "Security event: {$action}";
        
        return $this->log($action, $description, $properties);
    }

    /**
     * Get activity logs
     */
    public function getActivities($filters = [], $limit = 50, $offset = 0)
    {
        $where = [];
        $params = [];

        // Filter by user
        if (isset($filters['user_id'])) {
            $where[] = 'user_id = ?';
            $params[] = $filters['user_id'];
        }

        // Filter by action
        if (isset($filters['action'])) {
            $where[] = 'action = ?';
            $params[] = $filters['action'];
        }

        // Filter by date range
        if (isset($filters['date_from'])) {
            $where[] = 'created_at >= ?';
            $params[] = $filters['date_from'];
        }

        if (isset($filters['date_to'])) {
            $where[] = 'created_at <= ?';
            $params[] = $filters['date_to'];
        }

        // Filter by IP address
        if (isset($filters['ip_address'])) {
            $where[] = 'ip_address = ?';
            $params[] = $filters['ip_address'];
        }

        $whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';
        
        $sql = "SELECT ual.*, u.first_name, u.last_name, u.username, u.email 
                FROM user_activity_log ual 
                LEFT JOIN users u ON ual.user_id = u.id 
                {$whereClause}
                ORDER BY ual.created_at DESC 
                LIMIT ? OFFSET ?";
        
        $params[] = $limit;
        $params[] = $offset;

        return $this->db->select($sql, $params);
    }

    /**
     * Get activity statistics
     */
    public function getStatistics($period = '7 days')
    {
        $sql = "SELECT 
                    action,
                    COUNT(*) as count,
                    DATE(created_at) as date
                FROM user_activity_log 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL {$period})
                GROUP BY action, DATE(created_at)
                ORDER BY date DESC, count DESC";

        return $this->db->select($sql);
    }

    /**
     * Get top active users
     */
    public function getTopActiveUsers($period = '7 days', $limit = 10)
    {
        $sql = "SELECT 
                    u.id,
                    u.first_name,
                    u.last_name,
                    u.username,
                    u.email,
                    COUNT(ual.id) as activity_count
                FROM users u
                INNER JOIN user_activity_log ual ON u.id = ual.user_id
                WHERE ual.created_at >= DATE_SUB(NOW(), INTERVAL {$period})
                GROUP BY u.id
                ORDER BY activity_count DESC
                LIMIT ?";

        return $this->db->select($sql, [$limit]);
    }

    /**
     * Clean old logs
     */
    public function cleanOldLogs($days = 90)
    {
        $sql = "DELETE FROM user_activity_log WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
        return $this->db->execute($sql, [$days]);
    }

    /**
     * Generate description for action
     */
    private function generateDescription($action)
    {
        $descriptions = [
            'login' => 'User logged in',
            'logout' => 'User logged out',
            'create' => 'Created item',
            'update' => 'Updated item',
            'delete' => 'Deleted item',
            'view' => 'Viewed item',
            'upload' => 'Uploaded file',
            'download' => 'Downloaded file'
        ];

        return $descriptions[$action] ?? ucfirst(str_replace('_', ' ', $action));
    }
}
