# CMS Pro - Modern Content Management System

[![PHP Version](https://img.shields.io/badge/PHP-8.1%2B-blue.svg)](https://php.net)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)](https://github.com/your-repo/cms-pro)
[![Coverage](https://img.shields.io/badge/Coverage-85%25-yellow.svg)](tests/coverage)

CMS Pro is a modern, feature-rich Content Management System built with PHP 8.1+, designed for developers and content creators who need a powerful, flexible, and secure platform for managing digital content.

## 🚀 Features

### 🎯 Core Features
- **Modern MVC Architecture** - Clean, maintainable code structure
- **Comprehensive Admin Panel** - Intuitive interface for content management
- **Advanced User System** - Roles, permissions, and authentication
- **Media Management** - File uploads, image processing, galleries
- **SEO Optimization** - Meta tags, sitemaps, structured data

### 📝 Content Management
- **Dynamic Pages** - Custom fields, templates, versioning
- **Blog System** - Posts, categories, tags, comments
- **Content Search** - Full-text search with highlighting
- **Analytics** - Performance metrics, engagement tracking

### 🎨 Frontend Features
- **Responsive Design** - Mobile-first, modern UI
- **Performance Optimized** - Caching, lazy loading, compression
- **Social Integration** - Open Graph, Twitter Cards, sharing
- **Accessibility** - WCAG compliant, keyboard navigation

### 🔧 Advanced Features
- **Settings Management** - Configurable system settings
- **Activity Logging** - Comprehensive audit trail
- **Cache System** - Multi-layer caching for performance
- **Security** - CSRF protection, input sanitization, XSS prevention

## 📋 Requirements

- **PHP 8.1+** with extensions:
  - PDO (MySQL/SQLite)
  - GD or Imagick
  - cURL
  - mbstring
  - OpenSSL
- **MySQL 5.7+** or **SQLite 3.8+**
- **Composer** for dependency management
- **Web Server** (Apache/Nginx)

## 🛠️ Installation

### Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-repo/cms-pro.git
   cd cms-pro
   ```

2. **Install dependencies**
   ```bash
   composer install
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your database and site settings
   ```

4. **Set up database**
   ```bash
   php artisan migrate
   php artisan seed
   ```

5. **Configure web server**
   - Point document root to `public/` directory
   - Enable URL rewriting

6. **Access your site**
   - Frontend: `http://your-domain.com`
   - Admin: `http://your-domain.com/admin`
   - Default login: `<EMAIL>` / `password`

### Docker Installation

```bash
# Clone and start with Docker
git clone https://github.com/your-repo/cms-pro.git
cd cms-pro
docker-compose up -d

# Access at http://localhost:8080
```

## 📖 Documentation

### User Guide
- [Getting Started](docs/user-guide/getting-started.md)
- [Content Management](docs/user-guide/content-management.md)
- [Media Management](docs/user-guide/media-management.md)
- [User Management](docs/user-guide/user-management.md)
- [Settings](docs/user-guide/settings.md)

### Developer Guide
- [Architecture](docs/developer-guide/architecture.md)
- [API Reference](docs/developer-guide/api-reference.md)
- [Custom Fields](docs/developer-guide/custom-fields.md)
- [Themes](docs/developer-guide/themes.md)
- [Plugins](docs/developer-guide/plugins.md)

### Deployment
- [Server Requirements](docs/deployment/server-requirements.md)
- [Production Setup](docs/deployment/production-setup.md)
- [Security Guide](docs/deployment/security-guide.md)
- [Performance Optimization](docs/deployment/performance-optimization.md)

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
./vendor/bin/phpunit

# Run specific test suite
./vendor/bin/phpunit --testsuite=Unit
./vendor/bin/phpunit --testsuite=Feature

# Generate coverage report
./vendor/bin/phpunit --coverage-html tests/coverage
```

## 🔧 Configuration

### Environment Variables

```env
# Application
APP_NAME="CMS Pro"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# Database
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=cms_pro
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Cache
CACHE_DRIVER=file
CACHE_PREFIX=cms_pro

# Mail
MAIL_DRIVER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=your_email
MAIL_PASSWORD=your_password
```

### Web Server Configuration

#### Apache (.htaccess)
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]
```

#### Nginx
```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
}

location ~ \.php$ {
    fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
    fastcgi_index index.php;
    fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
    include fastcgi_params;
}
```

## 🎨 Customization

### Creating Custom Themes

```php
// themes/my-theme/functions.php
<?php

// Register theme
add_theme_support('custom-header');
add_theme_support('custom-background');

// Enqueue styles
function my_theme_styles() {
    wp_enqueue_style('my-theme-style', get_template_directory_uri() . '/style.css');
}
add_action('wp_enqueue_scripts', 'my_theme_styles');
```

### Custom Fields

```php
// Add custom field type
class CustomFieldType extends FieldType {
    public function render($value, $field) {
        return '<input type="text" name="' . $field['name'] . '" value="' . $value . '">';
    }
}

// Register field type
FieldManager::register('custom_field', CustomFieldType::class);
```

## 🔌 API Reference

### Authentication

```bash
# Login
POST /api/v1/auth/login
{
    "email": "<EMAIL>",
    "password": "password"
}

# Get user profile
GET /api/v1/user
Authorization: Bearer {token}
```

### Content Management

```bash
# Get pages
GET /api/v1/pages

# Create page
POST /api/v1/pages
{
    "title": "New Page",
    "content": "<p>Page content</p>",
    "status": "published"
}

# Update page
PUT /api/v1/pages/{id}

# Delete page
DELETE /api/v1/pages/{id}
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes
4. Run tests: `./vendor/bin/phpunit`
5. Commit changes: `git commit -m 'Add amazing feature'`
6. Push to branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

### Code Style

We follow PSR-12 coding standards. Run PHP CS Fixer:

```bash
./vendor/bin/php-cs-fixer fix
```

## 🐛 Bug Reports

Found a bug? Please open an issue with:
- PHP version
- Steps to reproduce
- Expected vs actual behavior
- Error messages/logs

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Symfony Components](https://symfony.com/components)
- [Twig Template Engine](https://twig.symfony.com/)
- [Bootstrap CSS Framework](https://getbootstrap.com/)
- [Font Awesome Icons](https://fontawesome.com/)

## 📞 Support

- **Documentation**: [docs.cms-pro.com](https://docs.cms-pro.com)
- **Community Forum**: [forum.cms-pro.com](https://forum.cms-pro.com)
- **Email Support**: <EMAIL>
- **Discord**: [Join our Discord](https://discord.gg/cms-pro)

---

**Made with ❤️ by the CMS Pro Team**
