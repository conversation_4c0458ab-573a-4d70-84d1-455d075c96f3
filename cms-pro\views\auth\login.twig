{% extends "layouts/base.twig" %}

{% block title %}{{ __('Login') }} - {{ app.name }}{% endblock %}

{% block body_class %}auth-page{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-lg border-0 mt-5">
                <div class="card-header bg-primary text-white text-center py-4">
                    <h3 class="mb-0">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        {{ __('Login') }}
                    </h3>
                </div>
                <div class="card-body p-4">
                    <form method="POST" action="{{ url('/login') }}" class="needs-validation" novalidate>
                        {{ csrf_field() | raw }}
                        
                        <div class="mb-3">
                            <label for="login" class="form-label">{{ __('Email or Username') }}</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input 
                                    type="text" 
                                    class="form-control" 
                                    id="login" 
                                    name="login" 
                                    value="{{ old('login') }}"
                                    required 
                                    autofocus
                                    placeholder="{{ __('Enter your email or username') }}"
                                >
                                <div class="invalid-feedback">
                                    {{ __('Please enter your email or username.') }}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">{{ __('Password') }}</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input 
                                    type="password" 
                                    class="form-control" 
                                    id="password" 
                                    name="password" 
                                    required
                                    placeholder="{{ __('Enter your password') }}"
                                >
                                <button 
                                    type="button" 
                                    class="btn btn-outline-secondary" 
                                    onclick="togglePassword('password')"
                                >
                                    <i class="fas fa-eye" id="password-toggle-icon"></i>
                                </button>
                                <div class="invalid-feedback">
                                    {{ __('Please enter your password.') }}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3 form-check">
                            <input 
                                type="checkbox" 
                                class="form-check-input" 
                                id="remember" 
                                name="remember" 
                                value="1"
                            >
                            <label class="form-check-label" for="remember">
                                {{ __('Remember me') }}
                            </label>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                {{ __('Login') }}
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center py-3">
                    <div class="row">
                        <div class="col">
                            <a href="{{ url('/password/reset') }}" class="text-decoration-none">
                                <i class="fas fa-key me-1"></i>
                                {{ __('Forgot Password?') }}
                            </a>
                        </div>
                        {% if config('auth.registration_enabled', true) %}
                        <div class="col">
                            <a href="{{ url('/register') }}" class="text-decoration-none">
                                <i class="fas fa-user-plus me-1"></i>
                                {{ __('Register') }}
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Demo Credentials (only in development) -->
            {% if app.debug %}
            <div class="card mt-3 border-warning">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ __('Demo Credentials') }}
                    </h6>
                </div>
                <div class="card-body py-2">
                    <small class="text-muted">
                        <strong>{{ __('Admin:') }}</strong> <EMAIL> / password<br>
                        <strong>{{ __('Editor:') }}</strong> <EMAIL> / password
                    </small>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '-toggle-icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Auto-focus on first empty field
document.addEventListener('DOMContentLoaded', function() {
    const loginField = document.getElementById('login');
    const passwordField = document.getElementById('password');
    
    if (loginField.value === '') {
        loginField.focus();
    } else {
        passwordField.focus();
    }
});
</script>
{% endblock %}

{% block styles %}
{{ parent() }}
<style>
.auth-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.auth-page .card {
    border-radius: 15px;
    overflow: hidden;
}

.auth-page .card-header {
    background: linear-gradient(135deg, var(--bs-primary) 0%, #0056b3 100%) !important;
}

.auth-page .input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
}

.auth-page .form-control:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.auth-page .btn-primary {
    background: linear-gradient(135deg, var(--bs-primary) 0%, #0056b3 100%);
    border: none;
    font-weight: 600;
}

.auth-page .btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-1px);
}

.auth-page .card-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

.auth-page .card-footer a {
    color: var(--bs-primary);
    font-weight: 500;
}

.auth-page .card-footer a:hover {
    color: #0056b3;
    text-decoration: underline !important;
}
</style>
{% endblock %}
