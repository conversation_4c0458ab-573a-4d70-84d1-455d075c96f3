{% extends "layouts/base.twig" %}

{% block title %}{{ __('Login') }} - {{ app.name }}{% endblock %}

{% block body_class %}auth-page{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-lg border-0 mt-5">
                <div class="card-header bg-primary text-white text-center py-4">
                    <h3 class="mb-0">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        {{ __('Login') }}
                    </h3>
                </div>
                <div class="card-body p-4">
                    <form method="POST" action="{{ url('/login') }}" class="needs-validation login-form" novalidate>
                        {{ csrf_field() | raw }}

                        {# Security notice #}
                        <div class="alert alert-info alert-sm mb-3">
                            <i class="fas fa-shield-alt me-2"></i>
                            <small>{{ __('Your login is protected by advanced security measures.') }}</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="login" class="form-label">{{ __('Email or Username') }}</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input 
                                    type="text" 
                                    class="form-control" 
                                    id="login" 
                                    name="login" 
                                    value="{{ old('login') }}"
                                    required 
                                    autofocus
                                    placeholder="{{ __('Enter your email or username') }}"
                                >
                                <div class="invalid-feedback">
                                    {{ __('Please enter your email or username.') }}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">{{ __('Password') }}</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input 
                                    type="password" 
                                    class="form-control" 
                                    id="password" 
                                    name="password" 
                                    required
                                    placeholder="{{ __('Enter your password') }}"
                                >
                                <button 
                                    type="button" 
                                    class="btn btn-outline-secondary" 
                                    onclick="togglePassword('password')"
                                >
                                    <i class="fas fa-eye" id="password-toggle-icon"></i>
                                </button>
                                <div class="invalid-feedback">
                                    {{ __('Please enter your password.') }}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3 form-check">
                            <input 
                                type="checkbox" 
                                class="form-check-input" 
                                id="remember" 
                                name="remember" 
                                value="1"
                            >
                            <label class="form-check-label" for="remember">
                                {{ __('Remember me') }}
                            </label>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                {{ __('Login') }}
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center py-3">
                    <div class="row">
                        <div class="col">
                            <a href="{{ url('/password/reset') }}" class="text-decoration-none">
                                <i class="fas fa-key me-1"></i>
                                {{ __('Forgot Password?') }}
                            </a>
                        </div>
                        {% if config('auth.registration_enabled', true) %}
                        <div class="col">
                            <a href="{{ url('/register') }}" class="text-decoration-none">
                                <i class="fas fa-user-plus me-1"></i>
                                {{ __('Register') }}
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Demo Credentials (only in development) -->
            {% if app.debug %}
            <div class="card mt-3 border-warning">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ __('Demo Credentials') }}
                    </h6>
                </div>
                <div class="card-body py-2">
                    <small class="text-muted">
                        <strong>{{ __('Admin:') }}</strong> <EMAIL> / password<br>
                        <strong>{{ __('Editor:') }}</strong> <EMAIL> / password
                    </small>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
// Secure Login Form Handler
class SecureLoginForm {
    constructor() {
        this.form = document.querySelector('.login-form');
        this.submitButton = this.form.querySelector('button[type="submit"]');
        this.loginField = document.getElementById('login');
        this.passwordField = document.getElementById('password');
        this.attemptCount = 0;
        this.maxAttempts = 5;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupValidation();
        this.setupSecurityFeatures();
        this.autoFocus();
    }

    setupEventListeners() {
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        this.loginField.addEventListener('input', (e) => this.sanitizeInput(e));
        this.passwordField.addEventListener('paste', (e) => this.handlePasswordPaste(e));
    }

    setupValidation() {
        // Real-time validation
        this.loginField.addEventListener('blur', () => {
            this.validateLogin();
        });

        this.passwordField.addEventListener('blur', () => {
            this.validatePassword();
        });
    }

    setupSecurityFeatures() {
        // Prevent multiple rapid submissions
        this.form.addEventListener('submit', () => {
            this.submitButton.disabled = true;
            setTimeout(() => {
                this.submitButton.disabled = false;
            }, 2000);
        });

        // Monitor for suspicious activity
        this.monitorSuspiciousActivity();
    }

    handleSubmit(e) {
        e.preventDefault();

        if (!this.validateForm()) {
            return false;
        }

        if (this.attemptCount >= this.maxAttempts) {
            this.showError('Too many failed attempts. Please refresh the page.');
            return false;
        }

        this.showLoading();
        this.submitForm();
    }

    async submitForm() {
        try {
            const formData = new FormData(this.form);

            const response = await fetch(this.form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content || formData.get('_token')
                }
            });

            const result = await response.json();

            if (result.success) {
                if (result.requires_2fa) {
                    window.location.href = result.redirect;
                } else {
                    this.showSuccess('Login successful! Redirecting...');
                    setTimeout(() => {
                        window.location.href = result.redirect;
                    }, 1000);
                }
            } else {
                this.attemptCount++;
                this.showError(result.message || 'Login failed. Please try again.');
                this.hideLoading();
            }

        } catch (error) {
            this.attemptCount++;
            this.showError('Network error. Please check your connection and try again.');
            this.hideLoading();
        }
    }

    validateForm() {
        let isValid = true;

        if (!this.validateLogin()) isValid = false;
        if (!this.validatePassword()) isValid = false;

        return isValid;
    }

    validateLogin() {
        const value = this.loginField.value.trim();

        if (!value) {
            this.showFieldError(this.loginField, 'Email or username is required.');
            return false;
        }

        if (value.length > 255) {
            this.showFieldError(this.loginField, 'Input too long.');
            return false;
        }

        // Check for suspicious patterns
        if (this.containsSuspiciousPatterns(value)) {
            this.showFieldError(this.loginField, 'Invalid characters detected.');
            return false;
        }

        this.clearFieldError(this.loginField);
        return true;
    }

    validatePassword() {
        const value = this.passwordField.value;

        if (!value) {
            this.showFieldError(this.passwordField, 'Password is required.');
            return false;
        }

        if (value.length < 6) {
            this.showFieldError(this.passwordField, 'Password must be at least 6 characters.');
            return false;
        }

        if (value.length > 255) {
            this.showFieldError(this.passwordField, 'Password too long.');
            return false;
        }

        this.clearFieldError(this.passwordField);
        return true;
    }

    sanitizeInput(e) {
        const field = e.target;
        let value = field.value;

        // Remove dangerous characters
        value = value.replace(/[<>\"'\\]/g, '');

        if (value !== field.value) {
            field.value = value;
            this.showWarning('Some characters were removed for security.');
        }
    }

    handlePasswordPaste(e) {
        // Allow paste but validate content
        setTimeout(() => {
            const value = this.passwordField.value;
            if (value.length > 255) {
                this.passwordField.value = value.substring(0, 255);
                this.showWarning('Password was truncated to maximum length.');
            }
        }, 10);
    }

    containsSuspiciousPatterns(input) {
        const patterns = [
            /<script[^>]*>/i,
            /javascript:/i,
            /vbscript:/i,
            /on\w+\s*=/i,
            /(\bor\b|\band\b).*['"]/i,
            /union.*select/i,
            /drop.*table/i
        ];

        return patterns.some(pattern => pattern.test(input));
    }

    monitorSuspiciousActivity() {
        let keystrokes = 0;
        let rapidClicks = 0;

        document.addEventListener('keydown', () => {
            keystrokes++;
            if (keystrokes > 1000) { // Unusual activity
                this.logSuspiciousActivity('excessive_keystrokes');
            }
        });

        this.form.addEventListener('click', () => {
            rapidClicks++;
            setTimeout(() => rapidClicks--, 1000);

            if (rapidClicks > 10) {
                this.logSuspiciousActivity('rapid_clicking');
            }
        });
    }

    logSuspiciousActivity(type) {
        fetch('/api/security/log', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
            },
            body: JSON.stringify({
                type: type,
                timestamp: new Date().toISOString(),
                url: window.location.href
            })
        }).catch(() => {}); // Fail silently
    }

    showLoading() {
        this.submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Logging in...';
        this.submitButton.disabled = true;
    }

    hideLoading() {
        this.submitButton.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>Login';
        this.submitButton.disabled = false;
    }

    showError(message) {
        this.showAlert(message, 'danger');
    }

    showSuccess(message) {
        this.showAlert(message, 'success');
    }

    showWarning(message) {
        this.showAlert(message, 'warning');
    }

    showAlert(message, type) {
        const existingAlert = this.form.querySelector('.alert-dismissible');
        if (existingAlert) {
            existingAlert.remove();
        }

        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        this.form.insertBefore(alert, this.form.firstChild);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }

    showFieldError(field, message) {
        field.classList.add('is-invalid');
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.textContent = message;
        }
    }

    clearFieldError(field) {
        field.classList.remove('is-invalid');
    }

    autoFocus() {
        if (this.loginField.value === '') {
            this.loginField.focus();
        } else {
            this.passwordField.focus();
        }
    }
}

function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '-toggle-icon');

    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Initialize secure login form
document.addEventListener('DOMContentLoaded', function() {
    new SecureLoginForm();
});
</script>
{% endblock %}

{% block styles %}
{{ parent() }}
<style>
.auth-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.auth-page .card {
    border-radius: 15px;
    overflow: hidden;
}

.auth-page .card-header {
    background: linear-gradient(135deg, var(--bs-primary) 0%, #0056b3 100%) !important;
}

.auth-page .input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
}

.auth-page .form-control:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.auth-page .btn-primary {
    background: linear-gradient(135deg, var(--bs-primary) 0%, #0056b3 100%);
    border: none;
    font-weight: 600;
}

.auth-page .btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-1px);
}

.auth-page .card-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

.auth-page .card-footer a {
    color: var(--bs-primary);
    font-weight: 500;
}

.auth-page .card-footer a:hover {
    color: #0056b3;
    text-decoration: underline !important;
}
</style>
{% endblock %}
