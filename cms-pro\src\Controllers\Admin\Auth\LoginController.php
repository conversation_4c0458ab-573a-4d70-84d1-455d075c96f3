<?php

namespace CmsPro\Controllers\Admin\Auth;

use CmsPro\Controllers\BaseController;
use CmsPro\Core\Auth;
use Symfony\Component\HttpFoundation\Request;

/**
 * Admin Login Controller
 * 
 * @package CmsPro\Controllers\Admin\Auth
 */
class LoginController extends BaseController
{
    private $auth;

    public function __construct()
    {
        parent::__construct();
        $this->auth = new Auth();
    }

    /**
     * Show admin login form
     */
    public function showLoginForm(Request $request)
    {
        $this->request = $request;

        // Redirect if already authenticated and has admin access
        if ($this->auth->check() && $this->auth->can('admin.access')) {
            return $this->redirectToRoute('admin.dashboard');
        }

        $data = [
            'title' => __('Admin Login'),
            'meta_description' => __('Login to admin panel'),
        ];

        return $this->view('admin/auth/login.twig', $data);
    }

    /**
     * Handle admin login attempt
     */
    public function login(Request $request)
    {
        $this->request = $request;

        // Redirect if already authenticated and has admin access
        if ($this->auth->check() && $this->auth->can('admin.access')) {
            return $this->redirectToRoute('admin.dashboard');
        }

        // Validate input
        $credentials = $this->validate($request, [
            'login' => 'required',
            'password' => 'required'
        ]);

        $remember = $request->request->get('remember', false);

        try {
            // Attempt login
            if ($this->auth->attempt([
                'email' => $credentials['login'],
                'username' => $credentials['login'],
                'password' => $credentials['password']
            ], $remember)) {
                
                // Check if user has admin access
                if (!$this->auth->can('admin.access')) {
                    $this->auth->logout();
                    $this->flashError(__('You do not have permission to access the admin panel.'));
                    return $this->back();
                }
                
                $this->flashSuccess(__('Welcome to the admin panel!'));
                
                // Redirect to admin dashboard
                return $this->redirectToRoute('admin.dashboard');
            } else {
                $this->flashError(__('Invalid credentials. Please try again.'));
            }
        } catch (\Exception $e) {
            $this->flashError($e->getMessage());
        }

        // Flash input for form repopulation
        session()->flashInput($request->request->all());

        return $this->back();
    }

    /**
     * Handle admin logout
     */
    public function logout(Request $request)
    {
        $this->request = $request;

        $this->auth->logout();
        $this->flashSuccess(__('You have been logged out from admin panel.'));

        return $this->redirectToRoute('admin.login');
    }
}
